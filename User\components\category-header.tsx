"use client"

import { motion } from "framer-motion"
import Image from "next/image"

interface CategoryHeaderProps {
  title: string
  description: string
  image: string
}

export default function CategoryHeader({ title, description, image }: CategoryHeaderProps) {
  return (
    <div className="relative h-[300px] md:h-[400px] overflow-hidden">
      <div className="absolute inset-0">
        <Image src={image || "/placeholder.svg"} alt={title} fill className="object-cover" priority />
        <div className="absolute inset-0 bg-gradient-to-r from-black/70 to-transparent" />
      </div>

      <div className="relative container mx-auto px-4 h-full flex flex-col justify-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="max-w-xl"
        >
          <h1 className="text-3xl md:text-4xl lg:text-5xl font-medium text-white mb-4">{title}</h1>
          <p className="text-lg text-white/80">{description}</p>
        </motion.div>
      </div>
    </div>
  )
}
