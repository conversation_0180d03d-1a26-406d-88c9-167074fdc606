// Migration script to fix localStorage token key mismatch
// Run this in the browser console to migrate from uppercase to lowercase keys

function migrateTokenKeys() {
  console.log("🔄 Starting localStorage token key migration...");
  
  // Check current localStorage state
  const legacyToken = localStorage.getItem("SHOPIFY_CUSTOMER_ACCESS_TOKEN");
  const legacyExpires = localStorage.getItem("SHOPIFY_CUSTOMER_ACCESS_TOKEN_EXPIRES");
  const currentToken = localStorage.getItem("shopify_customer_access_token");
  const currentExpires = localStorage.getItem("shopify_customer_access_token_expires");
  
  console.log("📦 Current localStorage state:");
  console.log("  SHOPIFY_CUSTOMER_ACCESS_TOKEN (legacy):", legacyToken ? "✅ EXISTS" : "❌ NOT FOUND");
  console.log("  SHOPIFY_CUSTOMER_ACCESS_TOKEN_EXPIRES (legacy):", legacyExpires ? "✅ EXISTS" : "❌ NOT FOUND");
  console.log("  shopify_customer_access_token (current):", currentToken ? "✅ EXISTS" : "❌ NOT FOUND");
  console.log("  shopify_customer_access_token_expires (current):", currentExpires ? "✅ EXISTS" : "❌ NOT FOUND");
  
  let migrationPerformed = false;
  
  // Migrate token if needed
  if (legacyToken && !currentToken) {
    console.log("🔄 Migrating access token from legacy key...");
    localStorage.setItem("shopify_customer_access_token", legacyToken);
    localStorage.removeItem("SHOPIFY_CUSTOMER_ACCESS_TOKEN");
    migrationPerformed = true;
    console.log("✅ Access token migrated successfully");
  }
  
  // Migrate expiration if needed
  if (legacyExpires && !currentExpires) {
    console.log("🔄 Migrating token expiration from legacy key...");
    localStorage.setItem("shopify_customer_access_token_expires", legacyExpires);
    localStorage.removeItem("SHOPIFY_CUSTOMER_ACCESS_TOKEN_EXPIRES");
    migrationPerformed = true;
    console.log("✅ Token expiration migrated successfully");
  }
  
  // Check for other potential legacy keys
  const otherLegacyKeys = [];
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key && key.startsWith("SHOPIFY_")) {
      otherLegacyKeys.push(key);
    }
  }
  
  if (otherLegacyKeys.length > 0) {
    console.log("⚠️ Found other potential legacy keys:", otherLegacyKeys);
  }
  
  if (migrationPerformed) {
    console.log("🎉 Migration completed! Please refresh the page to see the changes.");
    
    // Verify migration
    const newToken = localStorage.getItem("shopify_customer_access_token");
    const newExpires = localStorage.getItem("shopify_customer_access_token_expires");
    
    console.log("✅ Verification:");
    console.log("  shopify_customer_access_token:", newToken ? "✅ EXISTS" : "❌ MISSING");
    console.log("  shopify_customer_access_token_expires:", newExpires ? "✅ EXISTS" : "❌ MISSING");
    
    // Trigger a page refresh to apply changes
    if (confirm("Migration completed! Would you like to refresh the page to apply changes?")) {
      window.location.reload();
    }
  } else {
    console.log("ℹ️ No migration needed. Keys are already in the correct format.");
  }
  
  return {
    migrationPerformed,
    hasLegacyToken: !!legacyToken,
    hasCurrentToken: !!currentToken,
    otherLegacyKeys
  };
}

// Auto-run the migration
console.log("🚀 Running automatic token key migration...");
migrateTokenKeys();
