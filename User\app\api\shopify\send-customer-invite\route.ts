import { NextRequest, NextResponse } from 'next/server'
import { getShopifyConfig } from '@/lib/env-validation'

// API to send customer invitation email explicitly
export async function POST(request: NextRequest) {
  let shopifyConfig;
  
  try {
    shopifyConfig = getShopifyConfig(true); // Need admin access
    console.log('🔧 Shopify configuration loaded for customer invitation');
  } catch (error) {
    console.error("Shopify environment variables are not properly configured:", error);
    return NextResponse.json(
      { error: 'Server configuration error' },
      { status: 500 }
    )
  }

  try {
    const {
      email,
      firstName,
      lastName,
      phone,
      password,
      acceptsMarketing = false,
      acceptsSmsMarketing = false
    } = await request.json()

    if (!email || !firstName || !lastName || !password) {
      return NextResponse.json(
        { error: 'Email, firstName, lastName, and password are required' },
        { status: 400 }
      )
    }

    console.log('Creating customer with invitation:', {
      email,
      firstName,
      lastName,
      phone: phone ? 'provided' : 'missing',
      acceptsMarketing,
      acceptsSmsMarketing
    })

    // Create customer payload
    const customerPayload = {
      customer: {
        email,
        first_name: firstName,
        last_name: lastName,
        phone: phone || '',
        accepts_marketing: acceptsMarketing, // Apply email marketing consent
        accepts_sms_marketing: acceptsSmsMarketing, // Apply SMS marketing consent
        verified_email: true, // Mark email as verified
        password: password, // Set password to activate account immediately
        password_confirmation: password,
        send_email_invite: false, // Don't send invitation email
        send_email_welcome: true // Send welcome email instead
      }
    }

    console.log('Customer payload being sent to Shopify:', JSON.stringify(customerPayload, null, 2))

    // Create customer with password and send welcome email - this will automatically send the "Customer Account Welcome" email
    const createResponse = await fetch(`https://${shopifyConfig.storeDomain}/admin/api/${shopifyConfig.apiVersion}/customers.json`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': shopifyConfig.adminAccessToken!
      },
      body: JSON.stringify(customerPayload)
    })

    const createResult = await createResponse.json()
    console.log('Customer creation result:', JSON.stringify(createResult, null, 2))

    // Log marketing consent specifically
    if (createResult.customer) {
      console.log('Customer marketing consent in response:', {
        accepts_marketing: createResult.customer.accepts_marketing,
        accepts_sms_marketing: createResult.customer.accepts_sms_marketing,
        marketing_opt_in_level: createResult.customer.marketing_opt_in_level,
        sms_marketing_consent: createResult.customer.sms_marketing_consent
      })
    }

    if (!createResult.customer) {
      const errors = createResult.errors || {}

      if (errors.email && errors.email.includes('has already been taken')) {
        return NextResponse.json({
          success: false,
          error: 'CUSTOMER_EXISTS',
          message: 'An account with this email already exists. Please sign in instead.',
          details: errors
        }, { status: 400 })
      }

      return NextResponse.json({
        success: false,
        error: 'CREATION_FAILED',
        message: 'Failed to create customer account. Please try again.',
        details: errors
      }, { status: 400 })
    }

    const customer = createResult.customer
    console.log('Customer created successfully with welcome email:', customer.id)
    console.log('Customer GraphQL ID:', customer.admin_graphql_api_id)

    // Customer created successfully and welcome email should have been sent automatically
    // Return the customer object with the correct GraphQL ID for metafield operations
    return NextResponse.json({
      success: true,
      customer: {
        ...customer,
        id: customer.admin_graphql_api_id // Use GraphQL ID instead of numeric ID
      },
      message: 'Account created successfully! Your account is now active. Please check your email for welcome instructions and you can now log in.'
    })

  } catch (error) {
    console.error('Customer invitation API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
