import type { Metadata } from "next"
import PageHeader from "@/components/page-header"
import ContactForm from "@/components/contact-form"
import ContactInfo from "@/components/contact-info"
import Faq from "@/components/faq"

export const metadata: Metadata = {
  title: "Contact Us | Benzochem Industries",
  description: "Get in touch with Benzochem Industries for inquiries, quotes, or support.",
}

export default function ContactPage() {
  return (
    <main className="flex min-h-screen flex-col pt-20">
      <PageHeader
        title="Contact Us"
        description="Get in touch with our team for inquiries, quotes, or technical support"
      />

      <section className="py-16 md:py-24">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <ContactForm />
            <ContactInfo />
          </div>
        </div>
      </section>

      <section className="py-16 md:py-24 bg-neutral-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-medium text-center mb-4">Frequently Asked Questions</h2>
          <p className="text-neutral-600 text-center max-w-3xl mx-auto mb-16">
            Find answers to common questions about our products, ordering process, and technical support.
          </p>

          <div className="max-w-3xl mx-auto">
            <Faq
              items={[
                {
                  question: "What is the minimum order quantity?",
                  answer:
                    "Our standard minimum order quantity varies by product. For most powder products, the minimum is 25kg, and for liquid products, it's 10L. For smaller quantities, please contact our customer service team.",
                },
                {
                  question: "Do you provide certificates of analysis?",
                  answer:
                    "Yes, we provide a Certificate of Analysis (CoA) with every shipment. The CoA includes detailed information about the product's specifications, batch number, and test results.",
                },
                {
                  question: "What are your shipping options?",
                  answer:
                    "We offer various shipping options including standard ground shipping, expedited shipping, and international shipping. The available options depend on your location and the products being ordered.",
                },
                {
                  question: "Can you provide custom formulations?",
                  answer:
                    "Yes, we offer custom formulation services for clients with specific requirements. Our R&D team can work with you to develop products tailored to your exact specifications.",
                },
                {
                  question: "What payment methods do you accept?",
                  answer:
                    "We accept various payment methods including credit cards, wire transfers, and purchase orders for established business accounts. For large orders, we can also arrange flexible payment terms.",
                },
              ]}
            />
          </div>
        </div>
      </section>
    </main>
  )
}
