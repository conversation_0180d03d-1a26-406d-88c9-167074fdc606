import type { Metadata } from "next"
import PageHeader from "@/components/page-header"
import CheckoutForm from "@/components/checkout-form"
import OrderSummary from "@/components/order-summary"
import AuthGuard from "@/components/auth-guard"

export const metadata: Metadata = {
  title: "Checkout | Benzochem Industries",
  description: "Complete your purchase securely.",
}

export default function CheckoutPage() {
  return (
    <AuthGuard requireVerifiedGST={true}>
      <main className="flex min-h-screen flex-col pt-20">
        <PageHeader title="Checkout" />

        <section className="py-16">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              <div className="lg:col-span-2">
                <CheckoutForm />
              </div>
              <div>
                <OrderSummary />
              </div>
            </div>
          </div>
        </section>
      </main>
    </AuthGuard>
  )
}
