"use client"

import { motion } from "framer-motion"
import { useCart } from "@/hooks/use-cart" // Added
import { Skeleton } from "@/components/ui/skeleton" // Added

// Skeleton for the OrderSummary component
function OrderSummarySkeleton() {
  return (
    <div className="bg-white border rounded-lg overflow-hidden sticky top-24">
      <div className="p-6 border-b">
        <Skeleton className="h-6 w-3/4 rounded" /> {/* "Order Summary" Header */}
      </div>
      <div className="p-6">
        <div className="space-y-3 mb-6">
          {[...Array(2)].map((_, i) => ( // Placeholder for 2 items
            <div key={i} className="flex justify-between">
              <Skeleton className="h-5 w-2/3 rounded" /> {/* Item Name & Qty */}
              <Skeleton className="h-5 w-1/4 rounded" /> {/* Item Price */}
            </div>
          ))}
        </div>
        <div className="border-t pt-4 space-y-4">
          <div className="flex justify-between">
            <Skeleton className="h-5 w-1/3 rounded" /> {/* Subtotal Label */}
            <Skeleton className="h-5 w-1/4 rounded" /> {/* Subtotal Value */}
          </div>
          <div className="flex justify-between">
            <Skeleton className="h-5 w-1/3 rounded" /> {/* Shipping Label */}
            <Skeleton className="h-5 w-1/4 rounded" /> {/* Shipping Value */}
          </div>
          <div className="flex justify-between">
            <Skeleton className="h-5 w-1/3 rounded" /> {/* Tax Label */}
            <Skeleton className="h-5 w-1/4 rounded" /> {/* Tax Value */}
          </div>
          <div className="border-t pt-4 flex justify-between">
            <Skeleton className="h-6 w-1/3 rounded" /> {/* Total Label */}
            <Skeleton className="h-6 w-1/4 rounded" /> {/* Total Value */}
          </div>
        </div>
      </div>
    </div>
  )
}

export default function OrderSummary() {
  const { items, subtotal, isLoading, itemCount } = useCart()

  // These could be fetched or configured if they vary
  const shipping = itemCount > 0 ? 25.00 : 0;
  const taxRate = 0.08; // 8%
  const tax = itemCount > 0 ? subtotal * taxRate : 0;
  const total = itemCount > 0 ? subtotal + shipping + tax : 0;

  if (isLoading) {
    return <OrderSummarySkeleton />
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.2 }}
      className="bg-white border rounded-lg overflow-hidden sticky top-24"
    >
      <div className="p-6 border-b">
        <h2 className="text-xl font-medium">Order Summary</h2>
      </div>

      <div className="p-6">
        {itemCount > 0 && (
          <div className="space-y-3 mb-6">
            {items.map((item) => (
              <div key={item.id} className="flex justify-between">
                <span className="text-neutral-600 truncate pr-2">
                  {item.name} ({item.quantity})
                </span>
                <span className="font-medium">₹{(item.price * item.quantity).toFixed(2)}</span>
              </div>
            ))}
          </div>
        )}

        <div className="border-t pt-4 space-y-4">
          <div className="flex justify-between">
            <span className="text-neutral-600">Subtotal</span>
            <span className="font-medium">₹{subtotal.toFixed(2)}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-neutral-600">Shipping</span>
            <span className="font-medium">₹{shipping.toFixed(2)}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-neutral-600">Tax ({(taxRate * 100).toFixed(0)}%)</span>
            <span className="font-medium">₹{tax.toFixed(2)}</span>
          </div>
          <div className="border-t pt-4 flex justify-between">
            <span className="text-xl font-semibold">Total</span>
            <span className="text-xl font-semibold">₹{total.toFixed(2)}</span>
          </div>
        </div>
      </div>
      {/* Optional: Add a message if cart is empty, though CheckoutForm might handle this */}
      {itemCount === 0 && !isLoading && (
        <div className="p-6 text-center text-neutral-500">
          Your cart is empty. Add items to proceed.
        </div>
      )}
    </motion.div>
  )
}
