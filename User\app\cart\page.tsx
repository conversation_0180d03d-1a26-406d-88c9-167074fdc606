import type { Metadata } from "next"
import PageHeader from "@/components/page-header"
import CartItems from "@/components/cart-items"
import CartSummary from "@/components/cart-summary"
import AuthGuard from "@/components/auth-guard"

export const metadata: Metadata = {
  title: "Shopping Cart | Benzochem Industries",
  description: "Review and manage your shopping cart items.",
}

export default function CartPage() {
  return (
    <AuthGuard>
      <main className="flex min-h-screen flex-col pt-20">
        <PageHeader title="Shopping Cart" />

        <section className="py-16">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              <div className="lg:col-span-2">
                <CartItems />
              </div>
              <div>
                <CartSummary />
              </div>
            </div>
          </div>
        </section>
      </main>
    </AuthGuard>
  )
}
