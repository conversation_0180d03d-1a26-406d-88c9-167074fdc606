import { storefrontFetch } from './shopify'
import type { Product } from './types'

// GraphQL query for searching products
const SEARCH_PRODUCTS_QUERY = `
  query SearchProducts($query: String!, $first: Int!) {
    products(query: $query, first: $first) {
      edges {
        node {
          id
          title
          description
          tags
          images(first: 1) {
            edges {
              node {
                url
              }
            }
          }
          priceRange {
            minVariantPrice {
              amount
              currencyCode
            }
          }
          collections(first: 1) {
            edges {
              node {
                title
              }
            }
          }
        }
      }
    }
  }
`

// Search products by query string
export async function searchProducts(query: string, limit: number = 5): Promise<Product[]> {
  try {
    if (!query || query.trim() === '') {
      return []
    }

    const { data } = await storefrontFetch<{
      products: {
        edges: Array<{
          node: Product
        }>
      }
    }>({
      query: SEARCH_PRODUCTS_QUERY,
      variables: {
        query,
        first: limit
      }
    })

    if (!data?.products?.edges) {
      return []
    }

    return data.products.edges.map(edge => edge.node)
  } catch (error) {
    console.error('Error searching products:', error)
    return []
  }
}
