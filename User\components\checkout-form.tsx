"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { Check } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useCart } from "@/hooks/use-cart" // Added
import { Skeleton } from "@/components/ui/skeleton" // Added

// Skeleton for the Review Step's Order Items section
function ReviewOrderItemsSkeleton() {
  return (
    <div className="bg-neutral-50 rounded-md p-4 space-y-4">
      {[...Array(2)].map((_, i) => (
        <div key={i} className="flex justify-between">
          <div className="space-y-1">
            <Skeleton className="h-5 w-32 rounded" /> {/* Item Name */}
            <Skeleton className="h-4 w-20 rounded" /> {/* Quantity */}
          </div>
          <Skeleton className="h-5 w-16 rounded" /> {/* Item Price */}
        </div>
      ))}
    </div>
  )
}

export default function CheckoutForm() {
  const [activeStep, setActiveStep] = useState<string>("shipping")
  const [isComplete, setIsComplete] = useState(false)
  const { items: cartItems, isLoading: isCartLoading, subtotal, itemCount } = useCart() // Added useCart

  const handleContinue = () => {
    if (activeStep === "shipping") {
      setActiveStep("payment")
    } else if (activeStep === "payment") {
      setActiveStep("review")
    } else if (activeStep === "review") {
      if (itemCount === 0) {
        // Optionally, prevent placing order if cart is empty
        // Or rely on OrderSummary to show cart is empty and disable checkout there
        alert("Your cart is empty. Please add items before placing an order.");
        return;
      }
      // In a real app, this would submit the order
      setIsComplete(true)
    }
  }

  const handleBack = () => {
    if (activeStep === "payment") {
      setActiveStep("shipping")
    } else if (activeStep === "review") {
      setActiveStep("payment")
    }
  }

  if (isComplete) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="bg-white border rounded-lg p-8"
      >
        <div className="text-center">
          <div className="w-16 h-16 bg-teal-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <Check className="h-8 w-8 text-teal-600" />
          </div>
          <h2 className="text-2xl font-medium mb-4">Order Placed Successfully!</h2>
          <p className="text-neutral-600 mb-6 max-w-md mx-auto">
            Thank you for your order. We've received your payment and will begin processing your order immediately. You
            will receive a confirmation email shortly.
          </p>
          <p className="font-medium mb-8">Order #BZ-********</p> {/* This would be dynamic */}
          <Button asChild>
            <a href="/account/orders">View Order Status</a>
          </Button>
        </div>
      </motion.div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="bg-white border rounded-lg overflow-hidden"
    >
      <div className="p-6 border-b">
        <h2 className="text-xl font-medium">Checkout</h2>
      </div>

      <Tabs value={activeStep} className="w-full">
        <TabsList className="grid w-full grid-cols-3 p-0 h-auto">
          <TabsTrigger
            value="shipping"
            className="data-[state=active]:border-b-2 data-[state=active]:border-teal-600 data-[state=active]:shadow-none rounded-none py-4"
            onClick={() => setActiveStep("shipping")}
          >
            1. Shipping
          </TabsTrigger>
          <TabsTrigger
            value="payment"
            className="data-[state=active]:border-b-2 data-[state=active]:border-teal-600 data-[state=active]:shadow-none rounded-none py-4"
            onClick={() => setActiveStep("payment")}
          >
            2. Payment
          </TabsTrigger>
          <TabsTrigger
            value="review"
            className="data-[state=active]:border-b-2 data-[state=active]:border-teal-600 data-[state=active]:shadow-none rounded-none py-4"
            onClick={() => setActiveStep("review")}
          >
            3. Review
          </TabsTrigger>
        </TabsList>

        <TabsContent value="shipping" className="p-6 space-y-6">
          {/* Shipping form fields remain the same, assuming they are not pre-filled by async data */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <label htmlFor="firstName" className="text-sm font-medium">
                First Name
              </label>
              <Input id="firstName" required />
            </div>
            <div className="space-y-2">
              <label htmlFor="lastName" className="text-sm font-medium">
                Last Name
              </label>
              <Input id="lastName" required />
            </div>
          </div>

          <div className="space-y-2">
            <label htmlFor="company" className="text-sm font-medium">
              Company Name
            </label>
            <Input id="company" />
          </div>

          <div className="space-y-2">
            <label htmlFor="address" className="text-sm font-medium">
              Address
            </label>
            <Input id="address" required />
          </div>

          <div className="space-y-2">
            <label htmlFor="addressLine2" className="text-sm font-medium">
              Address Line 2
            </label>
            <Input id="addressLine2" />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-2">
              <label htmlFor="city" className="text-sm font-medium">
                City
              </label>
              <Input id="city" required />
            </div>
            <div className="space-y-2">
              <label htmlFor="state" className="text-sm font-medium">
                State / Province
              </label>
              <Input id="state" required />
            </div>
            <div className="space-y-2">
              <label htmlFor="zip" className="text-sm font-medium">
                ZIP / Postal Code
              </label>
              <Input id="zip" required />
            </div>
          </div>

          <div className="space-y-2">
            <label htmlFor="country" className="text-sm font-medium">
              Country
            </label>
            <Select>
              <SelectTrigger>
                <SelectValue placeholder="Select country" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="in">India</SelectItem>
                <SelectItem value="us">United States</SelectItem>
                <SelectItem value="ca">Canada</SelectItem>
                <SelectItem value="uk">United Kingdom</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label htmlFor="phone" className="text-sm font-medium">
              Phone Number
            </label>
            <Input id="phone" type="tel" required />
          </div>

          <div className="space-y-2">
            <label htmlFor="email" className="text-sm font-medium">
              Email Address
            </label>
            <Input id="email" type="email" required />
          </div>

          <div className="space-y-2">
            <label htmlFor="notes" className="text-sm font-medium">
              Order Notes (Optional)
            </label>
            <Textarea id="notes" placeholder="Special instructions for delivery or handling" />
          </div>

          <div className="pt-4 flex justify-end">
            <Button onClick={handleContinue} className="bg-teal-600 hover:bg-teal-700 text-white">
              Continue to Payment
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="payment" className="p-6 space-y-6">
          {/* Payment form fields remain the same */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Payment Method</h3>
            <RadioGroup defaultValue="credit-card" className="space-y-4">
              <div className="flex items-center space-x-2 border rounded-md p-4">
                <RadioGroupItem value="credit-card" id="credit-card" />
                <Label htmlFor="credit-card" className="flex-grow cursor-pointer">
                  Credit Card
                </Label>
                <div className="flex space-x-1"> {/* Placeholder for card icons */}
                  <Skeleton className="w-10 h-6 rounded bg-neutral-200" />
                  <Skeleton className="w-10 h-6 rounded bg-neutral-200" />
                  <Skeleton className="w-10 h-6 rounded bg-neutral-200" />
                </div>
              </div>
              <div className="flex items-center space-x-2 border rounded-md p-4">
                <RadioGroupItem value="paypal" id="paypal" />
                <Label htmlFor="paypal" className="flex-grow cursor-pointer">
                  PayPal
                </Label>
                <Skeleton className="w-16 h-6 rounded bg-neutral-200" /> {/* Placeholder for PayPal icon */}
              </div>
              <div className="flex items-center space-x-2 border rounded-md p-4">
                <RadioGroupItem value="bank-transfer" id="bank-transfer" />
                <Label htmlFor="bank-transfer" className="flex-grow cursor-pointer">
                  Bank Transfer
                </Label>
                 <Skeleton className="w-10 h-6 rounded bg-neutral-200" /> {/* Placeholder for Bank icon */}
              </div>
            </RadioGroup>
          </div>

          <div className="space-y-6 pt-4">
            <h3 className="text-lg font-medium">Card Details</h3>
            <div className="space-y-2">
              <label htmlFor="cardName" className="text-sm font-medium">
                Name on Card
              </label>
              <Input id="cardName" required />
            </div>

            <div className="space-y-2">
              <label htmlFor="cardNumber" className="text-sm font-medium">
                Card Number
              </label>
              <Input id="cardNumber" required placeholder="•••• •••• •••• ••••" />
            </div>

            <div className="grid grid-cols-2 gap-6">
              <div className="space-y-2">
                <label htmlFor="expiry" className="text-sm font-medium">
                  Expiry Date
                </label>
                <Input id="expiry" required placeholder="MM/YY" />
              </div>
              <div className="space-y-2">
                <label htmlFor="cvc" className="text-sm font-medium">
                  CVC
                </label>
                <Input id="cvc" required placeholder="•••" />
              </div>
            </div>
          </div>

          <div className="pt-4 flex justify-between">
            <Button variant="outline" onClick={handleBack}>
              Back to Shipping
            </Button>
            <Button onClick={handleContinue} className="bg-teal-600 hover:bg-teal-700 text-white">
              Continue to Review
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="review" className="p-6 space-y-6">
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-4">Shipping Information</h3>
              {/* This data would ideally come from form state, not cart loading state */}
              <div className="bg-neutral-50 rounded-md p-4">
                <p className="font-medium">John Doe</p> {/* Placeholder */}
                <p>Acme Corporation</p> {/* Placeholder */}
                <p>123 Main Street, Suite 100</p> {/* Placeholder */}
                <p>New York, NY 10001</p> {/* Placeholder */}
                <p>United States</p> {/* Placeholder */}
                <p>+1 (234) 567-890</p> {/* Placeholder */}
                <p><EMAIL></p> {/* Placeholder */}
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-4">Payment Method</h3>
              {/* This data would also come from form state */}
              <div className="bg-neutral-50 rounded-md p-4">
                <p className="font-medium">Credit Card</p> {/* Placeholder */}
                <p>Visa ending in 4242</p> {/* Placeholder */}
                <p>Expires 12/25</p> {/* Placeholder */}
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-4">Order Items</h3>
              {isCartLoading ? (
                <ReviewOrderItemsSkeleton />
              ) : cartItems.length > 0 ? (
                <div className="bg-neutral-50 rounded-md p-4 space-y-4">
                  {cartItems.map((item) => (
                    <div key={item.id} className="flex justify-between">
                      <div>
                        <p className="font-medium">{item.name}</p>
                        <p className="text-sm text-neutral-600">Quantity: {item.quantity}</p>
                      </div>
                      <p className="font-medium">₹{(item.price * item.quantity).toFixed(2)}</p>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="bg-neutral-50 rounded-md p-4 text-center text-neutral-500">
                  Your cart is empty.
                </div>
              )}
            </div>
          </div>

          <div className="pt-4 flex justify-between">
            <Button variant="outline" onClick={handleBack}>
              Back to Payment
            </Button>
            <Button 
              onClick={handleContinue} 
              className="bg-teal-600 hover:bg-teal-700 text-white"
              disabled={isCartLoading || itemCount === 0}
            >
              {isCartLoading ? "Loading..." : "Place Order"}
            </Button>
          </div>
        </TabsContent>
      </Tabs>
    </motion.div>
  )
}
