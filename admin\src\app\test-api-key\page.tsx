"use client"

import React from 'react';
import { SecureApiKeyDisplay } from '@/components/ui/secure-api-key-display';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export default function TestApiKeyPage() {
  const testApiKey = "bzk_live_NsmILAuZ1234567890abcdefghijklmnop";
  const testKeyId = "bzk_live_NsmILAuZ";
  const maskedApiKey = "bzk_live_NsmILAuZ...****"; // Simulating what comes from getApiKeys

  return (
    <div className="container mx-auto p-6 space-y-6">
      <h1 className="text-3xl font-bold">API Key Display Test</h1>

      <div className="grid gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Normal API Key (Requires Re-auth) - Full Key</CardTitle>
          </CardHeader>
          <CardContent>
            <SecureApiKeyDisplay
              apiKey={testApiKey}
              keyId={testKeyId}
              placeholder="••••••••••••••••••••••••••••••••"
              allowImmediateAccess={false}
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Normal API Key (Requires Re-auth) - Masked Key</CardTitle>
          </CardHeader>
          <CardContent>
            <SecureApiKeyDisplay
              apiKey={maskedApiKey}
              keyId={testKeyId}
              placeholder="••••••••••••••••••••••••••••••••"
              allowImmediateAccess={false}
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Newly Created API Key (Immediate Access)</CardTitle>
          </CardHeader>
          <CardContent>
            <SecureApiKeyDisplay
              apiKey={testApiKey}
              keyId={testKeyId}
              placeholder="••••••••••••••••••••••••••••••••"
              allowImmediateAccess={true}
            />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
