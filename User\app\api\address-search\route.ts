import { NextRequest, NextResponse } from 'next/server'
import { getGoogleAPIConfig } from '@/lib/env-validation'
import { getCountryCode, getProvinceCode } from '@/lib/country-mapping'

interface AddressSuggestion {
  id: string
  description: string
  structured_formatting?: {
    main_text: string
    secondary_text: string
  }
}

interface AddressDetails {
  address1: string
  address2?: string
  city: string
  province: string
  country: string
  zip: string
}

// Google Places API interfaces
interface GooglePlacesPrediction {
  place_id: string
  description: string
  structured_formatting: {
    main_text: string
    secondary_text: string
  }
}

interface GooglePlacesResponse {
  predictions: GooglePlacesPrediction[]
  status: string
}

interface GooglePlaceDetailsResponse {
  result: {
    address_components: Array<{
      long_name: string
      short_name: string
      types: string[]
    }>
    formatted_address: string
  }
  status: string
}

// Google Places Autocomplete API integration
export async function GET(request: NextRequest) {
  try {
    let googleConfig;
    try {
      googleConfig = getGoogleAPIConfig();
    } catch (error) {
      console.error("Google API configuration error:", error);
      // Return user-friendly error message
      return NextResponse.json({
        suggestions: [],
        error: "Address search is temporarily unavailable. Please enter your address manually."
      }, { status: 500 });
    }

    const { searchParams } = new URL(request.url)
    const query = searchParams.get('q')

    if (!query || query.length < 3) {
      return NextResponse.json({ suggestions: [] })
    }

    // Google Places Autocomplete API call
    const googlePlacesUrl = new URL('https://maps.googleapis.com/maps/api/place/autocomplete/json')
    googlePlacesUrl.searchParams.set('input', query)
    googlePlacesUrl.searchParams.set('key', googleConfig.placesApiKey)
    googlePlacesUrl.searchParams.set('components', 'country:in') // Restrict to India
    googlePlacesUrl.searchParams.set('types', 'address') // Focus on addresses
    googlePlacesUrl.searchParams.set('language', 'en') // English language

    const response = await fetch(googlePlacesUrl.toString())

    if (!response.ok) {
      throw new Error(`Google Places API error: ${response.status}`)
    }

    const data: GooglePlacesResponse = await response.json()

    if (data.status !== 'OK' && data.status !== 'ZERO_RESULTS') {
      console.error('Google Places API error:', data.status)

      // Log detailed error for developers but show simple message to users
      let userMessage = "Address search is temporarily unavailable. Please enter your address manually."

      if (data.status === 'REQUEST_DENIED') {
        console.error('Google Places API: Invalid API key or API not enabled')
      } else if (data.status === 'OVER_QUERY_LIMIT') {
        console.error('Google Places API: Quota exceeded')
        userMessage = "Address search is busy right now. Please try again in a moment or enter your address manually."
      } else if (data.status === 'INVALID_REQUEST') {
        console.error('Google Places API: Invalid request parameters')
      }

      return NextResponse.json({
        suggestions: [],
        error: userMessage
      }, { status: 500 })
    }

    // Convert Google Places predictions to our format
    const suggestions: AddressSuggestion[] = data.predictions.map(prediction => ({
      id: prediction.place_id,
      description: prediction.description,
      structured_formatting: {
        main_text: prediction.structured_formatting.main_text,
        secondary_text: prediction.structured_formatting.secondary_text
      }
    }))

    return NextResponse.json({ suggestions: suggestions.slice(0, 5) }) // Limit to 5 results

  } catch (error) {
    console.error('Address search error:', error)
    return NextResponse.json(
      {
        suggestions: [],
        error: 'Address search is temporarily unavailable. Please enter your address manually.'
      },
      { status: 500 }
    )
  }
}

// Get detailed address information using Google Places Details API
export async function POST(request: NextRequest) {
  try {
    let googleConfig;
    try {
      googleConfig = getGoogleAPIConfig();
    } catch (error) {
      console.error("Google API configuration error:", error);
      return NextResponse.json({
        error: "Address details are temporarily unavailable. Please enter your address manually."
      }, { status: 500 });
    }

    let body;
    try {
      body = await request.json()
    } catch (parseError) {
      console.error('JSON parse error:', parseError)
      return NextResponse.json(
        { error: 'Invalid JSON in request body' },
        { status: 400 }
      )
    }

    const { placeId } = body

    if (!placeId) {
      return NextResponse.json(
        { error: 'Place ID is required' },
        { status: 400 }
      )
    }

    // Google Places Details API call
    const googleDetailsUrl = new URL('https://maps.googleapis.com/maps/api/place/details/json')
    googleDetailsUrl.searchParams.set('place_id', placeId)
    googleDetailsUrl.searchParams.set('key', googleConfig.placesApiKey)
    googleDetailsUrl.searchParams.set('fields', 'address_components,formatted_address')
    googleDetailsUrl.searchParams.set('language', 'en')

    const response = await fetch(googleDetailsUrl.toString())

    if (!response.ok) {
      throw new Error(`Google Places Details API error: ${response.status}`)
    }

    const data: GooglePlaceDetailsResponse = await response.json()

    if (data.status !== 'OK') {
      console.error('Google Places Details API error:', data.status)

      // Log detailed error for developers but show simple message to users
      let userMessage = "Address details are temporarily unavailable. Please enter your address manually."

      if (data.status === 'REQUEST_DENIED') {
        console.error('Google Places Details API: Invalid API key or API not enabled')
      } else if (data.status === 'OVER_QUERY_LIMIT') {
        console.error('Google Places Details API: Quota exceeded')
        userMessage = "Address service is busy right now. Please try again in a moment or enter your address manually."
      } else if (data.status === 'NOT_FOUND') {
        console.error('Google Places Details API: Place not found')
        userMessage = "Selected address details not found. Please try a different address or enter manually."
      }

      return NextResponse.json({
        error: userMessage
      }, { status: 500 })
    }

    // Parse address components
    const addressComponents = data.result.address_components
    let streetNumber = ''
    let route = ''
    let sublocality = ''
    let locality = ''
    let administrativeAreaLevel1 = ''
    let administrativeAreaLevel2 = ''
    let country = ''
    let postalCode = ''

    addressComponents.forEach(component => {
      const types = component.types
      if (types.includes('street_number')) {
        streetNumber = component.long_name
      } else if (types.includes('route')) {
        route = component.long_name
      } else if (types.includes('sublocality') || types.includes('sublocality_level_1')) {
        sublocality = component.long_name
      } else if (types.includes('locality')) {
        locality = component.long_name
      } else if (types.includes('administrative_area_level_1')) {
        administrativeAreaLevel1 = component.long_name
      } else if (types.includes('administrative_area_level_2')) {
        administrativeAreaLevel2 = component.long_name
      } else if (types.includes('country')) {
        country = component.long_name
      } else if (types.includes('postal_code')) {
        postalCode = component.long_name
      }
    })

    // Construct address1 from available components
    let address1 = ''
    if (streetNumber && route) {
      address1 = `${streetNumber} ${route}`
    } else if (route) {
      address1 = route
    } else if (sublocality) {
      address1 = sublocality
    } else {
      // Fallback to the first part of formatted address
      const formattedParts = data.result.formatted_address.split(',')
      address1 = formattedParts[0]?.trim() || ''
    }

    // Use locality for city, fallback to administrative_area_level_2
    const city = locality || administrativeAreaLevel2 || ''

    // Convert country name to country code for Shopify compatibility
    const countryCode = getCountryCode(country)

    // Convert province name to province code for Shopify compatibility
    const provinceCode = getProvinceCode(administrativeAreaLevel1, countryCode)

    console.log('Address parsing results:', {
      originalCountry: country,
      convertedCountryCode: countryCode,
      originalProvince: administrativeAreaLevel1,
      convertedProvinceCode: provinceCode,
      city: city,
      zip: postalCode
    })

    const addressDetails: AddressDetails = {
      address1: address1,
      address2: sublocality && address1 !== sublocality ? sublocality : '',
      city: city,
      province: provinceCode, // Use province code instead of full name
      country: countryCode, // Use country code instead of full name
      zip: postalCode
    }

    return NextResponse.json({ address: addressDetails })

  } catch (error) {
    console.error('Address details fetch error:', error)
    return NextResponse.json(
      { error: 'Address details are temporarily unavailable. Please enter your address manually.' },
      { status: 500 }
    )
  }
}
