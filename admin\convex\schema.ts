import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  // Users table - customer and business user data
  users: defineTable({
    // User identification
    userId: v.string(), // Unique user identifier
    email: v.string(),
    firstName: v.string(),
    lastName: v.string(),
    phone: v.optional(v.string()),
    businessName: v.optional(v.string()),
    gstNumber: v.optional(v.string()),
    isGstVerified: v.optional(v.boolean()),
    
    // Admin-specific fields
    status: v.union(
      v.literal("pending"),
      v.literal("approved"),
      v.literal("rejected"),
      v.literal("suspended")
    ),
    role: v.union(
      v.literal("user"),
      v.literal("admin"),
      v.literal("super_admin")
    ),
    
    // Approval workflow
    approvedBy: v.optional(v.id("admins")),
    approvedAt: v.optional(v.number()),
    rejectedBy: v.optional(v.id("admins")),
    rejectedAt: v.optional(v.number()),
    rejectionReason: v.optional(v.string()),
    
    // Metadata
    createdAt: v.number(),
    updatedAt: v.number(),
    lastLoginAt: v.optional(v.number()),
    
    // Additional business info
    legalNameOfBusiness: v.optional(v.string()),
    tradeName: v.optional(v.string()),
    constitutionOfBusiness: v.optional(v.string()),
    taxpayerType: v.optional(v.string()),
    gstStatus: v.optional(v.string()),
    principalPlaceOfBusiness: v.optional(v.string()),
    
    // Marketing preferences
    agreedToEmailMarketing: v.optional(v.boolean()),
    agreedToSmsMarketing: v.optional(v.boolean()),
  })
    .index("by_email", ["email"])
    .index("by_user_id", ["userId"])
    .index("by_status", ["status"])
    .index("by_role", ["role"])
    .index("by_created_at", ["createdAt"]),

  // Admin users table (simplified for development/testing)
  admins: defineTable({
    email: v.string(),
    firstName: v.string(),
    lastName: v.string(),
    password: v.string(), // Plain text password (DEVELOPMENT ONLY - NOT SECURE!)
    role: v.optional(v.union(v.literal("admin"), v.literal("super_admin"))),
    permissions: v.optional(v.array(v.string())),
    isActive: v.optional(v.boolean()),
    createdAt: v.optional(v.number()),
    updatedAt: v.optional(v.number()),
  })
    .index("by_email", ["email"])
    .index("by_role", ["role"]),

  // Collections table - product categories and groupings
  collections: defineTable({
    // Collection identification
    collectionId: v.string(), // Unique collection identifier
    title: v.string(),
    description: v.optional(v.string()),
    handle: v.string(), // URL-friendly identifier

    // Collection metadata
    image: v.optional(v.object({
      url: v.string(),
      altText: v.optional(v.string()),
    })),

    // SEO and display
    seoTitle: v.optional(v.string()),
    seoDescription: v.optional(v.string()),

    // Collection settings
    status: v.union(
      v.literal("active"),
      v.literal("inactive")
    ),
    sortOrder: v.optional(v.number()), // For ordering collections
    isVisible: v.boolean(), // Whether to show in navigation

    // Product count (denormalized for performance)
    productCount: v.optional(v.number()),

    // Metadata
    createdAt: v.number(),
    updatedAt: v.number(),
    createdBy: v.optional(v.id("admins")),
    updatedBy: v.optional(v.id("admins")),
  })
    .index("by_collection_id", ["collectionId"])
    .index("by_handle", ["handle"])
    .index("by_status", ["status"])
    .index("by_sort_order", ["sortOrder"])
    .index("by_created_at", ["createdAt"]),

  // Products table - chemical products and inventory data
  products: defineTable({
    // Product identification
    productId: v.string(), // Unique product identifier
    title: v.string(),
    description: v.string(),
    descriptionHtml: v.optional(v.string()),
    tags: v.array(v.string()),
    collections: v.array(v.string()),
    images: v.array(v.object({
      url: v.string(),
      altText: v.optional(v.string()),
    })),
    
    // Pricing
    priceRange: v.object({
      minVariantPrice: v.object({
        amount: v.string(),
        currencyCode: v.string(),
      }),
      maxVariantPrice: v.object({
        amount: v.string(),
        currencyCode: v.string(),
      }),
    }),
    
    // Chemical-specific metafields
    purity: v.optional(v.string()),
    packaging: v.optional(v.string()),
    casNumber: v.optional(v.string()),
    hsnNumber: v.optional(v.string()),
    molecularFormula: v.optional(v.string()),
    molecularWeight: v.optional(v.string()),
    appearance: v.optional(v.string()),
    solubility: v.optional(v.string()),
    phValue: v.optional(v.string()),
    chemicalName: v.optional(v.string()),
    features: v.optional(v.array(v.string())),
    applications: v.optional(v.array(v.string())),
    applicationDetails: v.optional(v.array(v.string())),
    
    // Admin fields
    status: v.union(
      v.literal("active"),
      v.literal("inactive"),
      v.literal("discontinued"),
      v.literal("pending_review")
    ),
    featured: v.boolean(),
    totalInventory: v.optional(v.number()),
    
    // Metadata
    createdAt: v.number(),
    updatedAt: v.number(),
    lastSyncedAt: v.optional(v.number()),
    createdBy: v.optional(v.id("admins")),
    updatedBy: v.optional(v.id("admins")),
  })
    .index("by_product_id", ["productId"])
    .index("by_status", ["status"])
    .index("by_featured", ["featured"])
    .index("by_created_at", ["createdAt"])
    .index("by_cas_number", ["casNumber"]),

  // Notifications table
  notifications: defineTable({
    type: v.union(
      v.literal("user_registration"),
      v.literal("user_approval"),
      v.literal("user_rejection"),
      v.literal("product_update"),
      v.literal("system_alert"),
      v.literal("gst_verification"),
      v.literal("order_notification")
    ),
    title: v.string(),
    message: v.string(),
    
    // Recipients
    recipientType: v.union(
      v.literal("admin"),
      v.literal("user"),
      v.literal("all_admins"),
      v.literal("specific_user")
    ),
    recipientId: v.optional(v.union(v.id("admins"), v.id("users"))),
    
    // Status
    isRead: v.boolean(),
    readAt: v.optional(v.number()),
    readBy: v.optional(v.union(v.id("admins"), v.id("users"))),
    
    // Priority
    priority: v.union(
      v.literal("low"),
      v.literal("medium"),
      v.literal("high"),
      v.literal("urgent")
    ),
    
    // Related data
    relatedEntityType: v.optional(v.union(
      v.literal("user"),
      v.literal("product"),
      v.literal("order")
    )),
    relatedEntityId: v.optional(v.string()),
    
    // Metadata
    createdAt: v.number(),
    expiresAt: v.optional(v.number()),
    createdBy: v.optional(v.id("admins")),
  })
    .index("by_recipient", ["recipientType", "recipientId"])
    .index("by_type", ["type"])
    .index("by_priority", ["priority"])
    .index("by_read_status", ["isRead"])
    .index("by_created_at", ["createdAt"]),

  // System settings table
  settings: defineTable({
    key: v.string(),
    value: v.any(),
    description: v.optional(v.string()),
    category: v.union(
      v.literal("general"),
      v.literal("api"),
      v.literal("notifications"),
      v.literal("security"),
      v.literal("integrations")
    ),
    isPublic: v.boolean(), // Whether this setting can be accessed by non-admin users
    updatedBy: v.optional(v.id("admins")),
    updatedAt: v.number(),
    createdAt: v.number(),
  })
    .index("by_key", ["key"])
    .index("by_category", ["category"])
    .index("by_public", ["isPublic"]),

  // API keys table for external access
  apiKeys: defineTable({
    name: v.string(),
    key: v.string(), // Hashed API key (SHA-256)
    keyId: v.string(), // First 8 characters of the key for identification
    environment: v.union(v.literal("live"), v.literal("test")),
    permissions: v.array(v.string()),
    isActive: v.boolean(),
    expiresAt: v.optional(v.number()),
    lastUsedAt: v.optional(v.number()),
    usageCount: v.number(),
    rateLimit: v.object({
      requestsPerMinute: v.number(),
      requestsPerHour: v.number(),
      requestsPerDay: v.number(),
      burstLimit: v.optional(v.number()),
    }),
    // Rate limiting tracking
    rateLimitResets: v.optional(v.object({
      minute: v.number(),
      hour: v.number(),
      day: v.number(),
    })),
    rateLimitCounts: v.optional(v.object({
      minute: v.number(),
      hour: v.number(),
      day: v.number(),
      burst: v.number(),
    })),
    // Revocation tracking
    revokedAt: v.optional(v.number()),
    revokedBy: v.optional(v.id("admins")),
    revocationReason: v.optional(v.string()),
    // Rotation tracking
    rotatedAt: v.optional(v.number()),
    rotatedBy: v.optional(v.id("admins")),
    rotationReason: v.optional(v.string()),
    createdBy: v.id("admins"),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_key", ["key"])
    .index("by_key_id", ["keyId"])
    .index("by_environment", ["environment"])
    .index("by_active", ["isActive"])
    .index("by_created_by", ["createdBy"]),

  // Activity logs table
  activityLogs: defineTable({
    action: v.string(),
    entityType: v.union(
      v.literal("user"),
      v.literal("product"),
      v.literal("admin"),
      v.literal("setting"),
      v.literal("api_key"),
      v.literal("notification")
    ),
    entityId: v.string(),
    oldValues: v.optional(v.any()),
    newValues: v.optional(v.any()),
    performedBy: v.union(v.id("admins"), v.id("users")),
    performedByType: v.union(v.literal("admin"), v.literal("user"), v.literal("system")),
    ipAddress: v.optional(v.string()),
    userAgent: v.optional(v.string()),
    createdAt: v.number(),
  })
    .index("by_entity", ["entityType", "entityId"])
    .index("by_performed_by", ["performedBy"])
    .index("by_action", ["action"])
    .index("by_created_at", ["createdAt"]),

  // Security events table for monitoring suspicious activity
  securityEvents: defineTable({
    eventType: v.union(
      v.literal("invalid_api_key"),
      v.literal("rate_limit_exceeded"),
      v.literal("suspicious_usage_pattern"),
      v.literal("multiple_failed_attempts"),
      v.literal("unusual_ip_activity"),
      v.literal("permission_violation"),
      v.literal("key_rotation_required"),
      v.literal("potential_key_compromise")
    ),
    severity: v.union(
      v.literal("low"),
      v.literal("medium"),
      v.literal("high"),
      v.literal("critical")
    ),
    description: v.string(),
    details: v.any(), // JSON object with event-specific details

    // Request context
    ipAddress: v.optional(v.string()),
    userAgent: v.optional(v.string()),
    requestUrl: v.optional(v.string()),
    requestMethod: v.optional(v.string()),

    // API key context (if applicable)
    apiKeyId: v.optional(v.string()),
    apiKeyEnvironment: v.optional(v.union(v.literal("live"), v.literal("test"))),

    // Status and resolution
    status: v.union(
      v.literal("open"),
      v.literal("investigating"),
      v.literal("resolved"),
      v.literal("false_positive")
    ),
    resolvedBy: v.optional(v.id("admins")),
    resolvedAt: v.optional(v.number()),
    resolutionNotes: v.optional(v.string()),

    // Alerting
    alertSent: v.optional(v.boolean()),
    alertSentAt: v.optional(v.number()),

    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_event_type", ["eventType"])
    .index("by_severity", ["severity"])
    .index("by_status", ["status"])
    .index("by_api_key", ["apiKeyId"])
    .index("by_ip", ["ipAddress"])
    .index("by_created_at", ["createdAt"]),
});
