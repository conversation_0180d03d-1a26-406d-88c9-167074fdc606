"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useMutation, useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { toast } from "sonner";

export default function TestPage() {
  // Test queries
  const users = useQuery(api.users.getUsers, {});
  const products = useQuery(api.products.getProducts, {});
  const userStats = useQuery(api.users.getUserStats);
  const productStats = useQuery(api.products.getProductStats);

  // Test mutation
  const seedData = useMutation(api.seed.seedSampleData);

  const handleSeedData = async () => {
    try {
      const result = await seedData({});
      toast.success(`Sample data seeded successfully! Created ${result.counts.users} users, ${result.counts.products} products`);
    } catch (error) {
      toast.error("Failed to seed data");
      console.error(error);
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Convex Test Page</h1>
        <p className="text-muted-foreground">Test Convex functions and seed sample data</p>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>User Statistics</CardTitle>
            <CardDescription>Current user data from Convex</CardDescription>
          </CardHeader>
          <CardContent>
            {userStats ? (
              <div className="space-y-2">
                <div>Total Users: {userStats.total}</div>
                <div>Pending: {userStats.pending}</div>
                <div>Approved: {userStats.approved}</div>
                <div>Rejected: {userStats.rejected}</div>
                <div>GST Verified: {userStats.gstVerified}</div>
              </div>
            ) : (
              <div>Loading user stats...</div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Product Statistics</CardTitle>
            <CardDescription>Current product data from Convex</CardDescription>
          </CardHeader>
          <CardContent>
            {productStats ? (
              <div className="space-y-2">
                <div>Total Products: {productStats.total}</div>
                <div>Active: {productStats.active}</div>
                <div>Inactive: {productStats.inactive}</div>
                <div>Featured: {productStats.featured}</div>
                <div>Collections: {productStats.collections}</div>
              </div>
            ) : (
              <div>Loading product stats...</div>
            )}
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Sample Data</CardTitle>
          <CardDescription>Seed the database with sample data for testing</CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={handleSeedData}>
            Seed Sample Data
          </Button>
        </CardContent>
      </Card>

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Users ({users?.length || 0})</CardTitle>
          </CardHeader>
          <CardContent>
            {users ? (
              <div className="space-y-2">
                {users.slice(0, 5).map((user) => (
                  <div key={user._id} className="p-2 border rounded">
                    <div className="font-medium">{user.firstName} {user.lastName}</div>
                    <div className="text-sm text-muted-foreground">{user.email}</div>
                    <div className="text-sm">Status: {user.status}</div>
                  </div>
                ))}
                {users.length > 5 && (
                  <div className="text-sm text-muted-foreground">
                    ... and {users.length - 5} more
                  </div>
                )}
              </div>
            ) : (
              <div>Loading users...</div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Products ({products?.length || 0})</CardTitle>
          </CardHeader>
          <CardContent>
            {products ? (
              <div className="space-y-2">
                {products.slice(0, 5).map((product) => (
                  <div key={product._id} className="p-2 border rounded">
                    <div className="font-medium">{product.title}</div>
                    <div className="text-sm text-muted-foreground">{product.casNumber}</div>
                    <div className="text-sm">Status: {product.status}</div>
                  </div>
                ))}
                {products.length > 5 && (
                  <div className="text-sm text-muted-foreground">
                    ... and {products.length - 5} more
                  </div>
                )}
              </div>
            ) : (
              <div>Loading products...</div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
