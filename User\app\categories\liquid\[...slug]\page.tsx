import type { Metadata } from "next"
import { Suspense } from 'react' 
import CategoryHeader from "@/components/category-header"
import ProductGrid from "@/components/product-grid"
import { getProductsByCollection } from "@/lib/products"
import { Skeleton } from "@/components/ui/skeleton" 
// Re-using ProductCardSkeleton from featured products skeleton
import { default as ProductCardSkeleton } from "@/components/featured-products-skeleton" // Adjusted import for default export

export const metadata: Metadata = {
  title: "Liquid Products | Benzochem Industries",
  description: "Browse our premium liquid chemical products for industrial and laboratory applications.",
}

// Skeleton for the product grid section of a category page
function CategoryPageProductGridSkeleton() {
  return (
    <div className="container mx-auto px-4 py-12">
      <div className="space-y-8">
        {/* Skeleton for Header (Title, Count) and Controls (Filter, Sort) */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
          <div>
            <Skeleton className="h-7 w-32 mb-1 rounded" /> {/* "Products" title */}
            <Skeleton className="h-4 w-24 rounded" /> {/* Product count */}
          </div>
          <div className="flex gap-2">
            <Skeleton className="h-10 w-24 rounded-md" /> {/* Filter Button */}
            <Skeleton className="h-10 w-24 rounded-md" /> {/* Sort Button */}
          </div>
        </div>

        {/* Skeleton for Product Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {[...Array(8)].map((_, i) => ( // Show 8 skeleton cards
            // ProductCardSkeleton is imported and expects no props for its skeleton structure
            // If ProductCardSkeleton was not default export, it would be <FeaturedProductCardSkeleton />
            // For now, assuming ProductCardSkeleton from featured-products-skeleton.tsx is the one with ProductCardSkeleton
            // and FeaturedProductsSkeleton is the one with the grid and filters.
            // Let's define a simple one here if the import is tricky or use the one from featured-products-skeleton.
            // The import `import { default as ProductCardSkeleton } from "@/components/featured-products-skeleton"`
            // means ProductCardSkeleton IS the FeaturedProductsSkeleton component, which contains filters + grid.
            // This is not what we want. We want the *individual card skeleton*.
            // I will redefine a simple ProductCardSkeleton here for clarity,
            // or assume the `featured-products-skeleton.tsx` exports `ProductCardSkeleton` namedly.
            // For now, let's assume `components/featured-products-skeleton.tsx` exports `ProductCardSkeleton` as a named export.
            // If not, this will need adjustment.
            // Correcting the thought process: `featured-products-skeleton.tsx` exports `FeaturedProductsSkeleton` as default.
            // It internally defines `ProductCardSkeleton`. I need to extract `ProductCardSkeleton` or redefine.
            // For simplicity, I'll redefine a basic card skeleton here.
            <BasicProductCardSkeleton key={i} />
          ))}
        </div>
      </div>
    </div>
  )
}

// Basic skeleton for an individual product card (can be moved to a shared file later)
function BasicProductCardSkeleton() {
  return (
    <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 space-y-3">
      <Skeleton className="h-40 w-full rounded-md" /> {/* Image */}
      <Skeleton className="h-6 w-3/4 rounded" /> {/* Title */}
      <Skeleton className="h-4 w-full rounded" /> {/* Description line 1 */}
      <Skeleton className="h-4 w-5/6 rounded" /> {/* Description line 2 */}
      <div className="flex justify-between items-center pt-2">
        <Skeleton className="h-5 w-1/3 rounded" /> {/* Price */}
        <Skeleton className="h-8 w-1/3 rounded" /> {/* Button */}
      </div>
    </div>
  );
}


// Component to fetch and render actual category content
async function LiquidCategoryPageContent() {
  const products = await getProductsByCollection("liquid")
  // console.log("Liquid category page received", products.length, "products") // Original log had "Powder"

  return (
    <div className="container mx-auto px-4 py-12">
      <ProductGrid products={products} />
    </div>
  )
}

export default async function LiquidCategoryPage() { // No params needed here
  return (
    <main className="flex min-h-screen flex-col pt-20">
      <CategoryHeader
        title="Liquid Products"
        description="Premium liquid solutions for specialized chemical processes"
        image="https://images.visualelectric.com/08e4505c-6e36-4d72-91f0-74a3d8b72982/large"
      />
      <Suspense fallback={<CategoryPageProductGridSkeleton />}>
        <LiquidCategoryPageContent />
      </Suspense>
    </main>
  )
}