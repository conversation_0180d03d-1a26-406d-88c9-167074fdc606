"use client";

import React from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";

interface BusinessInfoProps {
  formData: {
    legalNameOfBusiness: string;
    tradeName: string;
    dateOfRegistration: string;
    constitutionOfBusiness: string;
    taxpayerType: string;
    principalPlaceOfBusiness: string;
    natureOfCoreBusinessActivity: string;
    agreedToEmailMarketing: boolean;
    agreedToSmsMarketing: boolean;
  };
  handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  handleCheckboxChange: (name: string, checked: boolean) => void;
  fieldErrors: Record<string, string | null>;
  isGstVerified: boolean;
}

const BusinessInfo: React.FC<BusinessInfoProps> = ({
  formData,
  handleChange,
  handleCheckboxChange,
  fieldErrors,
  isGstVerified,
}) => {
  return (
    <div className="space-y-5">
      <div>
        <Label htmlFor="legalNameOfBusiness" className="text-sm font-medium">Legal Name of Business</Label>
        <Input
          id="legalNameOfBusiness"
          name="legalNameOfBusiness"
          value={formData.legalNameOfBusiness}
          onChange={handleChange}
          placeholder="As per GST registration"
          required={!isGstVerified}
          readOnly={isGstVerified}
          className={`${fieldErrors.legalNameOfBusiness ? "border-red-500" : "border-neutral-200 focus:border-teal-500 focus:ring-teal-500/20"} ${isGstVerified ? "bg-neutral-50" : ""}`}
        />
        {fieldErrors.legalNameOfBusiness && (
          <p className="text-red-500 text-xs mt-1">{fieldErrors.legalNameOfBusiness}</p>
        )}
      </div>

      <div>
        <Label htmlFor="tradeName" className="text-sm font-medium">Trade Name (Optional)</Label>
        <Input
          id="tradeName"
          name="tradeName"
          value={formData.tradeName}
          onChange={handleChange}
          placeholder="Your business brand name"
          readOnly={isGstVerified}
           className={`${fieldErrors.tradeName ? "border-red-500" : "border-neutral-200 focus:border-teal-500 focus:ring-teal-500/20"} ${isGstVerified ? "bg-neutral-50" : ""}`}
        />
         {fieldErrors.tradeName && (
          <p className="text-red-500 text-xs mt-1">{fieldErrors.tradeName}</p>
        )}
      </div>

      <div>
        <Label htmlFor="dateOfRegistration" className="text-sm font-medium">Date of Registration (YYYY-MM-DD)</Label>
        <Input
          id="dateOfRegistration"
          name="dateOfRegistration"
          type="date" // Use date type for better UX
          value={formData.dateOfRegistration}
          onChange={handleChange}
          placeholder="YYYY-MM-DD"
          required={!isGstVerified}
          readOnly={isGstVerified}
          className={`${fieldErrors.dateOfRegistration ? "border-red-500" : "border-neutral-200 focus:border-teal-500 focus:ring-teal-500/20"} ${isGstVerified ? "bg-neutral-50" : ""}`}
        />
        {fieldErrors.dateOfRegistration && (
          <p className="text-red-500 text-xs mt-1">{fieldErrors.dateOfRegistration}</p>
        )}
      </div>

      <div>
        <Label htmlFor="constitutionOfBusiness" className="text-sm font-medium">Constitution of Business</Label>
        <Input
          id="constitutionOfBusiness"
          name="constitutionOfBusiness"
          value={formData.constitutionOfBusiness}
          onChange={handleChange}
          placeholder="e.g., Proprietorship, Partnership, Pvt Ltd"
          required={!isGstVerified}
          readOnly={isGstVerified}
          className={`${fieldErrors.constitutionOfBusiness ? "border-red-500" : "border-neutral-200 focus:border-teal-500 focus:ring-teal-500/20"} ${isGstVerified ? "bg-neutral-50" : ""}`}
        />
        {fieldErrors.constitutionOfBusiness && (
          <p className="text-red-500 text-xs mt-1">{fieldErrors.constitutionOfBusiness}</p>
        )}
      </div>

      <div>
        <Label htmlFor="taxpayerType" className="text-sm font-medium">Taxpayer Type</Label>
        <Input
          id="taxpayerType"
          name="taxpayerType"
          value={formData.taxpayerType}
          onChange={handleChange}
          placeholder="e.g., Regular, Composition"
          required={!isGstVerified}
          readOnly={isGstVerified}
          className={`${fieldErrors.taxpayerType ? "border-red-500" : "border-neutral-200 focus:border-teal-500 focus:ring-teal-500/20"} ${isGstVerified ? "bg-neutral-50" : ""}`}
        />
        {fieldErrors.taxpayerType && (
          <p className="text-red-500 text-xs mt-1">{fieldErrors.taxpayerType}</p>
        )}
      </div>

      <div>
        <Label htmlFor="principalPlaceOfBusiness" className="text-sm font-medium">Principal Place of Business Address</Label>
        <Textarea
          id="principalPlaceOfBusiness"
          name="principalPlaceOfBusiness"
          value={formData.principalPlaceOfBusiness}
          onChange={handleChange}
          placeholder="Enter the full address"
          required={!isGstVerified}
          readOnly={isGstVerified}
          className={`${fieldErrors.principalPlaceOfBusiness ? "border-red-500" : "border-neutral-200 focus:border-teal-500 focus:ring-teal-500/20"} ${isGstVerified ? "bg-neutral-50" : ""}`}
        />
        {fieldErrors.principalPlaceOfBusiness && (
          <p className="text-red-500 text-xs mt-1">{fieldErrors.principalPlaceOfBusiness}</p>
        )}
      </div>

      <div>
        <Label htmlFor="natureOfCoreBusinessActivity" className="text-sm font-medium">Nature of Core Business Activity</Label>
        <Textarea
          id="natureOfCoreBusinessActivity"
          name="natureOfCoreBusinessActivity"
          value={formData.natureOfCoreBusinessActivity}
          onChange={handleChange}
          placeholder="Describe your main business activity"
          required={!isGstVerified}
          readOnly={isGstVerified}
          className={`${fieldErrors.natureOfCoreBusinessActivity ? "border-red-500" : "border-neutral-200 focus:border-teal-500 focus:ring-teal-500/20"} ${isGstVerified ? "bg-neutral-50" : ""}`}
        />
        {fieldErrors.natureOfCoreBusinessActivity && (
          <p className="text-red-500 text-xs mt-1">{fieldErrors.natureOfCoreBusinessActivity}</p>
        )}
      </div>

      <div className="space-y-2">
        <div className="flex items-center space-x-2">
          <Checkbox
            id="agreedToEmailMarketing"
            name="agreedToEmailMarketing"
            checked={formData.agreedToEmailMarketing}
            onCheckedChange={(checked) => handleCheckboxChange("agreedToEmailMarketing", !!checked)}
          />
          <Label htmlFor="agreedToEmailMarketing" className="text-sm font-normal">
            I agree to receive marketing emails.
          </Label>
        </div>
        <div className="flex items-center space-x-2">
          <Checkbox
            id="agreedToSmsMarketing"
            name="agreedToSmsMarketing"
            checked={formData.agreedToSmsMarketing}
            onCheckedChange={(checked) => handleCheckboxChange("agreedToSmsMarketing", !!checked)}
          />
          <Label htmlFor="agreedToSmsMarketing" className="text-sm font-normal">
            I agree to receive marketing SMS messages.
          </Label>
        </div>
      </div>
    </div>
  );
};

export default BusinessInfo;

