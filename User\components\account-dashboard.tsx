"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent } from "@/components/ui/tabs"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Separator } from "@/components/ui/separator"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { 
  Package, 
  CreditCard, 
  User, 
  Heart, 
  LogOut, 
  FileText, 
  Building, 
  Loader2, 
  ShoppingBag, 
  MapPin, 
  Phone, 
  Mail, 
  Clock, 
  CheckCircle2,
  AlertCircle,
  ChevronRight,
  Settings,
  Shield,
  Edit,
  Save,
  Calendar,
  Truck,
  RefreshCw
} from "lucide-react"
import { useAuth } from "@/contexts/shopify-auth-context"
import OrderHistory from "@/components/order-history"
import SavedProducts from "@/components/saved-products"
import GstDetails from "@/components/gst-details"
import { <PERSON><PERSON>, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { fetchShopifyCustomerData } from "@/lib/shopify-customer-data"
// import { getCustomerAccessToken, isCustomerAccessTokenValid } from "@/lib/customer-service" // Commented out as it's not used directly in this file after refactor
import { useRouter, usePathname } from "next/navigation"
import AccountHeader from "./account/AccountHeader"
import ErrorDisplay from "./account/ErrorDisplay"
import AccountSidebar from "./account/AccountSidebar"
import AccountOverviewTab from "./account/tabs/AccountOverviewTab"
import BusinessDetailsTab from "./account/tabs/BusinessDetailsTab" // Added import
import PaymentMethodsTab from "./account/tabs/PaymentMethodsTab"
import OrderHistoryTab from "./account/tabs/OrderHistoryTab"
import SavedProductsTab from "./account/tabs/SavedProductsTab"


export default function AccountDashboard({ initialShopifyData }: { initialShopifyData?: any }) {
  console.log('[AccountDashboard] Component mounted, initialShopifyData present:', !!initialShopifyData);
  const [activeTab, setActiveTab] = useState("overview")
  const [shopifyData, setShopifyData] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isEditing, setIsEditing] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [tokenValid, setTokenValid] = useState<boolean | null>(null)
  const [isUpdating, setIsUpdating] = useState(false)
  const [updateStatus, setUpdateStatus] = useState<'success' | 'error' | null>(null)
  const [updateMessage, setUpdateMessage] = useState('')
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    phoneCountryCode: "+91",
    company: "",
    address1: "",
    address2: "",
    city: "",
    province: "",
    country: "",
    zip: "",
    shippingFirstName: "",
    shippingLastName: "",
    shippingPhone: ""
  })

  // Track original values to detect changes
  const [originalFormData, setOriginalFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    phoneCountryCode: "+91",
    company: "",
    address1: "",
    address2: "",
    city: "",
    province: "",
    country: "",
    zip: ""
  })

  // Track which fields have been modified
  const [modifiedFields, setModifiedFields] = useState<Set<string>>(new Set())
  const { user, logout } = useAuth()
  const router = useRouter()
  const pathname = usePathname()

  // Helper function to set both formData and originalFormData
  const setFormDataAndOriginal = (data: typeof formData) => {
    setFormData(data)
    setOriginalFormData(data)
    setModifiedFields(new Set()) // Clear modified fields when setting new data
  }
  
  // Handle form input changes with change tracking
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target

    // Update form data
    setFormData(prev => ({
      ...prev,
      [id]: value
    }))

    // Track which fields have been modified
    const originalValue = originalFormData[id as keyof typeof originalFormData]
    if (value !== originalValue) {
      setModifiedFields(prev => new Set(prev).add(id))
    } else {
      setModifiedFields(prev => {
        const newSet = new Set(prev)
        newSet.delete(id)
        return newSet
      })
    }

    // Clear update status when user starts editing
    if (updateStatus) {
      setUpdateStatus(null)
      setUpdateMessage('')
    }
  }

  // Handle address selection from search - only update city, state, country, pincode
  const handleAddressSelect = (address: { address1: string; address2?: string; city: string; province: string; country: string; zip: string }) => {
    console.log('🏠 Address selected from search:', address);

    // Only update city, state, country, and pincode - let user manually enter address lines
    setFormData(prev => ({
      ...prev,
      city: address.city,
      province: address.province,
      country: address.country,
      zip: address.zip
    }))

    // Mark only the auto-filled fields as modified
    const autoFilledFields = ['city', 'province', 'country', 'zip']
    setModifiedFields(prev => {
      const newSet = new Set(prev)
      autoFilledFields.forEach(field => {
        const newValue = address[field as keyof typeof address] || ''
        const originalValue = originalFormData[field as keyof typeof originalFormData]
        if (newValue !== originalValue) {
          newSet.add(field)
        }
      })
      return newSet
    })

    // Clear update status when user starts editing
    if (updateStatus) {
      setUpdateStatus(null)
      setUpdateMessage('')
    }
  }

  // Handle form submission with selective field updates
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!shopifyData?.id) {
      setUpdateStatus('error')
      setUpdateMessage('Customer ID not found. Please refresh the page and try again.')
      return
    }

    // Check if any fields have been modified
    if (modifiedFields.size === 0) {
      setUpdateStatus('error')
      setUpdateMessage('No changes detected. Please modify some fields before saving.')
      return
    }

    setIsUpdating(true)
    setUpdateStatus(null)
    setUpdateMessage('')

    try {
      console.log("Modified fields:", Array.from(modifiedFields))
      console.log("Updating customer with selective data:", formData)

      // Separate basic customer fields from address fields
      const basicFields = ['firstName', 'lastName']
      const addressFields = ['company', 'address1', 'address2', 'city', 'province', 'country', 'zip', 'shippingFirstName', 'shippingLastName', 'shippingPhone']

      const modifiedBasicFields = basicFields.filter(field => modifiedFields.has(field))
      const modifiedAddressFields = addressFields.filter(field => modifiedFields.has(field))

      let customerUpdateResult = null

      // Update basic customer information and address if any basic/address fields were modified
      if (modifiedBasicFields.length > 0 || modifiedAddressFields.length > 0) {
        const updatePayload: any = {
          customerId: shopifyData.id
        }

        // Only include modified basic fields
        if (modifiedBasicFields.includes('firstName')) {
          updatePayload.firstName = formData.firstName
        }
        if (modifiedBasicFields.includes('lastName')) {
          updatePayload.lastName = formData.lastName
        }

        // Include complete address if any address fields were modified
        // This ensures required fields like city, country are always included
        if (modifiedAddressFields.length > 0) {
          updatePayload.address = {
            company: formData.company,
            address1: formData.address1,
            address2: formData.address2,
            city: formData.city,
            province: formData.province,
            country: formData.country,
            zip: formData.zip,
            firstName: formData.shippingFirstName,
            lastName: formData.shippingLastName,
            phone: formData.shippingPhone
          }
        }

        console.log("Sending customer update payload:", updatePayload)
        console.log("Current formData:", formData)
        console.log("Modified address fields:", modifiedAddressFields)

        const customerResponse = await fetch('/api/update-customer', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(updatePayload)
        })

        customerUpdateResult = await customerResponse.json()

        if (!customerUpdateResult.success) {
          setUpdateStatus('error')
          setUpdateMessage(customerUpdateResult.error || 'Failed to update customer information.')
          return
        }
      }



      // Success - update local state and reset tracking
      setUpdateStatus('success')
      setUpdateMessage('Information updated successfully!')
      setIsEditing(false)

      // Update shopifyData with the new customer data
      if (customerUpdateResult?.customer) {
        setShopifyData((prev: any) => ({
          ...prev,
          firstName: customerUpdateResult.customer.firstName,
          lastName: customerUpdateResult.customer.lastName,
          defaultAddress: customerUpdateResult.customer.defaultAddress
        }))

        // Update formData with the latest data from Shopify to ensure consistency
        const updatedFormData = {
          ...formData,
          firstName: customerUpdateResult.customer.firstName || formData.firstName,
          lastName: customerUpdateResult.customer.lastName || formData.lastName,
          company: customerUpdateResult.customer.defaultAddress?.company || formData.company,
          address1: customerUpdateResult.customer.defaultAddress?.address1 || formData.address1,
          address2: customerUpdateResult.customer.defaultAddress?.address2 || formData.address2,
          city: customerUpdateResult.customer.defaultAddress?.city || formData.city,
          province: customerUpdateResult.customer.defaultAddress?.province || formData.province,
          country: customerUpdateResult.customer.defaultAddress?.country || formData.country,
          zip: customerUpdateResult.customer.defaultAddress?.zip || formData.zip,
          shippingFirstName: customerUpdateResult.customer.defaultAddress?.firstName || formData.shippingFirstName,
          shippingLastName: customerUpdateResult.customer.defaultAddress?.lastName || formData.shippingLastName,
          shippingPhone: customerUpdateResult.customer.defaultAddress?.phone || formData.shippingPhone
        }

        console.log('🔄 Updating formData with Shopify response:', {
          original: formData,
          updated: updatedFormData,
          shopifyAddress: customerUpdateResult.customer.defaultAddress
        })

        // Update both formData and originalFormData with the latest Shopify data
        setFormData(updatedFormData)
        setOriginalFormData(updatedFormData)
      } else {
        // If no customer data returned, just reset with current form data
        setOriginalFormData(formData)
      }

      // Clear modified fields
      setModifiedFields(new Set())

      // Auto-hide success message after 5 seconds
      setTimeout(() => {
        setUpdateStatus(null)
        setUpdateMessage('')
      }, 5000)

    } catch (error) {
      console.error('Error updating customer:', error)
      setUpdateStatus('error')
      setUpdateMessage('An unexpected error occurred. Please try again.')
    } finally {
      setIsUpdating(false)
    }
  }
  
  // Format date for better display
  const formatDate = (dateString: string) => {
    if (!dateString) return ""
    const options: Intl.DateTimeFormatOptions = { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    }
    return new Date(dateString).toLocaleDateString('en-US', options)
  }
  
  // Format currency for better display
  const formatCurrency = (amount: string, currency: string) => {
    if (!amount) return ""
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: currency || 'INR',
      minimumFractionDigits: 2
    }).format(parseFloat(amount))
  }
  
  // Function to redirect to login page
  const redirectToLogin = () => {
    router.push('/login')
  }
  


  useEffect(() => {
    console.log('🔄 AccountDashboard useEffect triggered');
    console.log('👤 user from auth context:', user);
    console.log('📦 initialShopifyData:', initialShopifyData);

    // If we have initialShopifyData from server action, use it
    if (initialShopifyData) {
      console.log('Using initialShopifyData from server action:', {
        id: initialShopifyData.id,
        email: initialShopifyData.email,
        firstName: initialShopifyData.firstName,
        lastName: initialShopifyData.lastName,
        businessName: initialShopifyData.businessName,
        gstNumber: initialShopifyData.gstNumber,
        isGstVerified: initialShopifyData.isGstVerified,
        clerkId: initialShopifyData.clerkId
      });
      
      console.log('[AccountDashboard] Processing initialShopifyData:', {
        id: initialShopifyData.id,
        firstName: initialShopifyData.firstName,
        lastName: initialShopifyData.lastName,
        email: initialShopifyData.email,
        phone: initialShopifyData.phone,
        businessName: initialShopifyData.businessName,
        gstNumber: initialShopifyData.gstNumber,
        metafields: initialShopifyData.metafields
      });
      
      // Set the Shopify data to state - this is the source of truth
      setShopifyData(initialShopifyData);
      
      // Set form data exclusively from Shopify data
      console.log('🏠 Address debugging - initialShopifyData.defaultAddress:', initialShopifyData.defaultAddress);

      const newFormData = {
        firstName: initialShopifyData.firstName || "",
        lastName: initialShopifyData.lastName || "",
        email: initialShopifyData.email || "",
        phone: initialShopifyData.phone || "",
        phoneCountryCode: "+91",
        company: initialShopifyData.defaultAddress?.company || "",
        address1: initialShopifyData.defaultAddress?.address1 || "",
        address2: initialShopifyData.defaultAddress?.address2 || "",
        city: initialShopifyData.defaultAddress?.city || "",
        province: initialShopifyData.defaultAddress?.province || "",
        country: initialShopifyData.defaultAddress?.country || "",
        zip: initialShopifyData.defaultAddress?.zip || "",
        shippingFirstName: initialShopifyData.defaultAddress?.firstName || initialShopifyData.firstName || "",
        shippingLastName: initialShopifyData.defaultAddress?.lastName || initialShopifyData.lastName || "",
        shippingPhone: initialShopifyData.defaultAddress?.phone || initialShopifyData.phone || ""
      };

      console.log('🏠 Address debugging - newFormData address fields:', {
        address1: newFormData.address1,
        address2: newFormData.address2,
        city: newFormData.city,
        province: newFormData.province,
        country: newFormData.country,
        zip: newFormData.zip
      });
      
      console.log('[AccountDashboard] Setting formData with Shopify data only:', newFormData);
      setFormDataAndOriginal(newFormData);
      setIsLoading(false);
      return;
    }
    
    // If no initialShopifyData, fetch customer data from Shopify
    const fetchCustomerData = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        console.log('=== ACCOUNT DASHBOARD DATA FLOW DEBUGGING ===');
        console.log('1. Starting customer data fetch process');
        

        
        // Get user data from localStorage first to ensure we have something to display
        const storedUserData = localStorage.getItem("benzochem_user");
        let userData = null;
        
        console.log('2. Checking localStorage for user data');
        if (storedUserData) {
          try {
            userData = JSON.parse(storedUserData);
            console.log('Found user data in localStorage:', {
              email: userData.email,
              firstName: userData.firstName,
              lastName: userData.lastName,
              shopifyCustomerId: userData.shopifyCustomerId || 'NOT SET'
            });
            
            // Immediately populate form with user data from localStorage
            const initialFormData = {
              firstName: userData.firstName || "",
              lastName: userData.lastName || "",
              email: userData.email || "",
              phone: userData.phone || "",
              phoneCountryCode: "+91",
              company: "",
              address1: "",
              address2: "",
              city: "",
              province: "",
              country: "",
              zip: "",
              shippingFirstName: userData.firstName || "",
              shippingLastName: userData.lastName || "",
              shippingPhone: userData.phone || ""
            };
            console.log('📝 Setting initial form data from localStorage:', initialFormData);
            setFormDataAndOriginal(initialFormData);
          } catch (parseError) {
            console.error("Error parsing stored user data:", parseError);
          }
        } else {
          console.log('No user data found in localStorage');
        }
        
        // Skip the API call and use localStorage data directly
        console.log('3. Using localStorage data for authentication (client-side only)');

        // Fetch customer data directly using the client-side function
        let customerData = null;
        console.log('4. Attempting to fetch customer data using Shopify customer access token');
        
        try {
          // Use the new /api/current-user endpoint that has correct metafield mappings
          const customerAccessToken = localStorage.getItem('shopify_customer_access_token');

          if (customerAccessToken) {
            console.log('🔑 Using customer access token for API call');

            const response = await fetch('/api/current-user', {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json',
                'x-customer-access-token': customerAccessToken,
              }
            });

            if (response.ok) {
              customerData = await response.json();
              console.log('✅ Customer data from /api/current-user:', {
                id: customerData.id,
                email: customerData.email,
                firstName: customerData.firstName,
                lastName: customerData.lastName,
                businessName: customerData.businessName,
                gstNumber: customerData.gstNumber,
                isGstVerified: customerData.isGstVerified
              });

              // If we found customer data via access token, update localStorage with the Shopify customer ID
              if (userData) {
                userData.shopifyCustomerId = customerData.id;
                localStorage.setItem("benzochem_user", JSON.stringify(userData));
                console.log('Updated localStorage with Shopify customer ID from token lookup:', customerData.id);
              }
            } else {
              const errorData = await response.json();
              console.log('❌ API call failed:', errorData.error);
            }
          } else {
            console.log('❌ No customer access token found');
          }
        } catch (error) {
          console.error('Error fetching customer data:', error);
        }
        
        // We've already tried both access token and Clerk ID approaches above
        if (!customerData) {
          console.log('No customer data found using either access token or Clerk ID');
        }
        
        if (customerData) {
          // If we got Shopify data, update the form with it - this is the only source of data we use
          console.log('[AccountDashboard] ✅ Got Shopify customer data:', customerData);
          console.log('🏠 Address debugging - customerData.defaultAddress:', customerData.defaultAddress);

          setShopifyData(customerData)
          const newFormData = {
            firstName: customerData.firstName || "",
            lastName: customerData.lastName || "",
            email: customerData.email || "",
            phone: customerData.phone || "",
            phoneCountryCode: "+91",
            company: customerData.defaultAddress?.company || "",
            address1: customerData.defaultAddress?.address1 || "",
            address2: customerData.defaultAddress?.address2 || "",
            city: customerData.defaultAddress?.city || "",
            province: customerData.defaultAddress?.province || "",
            country: customerData.defaultAddress?.country || "",
            zip: customerData.defaultAddress?.zip || "",
            shippingFirstName: customerData.defaultAddress?.firstName || customerData.firstName || "",
            shippingLastName: customerData.defaultAddress?.lastName || customerData.lastName || "",
            shippingPhone: customerData.defaultAddress?.phone || customerData.phone || ""
          };

          console.log('🏠 Address debugging - newFormData from API:', {
            address1: newFormData.address1,
            address2: newFormData.address2,
            city: newFormData.city,
            province: newFormData.province,
            country: newFormData.country,
            zip: newFormData.zip
          });
          console.log('[AccountDashboard] Setting formData with pure Shopify data:', newFormData);
          setFormDataAndOriginal(newFormData);
        } else {
          // If no Shopify data, try to use localStorage data
          console.log('[AccountDashboard] ❌ No Shopify data found - trying localStorage fallback');
          const storedUserData = localStorage.getItem("benzochem_user");
          console.log('[AccountDashboard] localStorage benzochem_user:', storedUserData);

          if (storedUserData) {
            try {
              const userData = JSON.parse(storedUserData);
              console.log('[AccountDashboard] ✅ Using localStorage data:', userData);
              const fallbackFormData = {
                firstName: userData.firstName || "",
                lastName: userData.lastName || "",
                email: userData.email || "",
                phone: userData.phone || "",
                phoneCountryCode: "+91",
                company: "",
                address1: "",
                address2: "",
                city: "",
                province: "",
                country: "",
                zip: "",
                shippingFirstName: userData.firstName || "",
                shippingLastName: userData.lastName || "",
                shippingPhone: userData.phone || ""
              };
              console.log('[AccountDashboard] Setting formData with localStorage data:', fallbackFormData);
              setFormDataAndOriginal(fallbackFormData);
            } catch (parseError) {
              console.error('[AccountDashboard] Error parsing localStorage data:', parseError);
              setFormDataAndOriginal({
                firstName: "",
                lastName: "",
                email: "",
                phone: "",
                phoneCountryCode: "+91",
                company: "",
                address1: "",
                address2: "",
                city: "",
                province: "",
                country: "",
                zip: "",
                shippingFirstName: "",
                shippingLastName: "",
                shippingPhone: ""
              });
            }
          } else {
            console.log('[AccountDashboard] ❌ No localStorage data found - showing empty form');
            setFormDataAndOriginal({
              firstName: "",
              lastName: "",
              email: "",
              phone: "",
              phoneCountryCode: "+91",
              company: "",
              address1: "",
              address2: "",
              city: "",
              province: "",
              country: "",
              zip: "",
              shippingFirstName: "",
              shippingLastName: "",
              shippingPhone: ""
            });
          }
        }
      } catch (error: any) {
        console.error("Error fetching customer data:", error);
        // Don't show error message for missing token, just use user data if available
        const storedUserData = localStorage.getItem("benzochem_user");
        if (storedUserData) {
          try {
            const userData = JSON.parse(storedUserData);
            setFormDataAndOriginal({
              firstName: userData.firstName || "",
              lastName: userData.lastName || "",
              email: userData.email || "",
              phone: userData.phone || "",
              phoneCountryCode: "+91",
              company: "",
              address1: "",
              address2: "",
              city: "",
              province: "",
              country: "",
              zip: "",
              shippingFirstName: userData.firstName || "",
              shippingLastName: userData.lastName || "",
              shippingPhone: userData.phone || ""
            });
          } catch (parseError) {
            console.error("Error parsing stored user data:", parseError);
            setError("Unable to load your account information");
          }
        } else {
          // For other errors, show a generic message
          setError("Unable to load your account information");
        }
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchCustomerData()
  }, [user, initialShopifyData])

  return (
    <div className="max-w-7xl mx-auto">
      <AccountHeader
        isLoading={isLoading}
        error={error}
        shopifyData={shopifyData}
        user={user}
        setActiveTab={setActiveTab}
        logout={logout}
        redirectToLogin={redirectToLogin}
      />

      {/* Main Content Area */}
      {error ? (
        <ErrorDisplay error={error} redirectToLogin={redirectToLogin} />
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
          <AccountSidebar
            activeTab={activeTab}
            setActiveTab={setActiveTab}
            shopifyData={shopifyData}
            user={user}
          />

          {/* Main Content */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
            className="lg:col-span-9"
          >
            <Tabs value={activeTab} className="w-full">
              {/* Account Overview Tab */}
              <TabsContent value="overview">
                <AccountOverviewTab
                  isLoading={isLoading}
                  shopifyData={shopifyData}
                  user={user}
                  isEditing={isEditing}
                  setIsEditing={setIsEditing}
                  formData={formData}
                  handleInputChange={handleInputChange}
                  handleSubmit={handleSubmit}
                  handleAddressSelect={handleAddressSelect}
                  setActiveTab={setActiveTab}
                  formatDate={formatDate}
                  formatCurrency={formatCurrency}
                  isUpdating={isUpdating}
                  updateStatus={updateStatus}
                  updateMessage={updateMessage}
                  modifiedFields={modifiedFields}
                />
              </TabsContent>

              {/* Order History Tab */}
              <TabsContent value="orders">
                <OrderHistoryTab />
              </TabsContent>

              {/* Business Profile Tab */}
              <TabsContent value="business">
                <BusinessDetailsTab isLoading={isLoading} shopifyData={shopifyData}/>
              </TabsContent>

              {/* Saved Products Tab */}
              <TabsContent value="saved">
                <SavedProductsTab />
              </TabsContent>
              
              {/* Payment Methods Tab */}
              {/* The second TabsContent value="business" that was here has been removed as it was likely inactive. */}
              {/* Its content is now in components/account/tabs/BusinessDetailsTab.tsx */}
              <TabsContent value="payment">
                <PaymentMethodsTab />
              </TabsContent>
            </Tabs>
          </motion.div>
        </div>
      )}
    </div>
  )
}
