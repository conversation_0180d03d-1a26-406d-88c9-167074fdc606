"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { useAuth } from "@/contexts/shopify-auth-context"
import RegisterPageContent from "@/app/register/page-content"
import { Loader2 } from "lucide-react"

export default function RegisterPageWrapper() {
  const { user, isLoading } = useAuth()
  const [isRedirecting, setIsRedirecting] = useState(false)
  const router = useRouter()

  useEffect(() => {
    // If user is already authenticated, redirect to home page
    if (!isLoading && user) {
      setIsRedirecting(true)
      // Small delay to ensure smooth transition
      setTimeout(() => {
        router.push("/")
      }, 500)
    }
  }, [isLoading, user, router])

  // Show loading state while checking or redirecting
  if (isLoading || isRedirecting) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center bg-[#FAFAFA]">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-12 w-12 animate-spin text-teal-600" />
          <p className="text-lg text-gray-600">
            {isRedirecting ? "You're already logged in! Redirecting..." : "Checking authentication status..."}
          </p>
        </div>
      </div>
    )
  }

  // Show register page once we've confirmed user is not signed in
  return <RegisterPageContent />
}
