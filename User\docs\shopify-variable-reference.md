# Shopify Email Template Variables Reference

## Customer Account Invite Template Variables

### ✅ CORRECT Variables for Customer Account Invite

```liquid
{{ account_activation_url }}     <!-- The activation link - USE THIS -->
{{ customer.first_name }}       <!-- Customer's first name -->
{{ customer.last_name }}        <!-- Customer's last name -->
{{ customer.email }}            <!-- Customer's email address -->
{{ shop.name }}                 <!-- Your store name -->
{{ shop.email }}                <!-- Store contact email -->
{{ shop.address.address1 }}     <!-- Store address line 1 -->
{{ shop.address.city }}         <!-- Store city -->
{{ shop.address.province }}     <!-- Store province/state -->
{{ shop.address.zip }}          <!-- Store zip/postal code -->
{{ shop.address.country }}      <!-- Store country -->
{{ "now" | date: "%Y" }}        <!-- Current year -->
{{ "now" | date: "%B %d, %Y" }} <!-- Current date formatted -->
```

### ❌ INCORRECT Variables (Don't Use These)

```liquid
{{ customer_url }}              <!-- WRONG - doesn't work for invitations -->
{{ activation_url }}            <!-- WRONG - not a valid variable -->
{{ invite_url }}                <!-- WRONG - not a valid variable -->
{{ customer.activation_url }}   <!-- WRONG - not available -->
```

## The Problem and Solution

### ❌ What Was Wrong:
```html
<!-- This doesn't work - button won't be clickable -->
<a href="{{ customer_url }}">Activate Your Account</a>
```

### ✅ What's Correct:
```html
<!-- This works - button will be clickable -->
<a href="{{ account_activation_url }}">Activate Your Account</a>
```

## Complete Working Example

```html
<div style="text-align: center; margin: 30px 0;">
  <a href="{{ account_activation_url }}" 
     style="background-color: #14b8a6; 
            color: white; 
            padding: 15px 30px; 
            text-decoration: none; 
            border-radius: 6px; 
            display: inline-block; 
            font-size: 16px; 
            font-weight: bold;">
    Activate Your Account
  </a>
</div>

<p>Or copy and paste this link into your browser:</p>
<p style="background-color: #f8f9fa; 
          padding: 15px; 
          border-radius: 6px; 
          word-break: break-all;">
  {{ account_activation_url }}
</p>
```

## Different Email Template Types

### Customer Account Invite
- **Purpose**: Sent when customer account is created but not activated
- **Key Variable**: `{{ account_activation_url }}`
- **When Used**: During registration process

### Customer Account Welcome  
- **Purpose**: Sent after customer activates their account
- **Key Variable**: `{{ customer.url }}` (for account dashboard)
- **When Used**: After successful activation

### Password Reset
- **Purpose**: Sent when customer requests password reset
- **Key Variable**: `{{ customer.reset_password_url }}`
- **When Used**: Forgot password flow

## Testing Variables

### In Shopify Admin:
1. Go to Settings > Notifications
2. Find the email template
3. Click "Preview" to see how variables render
4. Send test email to verify functionality

### In Development:
```javascript
// Test the invitation API
fetch('/api/shopify/send-customer-invite', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'User',
    password: 'TestPassword123!'
  })
})
.then(res => res.json())
.then(data => {
  console.log('API Response:', data);
  if (data.activationUrl) {
    console.log('Activation URL:', data.activationUrl);
  }
});
```

## Common Mistakes to Avoid

### 1. Wrong Variable Name
```html
<!-- DON'T DO THIS -->
<a href="{{ customer_url }}">Activate</a>

<!-- DO THIS INSTEAD -->
<a href="{{ account_activation_url }}">Activate</a>
```

### 2. Missing Liquid Syntax
```html
<!-- DON'T DO THIS -->
<a href="account_activation_url">Activate</a>

<!-- DO THIS INSTEAD -->
<a href="{{ account_activation_url }}">Activate</a>
```

### 3. Wrong Email Template Type
- Don't use `{{ account_activation_url }}` in "Customer account welcome" template
- Don't use `{{ customer.url }}` in "Customer account invite" template

## Debugging Tips

### Check Variable Output:
```html
<!-- Add this temporarily to see what the variable contains -->
<p>Debug - Activation URL: {{ account_activation_url }}</p>
```

### Verify Email Template Type:
- Make sure you're editing "Customer account invite" not "Customer account welcome"
- Check that the template is enabled in Shopify Admin

### Test Email Delivery:
1. Send test email from Shopify Admin
2. Check spam/junk folder
3. Verify email settings in Shopify Admin
4. Use different email address for testing

## Additional Resources

- [Shopify Liquid Documentation](https://shopify.dev/api/liquid)
- [Email Notification Variables](https://help.shopify.com/en/manual/sell-online/notifications/email-variables)
- [Customer Account Settings](https://help.shopify.com/en/manual/customers/customer-accounts)
