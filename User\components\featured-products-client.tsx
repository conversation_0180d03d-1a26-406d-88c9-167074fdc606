"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import ProductCard from "@/components/product-card"
import type { Product } from "@/lib/types"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle } from "lucide-react"

interface FeaturedProductsClientProps {
  initialProducts: Product[]
  error: string | null
}

export default function FeaturedProductsClient({ initialProducts, error }: FeaturedProductsClientProps) {
  const [activeFilter, setActiveFilter] = useState<string>("all")

  // Get unique categories from products' collections
  const categories = Array.from(
    new Set(
      initialProducts.flatMap((product) =>
        product.collections.edges.map((edge) => edge.node.title)
      )
    )
  )

  const filteredProducts =
    activeFilter === "all"
      ? initialProducts
      : initialProducts.filter((product) =>
          product.collections.edges.some((edge) => edge.node.title === activeFilter)
        )

  if (error) {
    return (
      <Alert variant="destructive" className="mb-6">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>Error loading products: {error}</AlertDescription>
      </Alert>
    )
  }

  if (initialProducts.length === 0) {
    return (
      <Alert className="mb-6">
        <AlertDescription>No products found. Please talk to our customer care.</AlertDescription>
      </Alert>
    )
  }

  return (
    <div className="space-y-8">
      <div className="flex justify-center space-x-4 mb-8 flex-wrap gap-2">
        <button
          onClick={() => setActiveFilter("all")}
          className={`px-4 py-2 text-sm font-medium rounded-full transition-colors ${
            activeFilter === "all" ? "bg-teal-600 text-white" : "bg-neutral-100 text-neutral-600 hover:bg-neutral-200"
          }`}
        >
          All Products
        </button>

        {categories.map((category) => (
          <button
            key={category}
            onClick={() => setActiveFilter(category)}
            className={`px-4 py-2 text-sm font-medium rounded-full transition-colors ${
              activeFilter === category
                ? "bg-teal-600 text-white"
                : "bg-neutral-100 text-neutral-600 hover:bg-neutral-200"
            }`}
          >
            {category}
          </button>
        ))}
      </div>

      <motion.div
        className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ staggerChildren: 0.1 }}
      >
        {filteredProducts.length > 0 ? (
          filteredProducts.map((product) => <ProductCard key={product.id} product={product} />)
        ) : (
          <p className="col-span-full text-center text-gray-500">No products found in this category.</p>
        )}
      </motion.div>
    </div>
  )
}
