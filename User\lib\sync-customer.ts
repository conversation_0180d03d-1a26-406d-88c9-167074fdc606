// Utility functions for Shopify customer management

import { storefrontFetch } from './shopify'

// Create customer in Shopify during registration
export async function createShopifyCustomer(email: string, firstName?: string, lastName?: string, phone?: string, password?: string) {
  try {

    // Build customer input
    const customerInput: any = {
      email,
      firstName: firstName || '',
      lastName: lastName || '',
      phone: phone || '',
      acceptsMarketing: false
    }

    // Add password if provided (needed for customer creation with authentication)
    if (password) {
      customerInput.password = password
    }

    const { data, errors } = await storefrontFetch<any>({
      query: `
        mutation customerCreate($input: CustomerCreateInput!) {
          customerCreate(input: $input) {
            customer {
              id
              email
              firstName
              lastName
              phone
            }
            customerUserErrors {
              field
              message
              code
            }
          }
        }
      `,
      variables: {
        input: customerInput
      }
    })

    if (errors) {
      console.error('GraphQL errors creating Shopify customer:', errors)
      return { success: false, error: 'GraphQL errors occurred while creating customer' }
    }

    const result = data?.customerCreate

    if (result?.customerUserErrors?.length > 0) {
      // Check if customer already exists
      const existsError = result.customerUserErrors.find((error: any) =>
        error.code === 'TAKEN' || error.message.includes('already exists')
      )

      if (existsError) {
        return {
          success: true,
          customer: { email }, // Return minimal customer data
          message: 'Customer already exists',
          alreadyExists: true
        }
      }

      console.error('User errors creating Shopify customer:', result.customerUserErrors)
      return { success: false, error: result.customerUserErrors[0].message, code: result.customerUserErrors[0].code }
    }

    if (result?.customer) {
      return {
        success: true,
        customer: result.customer,
        shopifyCustomerId: result.customer.id
      }
    }

    return { success: false, error: 'No customer data returned from Shopify' }

  } catch (error) {
    console.error('Error creating Shopify customer:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error occurred' }
  }
}

// Get existing Shopify customer by email
export async function getShopifyCustomerByEmail(email: string) {
  try {
    console.log('🔍 Looking up Shopify customer by email:', email)
    
    // Note: Storefront API doesn't allow customer lookup by email
    // This would need to be done via Admin API or during authentication
    // For now, return a placeholder that indicates we need to authenticate
    
    return { 
      success: false, 
      error: 'Customer lookup requires authentication',
      requiresAuth: true
    }

  } catch (error) {
    console.error('Error looking up Shopify customer:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

// Authenticate with Shopify and get customer access token
export async function authenticateShopifyCustomer(email: string, password: string) {
  try {
    
    const { data, errors } = await storefrontFetch<any>({
      query: `
        mutation customerAccessTokenCreate($input: CustomerAccessTokenCreateInput!) {
          customerAccessTokenCreate(input: $input) {
            customerAccessToken {
              accessToken
              expiresAt
            }
            customerUserErrors {
              field
              message
              code
            }
          }
        }
      `,
      variables: {
        input: {
          email,
          password
        }
      }
    })

    if (errors) {
      console.error('GraphQL errors authenticating Shopify customer:', errors)
      return { success: false, error: 'GraphQL errors' }
    }

    const result = data?.customerAccessTokenCreate
    
    if (result?.customerUserErrors?.length > 0) {
      console.error('User errors authenticating Shopify customer:', result.customerUserErrors)
      return {
        success: false,
        error: result.customerUserErrors[0].message,
        code: result.customerUserErrors[0].code
      }
    }

    if (result?.customerAccessToken) {
      return {
        success: true,
        accessToken: result.customerAccessToken.accessToken,
        expiresAt: result.customerAccessToken.expiresAt
      }
    }

    return { success: false, error: 'No access token returned' }

  } catch (error) {
    console.error('Error authenticating Shopify customer:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

// Get customer data using access token
export async function getShopifyCustomerData(accessToken: string) {
  try {

    const { data, errors } = await storefrontFetch<any>({
      query: `
        query getCustomer($customerAccessToken: String!) {
          customer(customerAccessToken: $customerAccessToken) {
            id
            email
            firstName
            lastName
            phone
            acceptsMarketing
            createdAt
            updatedAt
          }
        }
      `,
      variables: {
        customerAccessToken: accessToken
      }
    })

    if (errors) {
      console.error('GraphQL errors getting customer data:', errors)
      return { success: false, error: 'GraphQL errors' }
    }

    if (data?.customer) {
      return {
        success: true,
        customer: data.customer
      }
    }

    return { success: false, error: 'No customer data returned' }

  } catch (error) {
    console.error('Error getting customer data:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

// Helper function to create customer with password for cart sync scenarios
export async function createShopifyCustomerWithPassword(userData: any, password: string) {
  return await createShopifyCustomer(
    userData.email,
    userData.firstName || userData.first_name || '',
    userData.lastName || userData.last_name || '',
    userData.phone || '',
    password
  )
}
