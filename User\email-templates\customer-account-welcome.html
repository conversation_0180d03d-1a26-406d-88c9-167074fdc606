<!-- 
Customer Account Welcome Email Template for Shopify
Copy this HTML and paste it into Shopify Admin > Settings > Notifications > Customer account welcome

This template is used when customers are created with activated accounts (immediate access)
-->

<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #ffffff;">
  <!-- Header -->
  <div style="background-color: #14b8a6; color: white; padding: 20px; text-align: center;">
    <h2 style="margin: 0; font-size: 24px;">Welcome to {{ shop.name }}!</h2>
  </div>

  <!-- Main Content -->
  <div style="padding: 30px 20px;">
    <p style="font-size: 16px; color: #333; margin-bottom: 20px;">Hi {{ customer.first_name }},</p>

    <p style="font-size: 16px; color: #333; line-height: 1.6; margin-bottom: 25px;">
      Thank you for registering with Benzochem Industries! We're excited to have you as part of our business community.
    </p>

    <!-- Success Message Box -->
    <div style="background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%); padding: 30px; border-radius: 12px; margin: 35px 0; border-left: 6px solid #14b8a6; box-shadow: 0 8px 25px rgba(0,0,0,0.08);">
      <div style="font-size: 15px; color: #475569; line-height: 1.7;">
        🎉 <strong>Your account is now active!</strong> You can start browsing our premium chemical products and accessing exclusive business pricing right away.
      </div>
    </div>

    <!-- Account Access Information -->
    <div style="background-color: #f0f9ff; padding: 25px; border-radius: 8px; margin: 30px 0; border: 1px solid #e0f2fe;">
      <h3 style="color: #14b8a6; margin-top: 0; margin-bottom: 15px; font-size: 18px;">Your Account Details</h3>
      <div style="color: #475569; line-height: 1.8;">
        <div><strong>Email:</strong> {{ customer.email }}</div>
        <div><strong>Name:</strong> {{ customer.first_name }} {{ customer.last_name }}</div>
        <div><strong>Account Status:</strong> <span style="color: #059669; font-weight: bold;">Active</span></div>
      </div>
    </div>

    <!-- Login Button -->
    <div style="text-align: center; margin: 30px 0;">
      <a href="{{ shop.url }}/account/login" 
         style="background-color: #14b8a6; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; display: inline-block; font-size: 16px; font-weight: bold; box-shadow: 0 4px 12px rgba(20, 184, 166, 0.3);">
        Access Your Account
      </a>
    </div>

    <!-- What's Available Section -->
    <div style="background-color: #f0f9ff; padding: 25px; border-radius: 8px; margin: 30px 0; border: 1px solid #e0f2fe;">
      <h3 style="color: #14b8a6; margin-top: 0; margin-bottom: 15px; font-size: 18px;">What You Can Do Now</h3>
      <ul style="color: #475569; line-height: 1.8; margin: 0; padding-left: 20px;">
        <li>Browse our extensive chemical product catalog</li>
        <li>Access exclusive business pricing</li>
        <li>Place orders and track shipments</li>
        <li>Manage your business profile and GST details</li>
        <li>View order history and invoices</li>
        <li>Contact our dedicated support team</li>
      </ul>
    </div>

    <!-- Quick Links -->
    <div style="background-color: #fef3c7; padding: 20px; border-radius: 8px; margin: 25px 0; border-left: 4px solid #f59e0b;">
      <h4 style="color: #92400e; margin-top: 0; margin-bottom: 10px;">Quick Links</h4>
      <div style="color: #92400e; font-size: 14px; line-height: 1.6;">
        <div>🛍️ <a href="{{ shop.url }}/collections" style="color: #92400e; text-decoration: none;">Browse Products</a></div>
        <div>👤 <a href="{{ shop.url }}/account" style="color: #92400e; text-decoration: none;">Manage Account</a></div>
        <div>📞 <a href="{{ shop.url }}/pages/contact" style="color: #92400e; text-decoration: none;">Contact Support</a></div>
      </div>
    </div>

    <p style="font-size: 16px; color: #333; margin-bottom: 10px;">
      If you have any questions or need assistance, our support team is here to help. Don't hesitate to reach out!
    </p>

    <p style="font-size: 16px; color: #333; margin-bottom: 0;">
      Best regards,<br>
      <strong>The Benzochem Industries Team</strong>
    </p>
  </div>

  <!-- Footer -->
  <div style="background-color: #f8f9fa; padding: 20px; text-align: center; font-size: 12px; color: #666; border-top: 1px solid #e2e8f0;">
    <p style="margin: 0 0 5px 0;">© {{ "now" | date: "%Y" }} {{ shop.name }}. All rights reserved.</p>
    <p style="margin: 0;">{{ shop.address.address1 }}, {{ shop.address.city }}, {{ shop.address.province }} {{ shop.address.zip }}</p>
  </div>
</div>

<!-- 
SHOPIFY VARIABLES REFERENCE FOR WELCOME EMAIL:
- {{ shop.name }} - Your store name
- {{ shop.url }} - Your store URL
- {{ customer.first_name }} - Customer's first name
- {{ customer.last_name }} - Customer's last name
- {{ customer.email }} - Customer's email
- {{ shop.address.address1 }} - Store address
- {{ shop.address.city }} - Store city
- {{ shop.address.province }} - Store province/state
- {{ shop.address.zip }} - Store zip code
- {{ "now" | date: "%Y" }} - Current year

IMPORTANT: 
- This template is for "Customer account welcome" notification
- Customers receive this when their account is created and activated immediately
- No activation link needed since account is already active
- Focus on welcoming and guiding them to use their account
-->
