"use client"

import React from "react"
import { useAuth } from "@/contexts/shopify-auth-context"
import { useCart } from "@/hooks/use-cart"
import { Button } from "@/components/ui/button"
import { useRouter } from "next/navigation"
import { toast } from "sonner"

interface AddToCartButtonProps {
  productId: string
  variantId: string
  name: string
  price: number
  quantity: number
  image?: string
  category?: string
  packageSize?: string // Add packageSize prop
  disabled?: boolean
}

export default function AddToCartButton({
  productId,
  variantId,
  name,
  price,
  quantity,
  image,
  category,
  packageSize, // Destructure packageSize prop
  disabled
}: AddToCartButtonProps) {
  const { user } = useAuth()
  const { addItem } = useCart()
  const router = useRouter()

  const handleAddToCart = async () => {
    if (!user) {
      // Redirect to login with current page as redirect
      router.push(`/login?redirect=${encodeURIComponent(window.location.pathname)}`)
      return
    }

    try {
      await addItem({
        variantId,
        quantity,
        name,
        price,
        image,
        category,
        packageSize // Pass packageSize to addItem
      })
      toast.success(`${name} added to cart!`)
    } catch (error) {
      console.error("Error adding to cart:", error)
      toast.error("Failed to add item to cart.")
    }
  }

  return (
    <Button
      onClick={handleAddToCart}
      className="w-full bg-teal-600 hover:bg-teal-700 text-white disabled:opacity-50 disabled:cursor-not-allowed"
      disabled={disabled}
    >
      {disabled ? "Out of Stock" : "Add to Cart"}
    </Button>
  )
}