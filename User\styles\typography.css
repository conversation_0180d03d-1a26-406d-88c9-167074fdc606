/* Typography CSS for Benzochem Industries */

/* Base Typography */
:root {
  /* Font families are set via CSS variables in layout.tsx */
  --line-height-headings: 1.2;
  --line-height-subheadings: 1.3;
  --line-height-body: 1.5;
  
  --letter-spacing-headings: -0.02em;
  --letter-spacing-body: normal;
}

/* Headings (H1-H3) */
h1, h2, h3 {
  font-family: var(--font-headings);
  letter-spacing: var(--letter-spacing-headings);
  font-weight: 700;
  line-height: var(--line-height-headings);
}

h1 {
  font-size: 2.25rem; /* 36px */
  font-weight: 800;
}

@media (min-width: 768px) {
  h1 {
    font-size: 3rem; /* 48px */
  }
}

h2 {
  font-size: 1.875rem; /* 30px */
}

@media (min-width: 768px) {
  h2 {
    font-size: 2.25rem; /* 36px */
  }
}

h3 {
  font-size: 1.5rem; /* 24px */
}

@media (min-width: 768px) {
  h3 {
    font-size: 1.875rem; /* 30px */
  }
}

/* Subheadings (H4-H6) */
h4, h5, h6 {
  font-family: var(--font-headings);
  letter-spacing: var(--letter-spacing-headings);
  line-height: var(--line-height-subheadings);
  font-weight: 600;
}

h4 {
  font-size: 1.25rem; /* 20px */
}

h5 {
  font-size: 1.125rem; /* 18px */
}

h6 {
  font-size: 1rem; /* 16px */
}

/* Body Text */
body, p {
  font-family: var(--font-body);
  font-size: 1rem; /* 16px */
  line-height: var(--line-height-body);
  letter-spacing: var(--letter-spacing-body);
  font-weight: 400;
}

/* Navigation & UI Elements */
nav, button, .ui-element {
  font-family: var(--font-ui);
  font-weight: 500;
}

/* Product Names */
.product-name {
  font-family: var(--font-headings);
  font-weight: 600;
}

/* Price & CTAs */
.price, .cta-button {
  font-family: var(--font-headings);
  font-weight: 700;
}

/* Small Text */
small, .text-sm {
  font-size: 0.875rem; /* 14px */
  line-height: 1.4;
}

.text-xs {
  font-size: 0.75rem; /* 12px */
  line-height: 1.3;
}

/* Large Text */
.text-lg {
  font-size: 1.125rem; /* 18px */
  line-height: 1.6;
}

.text-xl {
  font-size: 1.25rem; /* 20px */
  line-height: 1.5;
}

/* Font Weights */
.font-regular {
  font-weight: 400;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.font-bold {
  font-weight: 700;
}

.font-heavy {
  font-weight: 800;
}

/* Letter Spacing */
.tracking-tight {
  letter-spacing: -0.02em;
}

.tracking-normal {
  letter-spacing: normal;
}

/* Line Heights */
.leading-tight {
  line-height: 1.2;
}

.leading-snug {
  line-height: 1.3;
}

.leading-normal {
  line-height: 1.5;
}

.leading-relaxed {
  line-height: 1.6;
}

/* Dark Mode Adjustments */
@media (prefers-color-scheme: dark) {
  body, p {
    font-weight: 500; /* Slightly heavier for better readability on dark backgrounds */
  }
}
