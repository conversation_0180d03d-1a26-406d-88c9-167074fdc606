import { NextRequest, NextResponse } from 'next/server'
import { setCustomerMetafields } from '@/actions/shopifyActions'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { customerId, metafields } = body

    // Validate required fields
    if (!customerId) {
      return NextResponse.json(
        { success: false, error: 'Customer ID is required' },
        { status: 400 }
      )
    }

    if (!metafields || !Array.isArray(metafields) || metafields.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Metafields array is required and must not be empty' },
        { status: 400 }
      )
    }

    console.log('🔄 Updating customer metafields:', customerId, 'with data:', metafields)

    // Validate metafield structure
    for (const metafield of metafields) {
      if (!metafield.namespace || !metafield.key || metafield.value === undefined || !metafield.type) {
        return NextResponse.json(
          { success: false, error: 'Each metafield must have namespace, key, value, and type' },
          { status: 400 }
        )
      }
    }

    // Update metafields using the shopifyActions function
    const result = await setCustomerMetafields(customerId, metafields)

    if (result.success) {
      console.log('✅ Customer metafields updated successfully')
      return NextResponse.json({
        success: true,
        message: 'Customer metafields updated successfully',
        metafields: result.metafields
      })
    } else {
      console.error('❌ Failed to update customer metafields:', result.error)
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      )
    }

  } catch (error) {
    console.error('Error in update-customer-metafields API:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
