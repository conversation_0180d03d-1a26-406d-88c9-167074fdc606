// Debug script to test cart sync functionality
// Run this in browser console to debug cart sync issues

async function debugCartSync() {
  console.log("🔍 Debugging Cart Sync Functionality");
  console.log("=====================================");
  
  // Check localStorage data
  const userDataRaw = localStorage.getItem("benzochem_user");
  const shopifyToken = localStorage.getItem("shopify_customer_access_token");
  const shopifyTokenExpires = localStorage.getItem("shopify_customer_access_token_expires");
  
  console.log("📦 localStorage Data:");
  console.log("  benzochem_user:", userDataRaw ? "✅ EXISTS" : "❌ NOT FOUND");
  console.log("  shopify_customer_access_token:", shopifyToken ? "✅ EXISTS" : "❌ NOT FOUND");
  console.log("  shopify_customer_access_token_expires:", shopifyTokenExpires);
  
  if (!userDataRaw) {
    console.log("❌ No user data found. User needs to be logged in.");
    return;
  }
  
  let userData;
  try {
    userData = JSON.parse(userDataRaw);
    console.log("👤 User Data Structure:");
    console.log("  Email:", userData.email);
    console.log("  First Name:", userData.firstName);
    console.log("  Last Name:", userData.lastName);
    console.log("  Name:", userData.name);
    console.log("  Phone:", userData.phone);
    console.log("  Shopify Customer ID:", userData.shopifyCustomerId || "NOT SET");
  } catch (e) {
    console.log("❌ Error parsing user data:", e);
    return;
  }
  
  // Test customer creation
  console.log("\n🧪 Testing Customer Creation");
  console.log("=============================");
  
  const testPassword = prompt("Enter a test password to try customer creation:");
  if (!testPassword) {
    console.log("❌ Password required for testing");
    return;
  }
  
  try {
    const customerInput = {
      email: userData.email,
      password: testPassword,
      firstName: userData.firstName || userData.first_name || 'Test',
      lastName: userData.lastName || userData.last_name || 'User',
      phone: userData.phone || '',
      acceptsMarketing: false
    };
    
    console.log("📤 Sending customer creation request with input:", {
      ...customerInput,
      password: '[REDACTED]'
    });
    
    const response = await fetch('https://benzochem.myshopify.com/api/2025-04/graphql.json', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Storefront-Access-Token': '95029391ce9ba8bad8a604e7faf7eb87'
      },
      body: JSON.stringify({
        query: `
          mutation customerCreate($input: CustomerCreateInput!) {
            customerCreate(input: $input) {
              customer {
                id
                email
                firstName
                lastName
              }
              customerUserErrors {
                field
                message
                code
              }
            }
          }
        `,
        variables: {
          input: customerInput
        }
      })
    });
    
    const result = await response.json();
    console.log("📥 Customer creation response:", result);
    
    if (result.data?.customerCreate?.customer) {
      console.log("✅ Customer created successfully:", result.data.customerCreate.customer.id);
    } else if (result.data?.customerCreate?.customerUserErrors) {
      console.log("❌ Customer creation errors:");
      result.data.customerCreate.customerUserErrors.forEach((error, index) => {
        console.log(`  Error ${index + 1}:`, {
          code: error.code,
          field: error.field,
          message: error.message
        });
      });
    } else {
      console.log("❓ Unexpected response structure");
    }
    
  } catch (error) {
    console.log("❌ Error during customer creation test:", error);
  }
  
  // Test authentication
  console.log("\n🔐 Testing Authentication");
  console.log("=========================");
  
  try {
    const authResponse = await fetch('https://benzochem.myshopify.com/api/2025-04/graphql.json', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Storefront-Access-Token': '95029391ce9ba8bad8a604e7faf7eb87'
      },
      body: JSON.stringify({
        query: `
          mutation customerAccessTokenCreate($input: CustomerAccessTokenCreateInput!) {
            customerAccessTokenCreate(input: $input) {
              customerAccessToken {
                accessToken
                expiresAt
              }
              customerUserErrors {
                field
                message
                code
              }
            }
          }
        `,
        variables: {
          input: {
            email: userData.email,
            password: testPassword
          }
        }
      })
    });
    
    const authResult = await authResponse.json();
    console.log("📥 Authentication response:", authResult);
    
    if (authResult.data?.customerAccessTokenCreate?.customerAccessToken) {
      console.log("✅ Authentication successful!");
    } else if (authResult.data?.customerAccessTokenCreate?.customerUserErrors) {
      console.log("❌ Authentication errors:");
      authResult.data.customerAccessTokenCreate.customerUserErrors.forEach((error, index) => {
        console.log(`  Error ${index + 1}:`, {
          code: error.code,
          field: error.field,
          message: error.message
        });
      });
    }
    
  } catch (error) {
    console.log("❌ Error during authentication test:", error);
  }
}

// Run the debug function
debugCartSync();
