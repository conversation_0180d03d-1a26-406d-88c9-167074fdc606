"use client"

import { motion } from "framer-motion"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { useCart } from "@/hooks/use-cart" // Added
import { Skeleton } from "@/components/ui/skeleton" // Added

// Skeleton for the CartSummary component
function CartSummarySkeleton() {
  return (
    <div className="bg-white border rounded-lg overflow-hidden sticky top-24">
      <div className="p-6 border-b">
        <Skeleton className="h-6 w-3/4 rounded" /> {/* "Order Summary" Header */}
      </div>
      <div className="p-6 space-y-4">
        <div className="flex justify-between">
          <Skeleton className="h-5 w-1/3 rounded" /> {/* Subtotal Label */}
          <Skeleton className="h-5 w-1/4 rounded" /> {/* Subtotal Value */}
        </div>
        <div className="flex justify-between">
          <Skeleton className="h-5 w-1/3 rounded" /> {/* Shipping Label */}
          <Skeleton className="h-5 w-1/4 rounded" /> {/* Shipping Value */}
        </div>
        <div className="flex justify-between">
          <Skeleton className="h-5 w-1/3 rounded" /> {/* Tax Label */}
          <Skeleton className="h-5 w-1/4 rounded" /> {/* Tax Value */}
        </div>
        <div className="border-t pt-4 flex justify-between">
          <Skeleton className="h-6 w-1/3 rounded" /> {/* Total Label */}
          <Skeleton className="h-6 w-1/4 rounded" /> {/* Total Value */}
        </div>
      </div>
      <div className="p-6 bg-neutral-50">
        <Skeleton className="h-10 w-full rounded-md" /> {/* Checkout Button */}
        <Skeleton className="h-3 w-3/4 mx-auto mt-4 rounded" /> {/* Secure checkout text line 1 */}
        <Skeleton className="h-3 w-2/3 mx-auto mt-1 rounded" /> {/* Secure checkout text line 2 */}
      </div>
    </div>
  )
}

export default function CartSummary() {
  const { subtotal, isLoading, itemCount, checkoutUrl } = useCart() // Added checkoutUrl

  // Note: Shopify's cart object (via checkoutUrl) usually includes calculated totals,
  // including taxes and shipping if configured in Shopify.
  // The subtotal from useCart is the sum of line item prices.
  // For a more accurate total that matches Shopify's checkout,
  // you might eventually want to display totals directly from the cart object if available,
  // or rely on Shopify's checkout page for the final confirmed amounts.
  // These local calculations are for display purposes on the cart page.
  const shipping = itemCount > 0 ? 25.00 : 0; // Example: Replace with dynamic shipping logic if needed
  const taxRate = 0.08; // Example: 8%
  const tax = itemCount > 0 ? subtotal * taxRate : 0;
  const total = itemCount > 0 ? subtotal + shipping + tax : 0;

  if (isLoading && itemCount === 0) { // Show skeleton if loading and no items yet (initial load)
    return <CartSummarySkeleton />
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.2 }} // Keep delay for staggered effect if CartItems loads first
      className="bg-white border rounded-lg overflow-hidden sticky top-24"
    >
      <div className="p-6 border-b">
        <h2 className="text-xl font-medium">Order Summary</h2>
      </div>

      <div className="p-6 space-y-4">
        <div className="flex justify-between">
          <span className="text-neutral-600">Subtotal</span>
          <span className="font-medium">₹{subtotal.toFixed(2)}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-neutral-600">Shipping</span>
          <span className="font-medium">₹{shipping.toFixed(2)}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-neutral-600">Tax ({(taxRate * 100).toFixed(0)}%)</span>
          <span className="font-medium">₹{tax.toFixed(2)}</span>
        </div>
        <div className="border-t pt-4 flex justify-between">
          <span className="text-xl font-semibold">Total</span>
          <span className="text-xl font-semibold">₹{total.toFixed(2)}</span>
        </div>
      </div>

      <div className="p-6 bg-neutral-50">
        {itemCount > 0 && checkoutUrl ? (
          <Button asChild className="w-full bg-teal-600 hover:bg-teal-700 text-white">
            <Link href={checkoutUrl}>Proceed to Checkout</Link>
          </Button>
        ) : (
          <Button className="w-full" disabled>
            {itemCount === 0 ? "Cart is Empty" : "Checkout Not Available"}
          </Button>
        )}
        <p className="text-xs text-neutral-500 text-center mt-4">
          Secure checkout. Your payment information is encrypted and secure.
        </p>
      </div>
    </motion.div>
  )
}
