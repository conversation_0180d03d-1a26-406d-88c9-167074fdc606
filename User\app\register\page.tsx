import type { Metadata } from "next"
import ShopifyRegisterForm from "@/components/shopify-register-form"
import Link from "next/link"
import Image from "next/image"

export const metadata: Metadata = {
  title: "Register | Benzochem Industries",
  description: "Create a new account with Benzochem Industries",
}

export default function RegisterPage() {
  return (
    <main className="flex min-h-screen flex-col pt-16 bg-[#FAFAFA]">
      <section className="flex-1 flex items-stretch overflow-hidden">
        <div className="w-full max-w-6xl mx-auto my-8 rounded-2xl shadow-xl overflow-hidden flex bg-white">
          {/* Left side - Image and branding */}
          <div className="hidden md:block w-1/2 bg-gradient-to-br from-teal-600 to-teal-800 p-8 text-white relative overflow-hidden">
            <div className="absolute inset-0 opacity-50">
              <Image
                src="/images/register.png"
                alt="Chemistry laboratory"
                fill
                className="object-cover"
                priority
              />
            </div>

            <div className="relative z-10 h-full flex flex-col p-8">
              <div className="mt-auto">
                <h3 className="text-3xl md:text-4xl font-light mb-4">Join Our Network<br />of Trusted Partners</h3>
                <p className="text-teal-100 opacity-80 max-w-md">
                  Create an account to access exclusive pricing, streamlined ordering, and dedicated support for your business.
                </p>

                <div className="mt-8 flex space-x-2">
                  <span className="w-2 h-2 rounded-full bg-white opacity-60"></span>
                  <span className="w-6 h-2 rounded-full bg-white"></span>
                  <span className="w-2 h-2 rounded-full bg-white opacity-60"></span>
                </div>
              </div>
            </div>
          </div>

          {/* Right side - Register form */}
          <div className="w-full md:w-1/2 p-6 md:p-8 flex flex-col">
            <div className="flex-1 overflow-y-auto">
              <ShopifyRegisterForm />
            </div>

            <div className="mt-6 text-center">
              <p className="text-xs text-gray-600 leading-relaxed">
                By creating an account, you agree to our{" "}
                <Link href="/terms" className="text-teal-600 hover:underline font-medium">
                  Terms of Service
                </Link>{" "}
                and{" "}
                <Link href="/privacy" className="text-teal-600 hover:underline font-medium">
                  Privacy Policy
                </Link>
              </p>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}
