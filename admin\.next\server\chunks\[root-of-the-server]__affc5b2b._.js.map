{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/app/api/v1/docs/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\n/**\n * GET /api/v1/docs\n * Interactive API documentation\n */\nexport async function GET(request: NextRequest) {\n  try {\n    const url = new URL(request.url);\n    const format = url.searchParams.get('format') || 'json';\n\n    if (format === 'openapi' || format === 'swagger') {\n      // Return OpenAPI/Swagger specification\n      const openApiSpec = {\n        openapi: '3.0.0',\n        info: {\n          title: 'Benzochem Industries API',\n          version: '1.0.0',\n          description: 'Professional API for chemical products and collections management',\n          contact: {\n            name: 'API Support',\n            email: '<EMAIL>',\n            url: 'https://docs.benzochem.com'\n          },\n          license: {\n            name: 'Proprietary',\n            url: 'https://benzochem.com/terms'\n          }\n        },\n        servers: [\n          {\n            url: 'https://admin.benzochem.com/api/v1',\n            description: 'Production server'\n          },\n          {\n            url: 'http://localhost:3000/api/v1',\n            description: 'Development server'\n          }\n        ],\n        security: [\n          {\n            BearerAuth: []\n          },\n          {\n            ApiKeyAuth: []\n          }\n        ],\n        components: {\n          securitySchemes: {\n            BearerAuth: {\n              type: 'http',\n              scheme: 'bearer',\n              bearerFormat: 'API Key',\n              description: 'Use your Benzochem API key as a bearer token'\n            },\n            ApiKeyAuth: {\n              type: 'apiKey',\n              in: 'header',\n              name: 'X-API-Key',\n              description: 'Use your Benzochem API key in the X-API-Key header'\n            }\n          },\n          schemas: {\n            Product: {\n              type: 'object',\n              properties: {\n                id: { type: 'string', example: 'prod_123' },\n                title: { type: 'string', example: 'Sodium Chloride' },\n                description: { type: 'string', example: 'High purity sodium chloride' },\n                casNumber: { type: 'string', example: '7647-14-5' },\n                purity: { type: 'string', example: '99.9%' },\n                priceRange: {\n                  type: 'object',\n                  properties: {\n                    minVariantPrice: {\n                      type: 'object',\n                      properties: {\n                        amount: { type: 'string', example: '25.00' },\n                        currencyCode: { type: 'string', example: 'USD' }\n                      }\n                    }\n                  }\n                }\n              }\n            },\n            Collection: {\n              type: 'object',\n              properties: {\n                id: { type: 'string', example: 'coll_123' },\n                title: { type: 'string', example: 'Laboratory Chemicals' },\n                description: { type: 'string', example: 'High-grade chemicals for laboratory use' },\n                handle: { type: 'string', example: 'laboratory-chemicals' },\n                status: { type: 'string', enum: ['active', 'inactive'] },\n                isVisible: { type: 'boolean', example: true }\n              }\n            },\n            Error: {\n              type: 'object',\n              properties: {\n                success: { type: 'boolean', example: false },\n                error: { type: 'string', example: 'Error message' },\n                code: { type: 'string', example: 'ERROR_CODE' }\n              }\n            }\n          }\n        },\n        paths: {\n          '/products': {\n            get: {\n              summary: 'List products',\n              description: 'Retrieve a list of chemical products with optional filtering',\n              security: [{ BearerAuth: [] }, { ApiKeyAuth: [] }],\n              parameters: [\n                {\n                  name: 'limit',\n                  in: 'query',\n                  description: 'Number of products to return (max 100)',\n                  schema: { type: 'integer', minimum: 1, maximum: 100, default: 50 }\n                },\n                {\n                  name: 'offset',\n                  in: 'query',\n                  description: 'Number of products to skip',\n                  schema: { type: 'integer', minimum: 0, default: 0 }\n                },\n                {\n                  name: 'search',\n                  in: 'query',\n                  description: 'Search term for product title or description',\n                  schema: { type: 'string' }\n                },\n                {\n                  name: 'status',\n                  in: 'query',\n                  description: 'Filter by product status',\n                  schema: { type: 'string', enum: ['active', 'inactive', 'discontinued'] }\n                },\n                {\n                  name: 'featured',\n                  in: 'query',\n                  description: 'Filter by featured status',\n                  schema: { type: 'boolean' }\n                }\n              ],\n              responses: {\n                '200': {\n                  description: 'Successful response',\n                  content: {\n                    'application/json': {\n                      schema: {\n                        type: 'object',\n                        properties: {\n                          success: { type: 'boolean', example: true },\n                          data: {\n                            type: 'array',\n                            items: { $ref: '#/components/schemas/Product' }\n                          },\n                          pagination: {\n                            type: 'object',\n                            properties: {\n                              limit: { type: 'integer' },\n                              offset: { type: 'integer' },\n                              hasMore: { type: 'boolean' }\n                            }\n                          }\n                        }\n                      }\n                    }\n                  }\n                },\n                '401': {\n                  description: 'Unauthorized',\n                  content: {\n                    'application/json': {\n                      schema: { $ref: '#/components/schemas/Error' }\n                    }\n                  }\n                },\n                '429': {\n                  description: 'Rate limit exceeded',\n                  content: {\n                    'application/json': {\n                      schema: { $ref: '#/components/schemas/Error' }\n                    }\n                  }\n                }\n              }\n            },\n            post: {\n              summary: 'Create product',\n              description: 'Create a new chemical product',\n              security: [{ BearerAuth: [] }, { ApiKeyAuth: [] }],\n              requestBody: {\n                required: true,\n                content: {\n                  'application/json': {\n                    schema: {\n                      type: 'object',\n                      required: ['title', 'description', 'priceRange'],\n                      properties: {\n                        title: { type: 'string', example: 'Sodium Chloride' },\n                        description: { type: 'string', example: 'High purity sodium chloride' },\n                        casNumber: { type: 'string', example: '7647-14-5' },\n                        purity: { type: 'string', example: '99.9%' },\n                        priceRange: {\n                          type: 'object',\n                          properties: {\n                            minVariantPrice: {\n                              type: 'object',\n                              properties: {\n                                amount: { type: 'string', example: '25.00' },\n                                currencyCode: { type: 'string', example: 'USD' }\n                              }\n                            }\n                          }\n                        }\n                      }\n                    }\n                  }\n                }\n              },\n              responses: {\n                '201': {\n                  description: 'Product created successfully',\n                  content: {\n                    'application/json': {\n                      schema: {\n                        type: 'object',\n                        properties: {\n                          success: { type: 'boolean', example: true },\n                          data: {\n                            type: 'object',\n                            properties: {\n                              id: { type: 'string', example: 'prod_123' },\n                              message: { type: 'string', example: 'Product created successfully' }\n                            }\n                          }\n                        }\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          },\n          '/collections': {\n            get: {\n              summary: 'List collections',\n              description: 'Retrieve a list of product collections',\n              security: [{ BearerAuth: [] }, { ApiKeyAuth: [] }],\n              responses: {\n                '200': {\n                  description: 'Successful response',\n                  content: {\n                    'application/json': {\n                      schema: {\n                        type: 'object',\n                        properties: {\n                          success: { type: 'boolean', example: true },\n                          data: {\n                            type: 'array',\n                            items: { $ref: '#/components/schemas/Collection' }\n                          }\n                        }\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          },\n          '/status': {\n            get: {\n              summary: 'API status',\n              description: 'Get API status and information (no authentication required)',\n              responses: {\n                '200': {\n                  description: 'API status information',\n                  content: {\n                    'application/json': {\n                      schema: {\n                        type: 'object',\n                        properties: {\n                          success: { type: 'boolean', example: true },\n                          data: {\n                            type: 'object',\n                            properties: {\n                              status: { type: 'string', example: 'operational' },\n                              version: { type: 'string', example: '1.0.0' },\n                              timestamp: { type: 'number' }\n                            }\n                          }\n                        }\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        }\n      };\n\n      return NextResponse.json(openApiSpec);\n    }\n\n    // Default JSON documentation\n    const documentation = {\n      success: true,\n      data: {\n        title: 'Benzochem Industries API Documentation',\n        version: '1.0.0',\n        description: 'Professional API for chemical products and collections management',\n        baseUrl: {\n          production: 'https://admin.benzochem.com/api/v1',\n          development: 'http://localhost:3000/api/v1'\n        },\n        authentication: {\n          type: 'API Key',\n          formats: [\n            'Authorization: Bearer <api_key>',\n            'X-API-Key: <api_key>'\n          ],\n          keyFormat: 'bzk_[environment]_[32_chars][4_checksum]',\n          environments: ['live', 'test']\n        },\n        endpoints: {\n          '/status': {\n            method: 'GET',\n            description: 'Get API status (no auth required)',\n            authentication: false\n          },\n          '/products': {\n            methods: ['GET', 'POST'],\n            description: 'Manage chemical products',\n            permissions: ['products:read', 'products:write']\n          },\n          '/collections': {\n            methods: ['GET', 'POST'],\n            description: 'Manage product collections',\n            permissions: ['collections:read', 'collections:write']\n          },\n          '/analytics/overview': {\n            method: 'GET',\n            description: 'Get analytics overview',\n            permissions: ['analytics:read']\n          },\n          '/webhooks': {\n            methods: ['GET', 'POST'],\n            description: 'Manage webhooks',\n            permissions: ['webhooks:read', 'webhooks:write']\n          }\n        },\n        rateLimits: {\n          standard: { perMinute: 100, perHour: 5000, perDay: 50000 },\n          premium: { perMinute: 500, perHour: 25000, perDay: 250000 },\n          enterprise: { perMinute: 2000, perHour: 100000, perDay: 1000000 }\n        },\n        examples: {\n          curl: 'curl -H \"Authorization: Bearer <your_api_key>\" https://admin.benzochem.com/api/v1/products',\n          javascript: 'fetch(\"/api/v1/products\", { headers: { \"Authorization\": \"Bearer <your_api_key>\" } })',\n          python: 'requests.get(\"/api/v1/products\", headers={\"Authorization\": \"Bearer <your_api_key>\"})'\n        },\n        links: {\n          fullDocumentation: '/API_DOCUMENTATION.md',\n          openApiSpec: '/api/v1/docs?format=openapi',\n          support: '<EMAIL>'\n        }\n      }\n    };\n\n    return NextResponse.json(documentation);\n\n  } catch (error) {\n    console.error('Documentation API error:', error);\n    return NextResponse.json(\n      { \n        success: false, \n        error: 'Failed to load documentation',\n        code: 'INTERNAL_ERROR'\n      },\n      { status: 500 }\n    );\n  }\n}\n\n/**\n * OPTIONS handler for CORS\n */\nexport async function OPTIONS(request: NextRequest) {\n  return new NextResponse(null, {\n    status: 200,\n    headers: {\n      'Access-Control-Allow-Origin': '*',\n      'Access-Control-Allow-Methods': 'GET, OPTIONS',\n      'Access-Control-Allow-Headers': 'Content-Type',\n      'Access-Control-Max-Age': '86400',\n    },\n  });\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAMO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;QAC/B,MAAM,SAAS,IAAI,YAAY,CAAC,GAAG,CAAC,aAAa;QAEjD,IAAI,WAAW,aAAa,WAAW,WAAW;YAChD,uCAAuC;YACvC,MAAM,cAAc;gBAClB,SAAS;gBACT,MAAM;oBACJ,OAAO;oBACP,SAAS;oBACT,aAAa;oBACb,SAAS;wBACP,MAAM;wBACN,OAAO;wBACP,KAAK;oBACP;oBACA,SAAS;wBACP,MAAM;wBACN,KAAK;oBACP;gBACF;gBACA,SAAS;oBACP;wBACE,KAAK;wBACL,aAAa;oBACf;oBACA;wBACE,KAAK;wBACL,aAAa;oBACf;iBACD;gBACD,UAAU;oBACR;wBACE,YAAY,EAAE;oBAChB;oBACA;wBACE,YAAY,EAAE;oBAChB;iBACD;gBACD,YAAY;oBACV,iBAAiB;wBACf,YAAY;4BACV,MAAM;4BACN,QAAQ;4BACR,cAAc;4BACd,aAAa;wBACf;wBACA,YAAY;4BACV,MAAM;4BACN,IAAI;4BACJ,MAAM;4BACN,aAAa;wBACf;oBACF;oBACA,SAAS;wBACP,SAAS;4BACP,MAAM;4BACN,YAAY;gCACV,IAAI;oCAAE,MAAM;oCAAU,SAAS;gCAAW;gCAC1C,OAAO;oCAAE,MAAM;oCAAU,SAAS;gCAAkB;gCACpD,aAAa;oCAAE,MAAM;oCAAU,SAAS;gCAA8B;gCACtE,WAAW;oCAAE,MAAM;oCAAU,SAAS;gCAAY;gCAClD,QAAQ;oCAAE,MAAM;oCAAU,SAAS;gCAAQ;gCAC3C,YAAY;oCACV,MAAM;oCACN,YAAY;wCACV,iBAAiB;4CACf,MAAM;4CACN,YAAY;gDACV,QAAQ;oDAAE,MAAM;oDAAU,SAAS;gDAAQ;gDAC3C,cAAc;oDAAE,MAAM;oDAAU,SAAS;gDAAM;4CACjD;wCACF;oCACF;gCACF;4BACF;wBACF;wBACA,YAAY;4BACV,MAAM;4BACN,YAAY;gCACV,IAAI;oCAAE,MAAM;oCAAU,SAAS;gCAAW;gCAC1C,OAAO;oCAAE,MAAM;oCAAU,SAAS;gCAAuB;gCACzD,aAAa;oCAAE,MAAM;oCAAU,SAAS;gCAA0C;gCAClF,QAAQ;oCAAE,MAAM;oCAAU,SAAS;gCAAuB;gCAC1D,QAAQ;oCAAE,MAAM;oCAAU,MAAM;wCAAC;wCAAU;qCAAW;gCAAC;gCACvD,WAAW;oCAAE,MAAM;oCAAW,SAAS;gCAAK;4BAC9C;wBACF;wBACA,OAAO;4BACL,MAAM;4BACN,YAAY;gCACV,SAAS;oCAAE,MAAM;oCAAW,SAAS;gCAAM;gCAC3C,OAAO;oCAAE,MAAM;oCAAU,SAAS;gCAAgB;gCAClD,MAAM;oCAAE,MAAM;oCAAU,SAAS;gCAAa;4BAChD;wBACF;oBACF;gBACF;gBACA,OAAO;oBACL,aAAa;wBACX,KAAK;4BACH,SAAS;4BACT,aAAa;4BACb,UAAU;gCAAC;oCAAE,YAAY,EAAE;gCAAC;gCAAG;oCAAE,YAAY,EAAE;gCAAC;6BAAE;4BAClD,YAAY;gCACV;oCACE,MAAM;oCACN,IAAI;oCACJ,aAAa;oCACb,QAAQ;wCAAE,MAAM;wCAAW,SAAS;wCAAG,SAAS;wCAAK,SAAS;oCAAG;gCACnE;gCACA;oCACE,MAAM;oCACN,IAAI;oCACJ,aAAa;oCACb,QAAQ;wCAAE,MAAM;wCAAW,SAAS;wCAAG,SAAS;oCAAE;gCACpD;gCACA;oCACE,MAAM;oCACN,IAAI;oCACJ,aAAa;oCACb,QAAQ;wCAAE,MAAM;oCAAS;gCAC3B;gCACA;oCACE,MAAM;oCACN,IAAI;oCACJ,aAAa;oCACb,QAAQ;wCAAE,MAAM;wCAAU,MAAM;4CAAC;4CAAU;4CAAY;yCAAe;oCAAC;gCACzE;gCACA;oCACE,MAAM;oCACN,IAAI;oCACJ,aAAa;oCACb,QAAQ;wCAAE,MAAM;oCAAU;gCAC5B;6BACD;4BACD,WAAW;gCACT,OAAO;oCACL,aAAa;oCACb,SAAS;wCACP,oBAAoB;4CAClB,QAAQ;gDACN,MAAM;gDACN,YAAY;oDACV,SAAS;wDAAE,MAAM;wDAAW,SAAS;oDAAK;oDAC1C,MAAM;wDACJ,MAAM;wDACN,OAAO;4DAAE,MAAM;wDAA+B;oDAChD;oDACA,YAAY;wDACV,MAAM;wDACN,YAAY;4DACV,OAAO;gEAAE,MAAM;4DAAU;4DACzB,QAAQ;gEAAE,MAAM;4DAAU;4DAC1B,SAAS;gEAAE,MAAM;4DAAU;wDAC7B;oDACF;gDACF;4CACF;wCACF;oCACF;gCACF;gCACA,OAAO;oCACL,aAAa;oCACb,SAAS;wCACP,oBAAoB;4CAClB,QAAQ;gDAAE,MAAM;4CAA6B;wCAC/C;oCACF;gCACF;gCACA,OAAO;oCACL,aAAa;oCACb,SAAS;wCACP,oBAAoB;4CAClB,QAAQ;gDAAE,MAAM;4CAA6B;wCAC/C;oCACF;gCACF;4BACF;wBACF;wBACA,MAAM;4BACJ,SAAS;4BACT,aAAa;4BACb,UAAU;gCAAC;oCAAE,YAAY,EAAE;gCAAC;gCAAG;oCAAE,YAAY,EAAE;gCAAC;6BAAE;4BAClD,aAAa;gCACX,UAAU;gCACV,SAAS;oCACP,oBAAoB;wCAClB,QAAQ;4CACN,MAAM;4CACN,UAAU;gDAAC;gDAAS;gDAAe;6CAAa;4CAChD,YAAY;gDACV,OAAO;oDAAE,MAAM;oDAAU,SAAS;gDAAkB;gDACpD,aAAa;oDAAE,MAAM;oDAAU,SAAS;gDAA8B;gDACtE,WAAW;oDAAE,MAAM;oDAAU,SAAS;gDAAY;gDAClD,QAAQ;oDAAE,MAAM;oDAAU,SAAS;gDAAQ;gDAC3C,YAAY;oDACV,MAAM;oDACN,YAAY;wDACV,iBAAiB;4DACf,MAAM;4DACN,YAAY;gEACV,QAAQ;oEAAE,MAAM;oEAAU,SAAS;gEAAQ;gEAC3C,cAAc;oEAAE,MAAM;oEAAU,SAAS;gEAAM;4DACjD;wDACF;oDACF;gDACF;4CACF;wCACF;oCACF;gCACF;4BACF;4BACA,WAAW;gCACT,OAAO;oCACL,aAAa;oCACb,SAAS;wCACP,oBAAoB;4CAClB,QAAQ;gDACN,MAAM;gDACN,YAAY;oDACV,SAAS;wDAAE,MAAM;wDAAW,SAAS;oDAAK;oDAC1C,MAAM;wDACJ,MAAM;wDACN,YAAY;4DACV,IAAI;gEAAE,MAAM;gEAAU,SAAS;4DAAW;4DAC1C,SAAS;gEAAE,MAAM;gEAAU,SAAS;4DAA+B;wDACrE;oDACF;gDACF;4CACF;wCACF;oCACF;gCACF;4BACF;wBACF;oBACF;oBACA,gBAAgB;wBACd,KAAK;4BACH,SAAS;4BACT,aAAa;4BACb,UAAU;gCAAC;oCAAE,YAAY,EAAE;gCAAC;gCAAG;oCAAE,YAAY,EAAE;gCAAC;6BAAE;4BAClD,WAAW;gCACT,OAAO;oCACL,aAAa;oCACb,SAAS;wCACP,oBAAoB;4CAClB,QAAQ;gDACN,MAAM;gDACN,YAAY;oDACV,SAAS;wDAAE,MAAM;wDAAW,SAAS;oDAAK;oDAC1C,MAAM;wDACJ,MAAM;wDACN,OAAO;4DAAE,MAAM;wDAAkC;oDACnD;gDACF;4CACF;wCACF;oCACF;gCACF;4BACF;wBACF;oBACF;oBACA,WAAW;wBACT,KAAK;4BACH,SAAS;4BACT,aAAa;4BACb,WAAW;gCACT,OAAO;oCACL,aAAa;oCACb,SAAS;wCACP,oBAAoB;4CAClB,QAAQ;gDACN,MAAM;gDACN,YAAY;oDACV,SAAS;wDAAE,MAAM;wDAAW,SAAS;oDAAK;oDAC1C,MAAM;wDACJ,MAAM;wDACN,YAAY;4DACV,QAAQ;gEAAE,MAAM;gEAAU,SAAS;4DAAc;4DACjD,SAAS;gEAAE,MAAM;gEAAU,SAAS;4DAAQ;4DAC5C,WAAW;gEAAE,MAAM;4DAAS;wDAC9B;oDACF;gDACF;4CACF;wCACF;oCACF;gCACF;4BACF;wBACF;oBACF;gBACF;YACF;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QAC3B;QAEA,6BAA6B;QAC7B,MAAM,gBAAgB;YACpB,SAAS;YACT,MAAM;gBACJ,OAAO;gBACP,SAAS;gBACT,aAAa;gBACb,SAAS;oBACP,YAAY;oBACZ,aAAa;gBACf;gBACA,gBAAgB;oBACd,MAAM;oBACN,SAAS;wBACP;wBACA;qBACD;oBACD,WAAW;oBACX,cAAc;wBAAC;wBAAQ;qBAAO;gBAChC;gBACA,WAAW;oBACT,WAAW;wBACT,QAAQ;wBACR,aAAa;wBACb,gBAAgB;oBAClB;oBACA,aAAa;wBACX,SAAS;4BAAC;4BAAO;yBAAO;wBACxB,aAAa;wBACb,aAAa;4BAAC;4BAAiB;yBAAiB;oBAClD;oBACA,gBAAgB;wBACd,SAAS;4BAAC;4BAAO;yBAAO;wBACxB,aAAa;wBACb,aAAa;4BAAC;4BAAoB;yBAAoB;oBACxD;oBACA,uBAAuB;wBACrB,QAAQ;wBACR,aAAa;wBACb,aAAa;4BAAC;yBAAiB;oBACjC;oBACA,aAAa;wBACX,SAAS;4BAAC;4BAAO;yBAAO;wBACxB,aAAa;wBACb,aAAa;4BAAC;4BAAiB;yBAAiB;oBAClD;gBACF;gBACA,YAAY;oBACV,UAAU;wBAAE,WAAW;wBAAK,SAAS;wBAAM,QAAQ;oBAAM;oBACzD,SAAS;wBAAE,WAAW;wBAAK,SAAS;wBAAO,QAAQ;oBAAO;oBAC1D,YAAY;wBAAE,WAAW;wBAAM,SAAS;wBAAQ,QAAQ;oBAAQ;gBAClE;gBACA,UAAU;oBACR,MAAM;oBACN,YAAY;oBACZ,QAAQ;gBACV;gBACA,OAAO;oBACL,mBAAmB;oBACnB,aAAa;oBACb,SAAS;gBACX;YACF;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAE3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO;YACP,MAAM;QACR,GACA;YAAE,QAAQ;QAAI;IAElB;AACF;AAKO,eAAe,QAAQ,OAAoB;IAChD,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,MAAM;QAC5B,QAAQ;QACR,SAAS;YACP,+BAA+B;YAC/B,gCAAgC;YAChC,gCAAgC;YAChC,0BAA0B;QAC5B;IACF;AACF", "debugId": null}}]}