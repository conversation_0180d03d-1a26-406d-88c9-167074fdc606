{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/convex/_generated/api.js"], "sourcesContent": ["/* eslint-disable */\n/**\n * Generated `api` utility.\n *\n * THIS CODE IS AUTOMATICALLY GENERATED.\n *\n * To regenerate, run `npx convex dev`.\n * @module\n */\n\nimport { anyApi } from \"convex/server\";\n\n/**\n * A utility for referencing Convex functions in your app's API.\n *\n * Usage:\n * ```js\n * const myFunctionReference = api.myModule.myFunction;\n * ```\n */\nexport const api = anyApi;\nexport const internal = anyApi;\n"], "names": [], "mappings": "AAAA,kBAAkB,GAClB;;;;;;;CAOC;;;;AAED;AAAA;;AAUO,MAAM,MAAM,wJAAA,CAAA,SAAM;AAClB,MAAM,WAAW,wJAAA,CAAA,SAAM", "debugId": null}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/lib/session.ts"], "sourcesContent": ["import { SignJWT, jwtVerify } from 'jose';\nimport { cookies } from 'next/headers';\nimport { NextRequest, NextResponse } from 'next/server';\n\n// Session configuration\nconst SESSION_COOKIE_NAME = 'benzochem-admin-session';\nconst CSRF_COOKIE_NAME = 'benzochem-csrf-token';\nconst SESSION_DURATION = 24 * 60 * 60 * 1000; // 24 hours in milliseconds\nconst CSRF_TOKEN_LENGTH = 32;\n\n// Get JWT secret from environment variable\nfunction getJWTSecret(): Uint8Array {\n  const secret = process.env.JWT_SECRET || 'benzochem-admin-jwt-secret-change-in-production';\n  return new TextEncoder().encode(secret);\n}\n\n// Get session encryption key from environment variable\nfunction getSessionKey(): string {\n  return process.env.SESSION_SECRET || 'benzochem-session-secret-must-be-at-least-32-characters-long-change-in-production';\n}\n\n// Admin session data interface\nexport interface AdminSession {\n  adminId: string;\n  email: string;\n  firstName: string;\n  lastName: string;\n  role: 'admin' | 'super_admin';\n  permissions: string[];\n  isActive: boolean;\n  loginTime: number;\n  expiresAt: number;\n}\n\n// CSRF token generation\nexport function generateCSRFToken(): string {\n  const array = new Uint8Array(CSRF_TOKEN_LENGTH);\n  crypto.getRandomValues(array);\n  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');\n}\n\n// Create JWT token for session\nexport async function createSessionToken(adminData: Omit<AdminSession, 'loginTime' | 'expiresAt'>): Promise<string> {\n  const now = Date.now();\n  const expiresAt = now + SESSION_DURATION;\n  \n  const payload: AdminSession = {\n    ...adminData,\n    loginTime: now,\n    expiresAt,\n  };\n\n  const token = await new SignJWT(payload)\n    .setProtectedHeader({ alg: 'HS256' })\n    .setIssuedAt(now / 1000)\n    .setExpirationTime(expiresAt / 1000)\n    .setIssuer('benzochem-admin')\n    .setAudience('benzochem-admin-dashboard')\n    .sign(getJWTSecret());\n\n  return token;\n}\n\n// Verify and decode JWT token\nexport async function verifySessionToken(token: string): Promise<AdminSession | null> {\n  try {\n    console.log('verifySessionToken: Verifying token...');\n    const { payload } = await jwtVerify(token, getJWTSecret(), {\n      issuer: 'benzochem-admin',\n      audience: 'benzochem-admin-dashboard',\n    });\n\n    const session = payload as unknown as AdminSession;\n    console.log('verifySessionToken: Token verified, checking expiration...');\n    console.log('verifySessionToken: Session expires at:', new Date(session.expiresAt));\n    console.log('verifySessionToken: Current time:', new Date());\n\n    // Check if session has expired\n    if (session.expiresAt < Date.now()) {\n      console.log('verifySessionToken: Session has expired');\n      return null;\n    }\n\n    console.log('verifySessionToken: Session is valid');\n    return session;\n  } catch (error) {\n    console.error('Session token verification failed:', error);\n    return null;\n  }\n}\n\n// Set secure session cookie\nexport function setSessionCookie(response: NextResponse, token: string): void {\n  const isProduction = process.env.NODE_ENV === 'production';\n  \n  response.cookies.set(SESSION_COOKIE_NAME, token, {\n    httpOnly: true,\n    secure: isProduction, // Only use secure in production (HTTPS)\n    sameSite: 'lax',\n    maxAge: SESSION_DURATION / 1000, // Convert to seconds\n    path: '/',\n  });\n}\n\n// Set CSRF token cookie\nexport function setCSRFCookie(response: NextResponse, csrfToken: string): void {\n  const isProduction = process.env.NODE_ENV === 'production';\n  \n  response.cookies.set(CSRF_COOKIE_NAME, csrfToken, {\n    httpOnly: false, // CSRF token needs to be accessible to client-side JavaScript\n    secure: isProduction,\n    sameSite: 'lax',\n    maxAge: SESSION_DURATION / 1000,\n    path: '/',\n  });\n}\n\n// Get session from request cookies\nexport async function getSessionFromRequest(request: NextRequest): Promise<AdminSession | null> {\n  const token = request.cookies.get(SESSION_COOKIE_NAME)?.value;\n  console.log('getSessionFromRequest: Token found:', !!token);\n\n  if (!token) {\n    console.log('getSessionFromRequest: No token found');\n    return null;\n  }\n\n  const session = await verifySessionToken(token);\n  console.log('getSessionFromRequest: Session verified:', !!session);\n  return session;\n}\n\n// Get session from server-side cookies\nexport async function getServerSession(): Promise<AdminSession | null> {\n  const cookieStore = cookies();\n  const token = cookieStore.get(SESSION_COOKIE_NAME)?.value;\n  \n  if (!token) {\n    return null;\n  }\n\n  return await verifySessionToken(token);\n}\n\n// Clear session cookies\nexport function clearSessionCookies(response: NextResponse): void {\n  response.cookies.delete(SESSION_COOKIE_NAME);\n  response.cookies.delete(CSRF_COOKIE_NAME);\n}\n\n// Validate CSRF token\nexport function validateCSRFToken(request: NextRequest, providedToken: string): boolean {\n  const cookieToken = request.cookies.get(CSRF_COOKIE_NAME)?.value;\n  return cookieToken === providedToken && cookieToken !== undefined;\n}\n\n// Session cookie configuration for iron-session (alternative approach)\nexport const sessionOptions = {\n  cookieName: SESSION_COOKIE_NAME,\n  password: getSessionKey(),\n  cookieOptions: {\n    secure: process.env.NODE_ENV === 'production',\n    httpOnly: true,\n    sameSite: 'lax' as const,\n    maxAge: SESSION_DURATION / 1000,\n  },\n};\n\n// Type for iron-session\ndeclare module 'iron-session' {\n  interface IronSessionData {\n    admin?: AdminSession;\n    csrfToken?: string;\n  }\n}\n\n// Refresh session token (extend expiration)\nexport async function refreshSessionToken(currentToken: string): Promise<string | null> {\n  const session = await verifySessionToken(currentToken);\n  \n  if (!session) {\n    return null;\n  }\n\n  // Create new token with extended expiration\n  const refreshedSession: Omit<AdminSession, 'loginTime' | 'expiresAt'> = {\n    adminId: session.adminId,\n    email: session.email,\n    firstName: session.firstName,\n    lastName: session.lastName,\n    role: session.role,\n    permissions: session.permissions,\n    isActive: session.isActive,\n  };\n\n  return await createSessionToken(refreshedSession);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AAAA;AACA;;;AAGA,wBAAwB;AACxB,MAAM,sBAAsB;AAC5B,MAAM,mBAAmB;AACzB,MAAM,mBAAmB,KAAK,KAAK,KAAK,MAAM,2BAA2B;AACzE,MAAM,oBAAoB;AAE1B,2CAA2C;AAC3C,SAAS;IACP,MAAM,SAAS,QAAQ,GAAG,CAAC,UAAU,IAAI;IACzC,OAAO,IAAI,cAAc,MAAM,CAAC;AAClC;AAEA,uDAAuD;AACvD,SAAS;IACP,OAAO,QAAQ,GAAG,CAAC,cAAc,IAAI;AACvC;AAgBO,SAAS;IACd,MAAM,QAAQ,IAAI,WAAW;IAC7B,OAAO,eAAe,CAAC;IACvB,OAAO,MAAM,IAAI,CAAC,OAAO,CAAA,OAAQ,KAAK,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG,MAAM,IAAI,CAAC;AAC5E;AAGO,eAAe,mBAAmB,SAAwD;IAC/F,MAAM,MAAM,KAAK,GAAG;IACpB,MAAM,YAAY,MAAM;IAExB,MAAM,UAAwB;QAC5B,GAAG,SAAS;QACZ,WAAW;QACX;IACF;IAEA,MAAM,QAAQ,MAAM,IAAI,uJAAA,CAAA,UAAO,CAAC,SAC7B,kBAAkB,CAAC;QAAE,KAAK;IAAQ,GAClC,WAAW,CAAC,MAAM,MAClB,iBAAiB,CAAC,YAAY,MAC9B,SAAS,CAAC,mBACV,WAAW,CAAC,6BACZ,IAAI,CAAC;IAER,OAAO;AACT;AAGO,eAAe,mBAAmB,KAAa;IACpD,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,CAAA,GAAA,yJAAA,CAAA,YAAS,AAAD,EAAE,OAAO,gBAAgB;YACzD,QAAQ;YACR,UAAU;QACZ;QAEA,MAAM,UAAU;QAChB,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,2CAA2C,IAAI,KAAK,QAAQ,SAAS;QACjF,QAAQ,GAAG,CAAC,qCAAqC,IAAI;QAErD,+BAA+B;QAC/B,IAAI,QAAQ,SAAS,GAAG,KAAK,GAAG,IAAI;YAClC,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;QAEA,QAAQ,GAAG,CAAC;QACZ,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,OAAO;IACT;AACF;AAGO,SAAS,iBAAiB,QAAsB,EAAE,KAAa;IACpE,MAAM,eAAe,oDAAyB;IAE9C,SAAS,OAAO,CAAC,GAAG,CAAC,qBAAqB,OAAO;QAC/C,UAAU;QACV,QAAQ;QACR,UAAU;QACV,QAAQ,mBAAmB;QAC3B,MAAM;IACR;AACF;AAGO,SAAS,cAAc,QAAsB,EAAE,SAAiB;IACrE,MAAM,eAAe,oDAAyB;IAE9C,SAAS,OAAO,CAAC,GAAG,CAAC,kBAAkB,WAAW;QAChD,UAAU;QACV,QAAQ;QACR,UAAU;QACV,QAAQ,mBAAmB;QAC3B,MAAM;IACR;AACF;AAGO,eAAe,sBAAsB,OAAoB;IAC9D,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,sBAAsB;IACxD,QAAQ,GAAG,CAAC,uCAAuC,CAAC,CAAC;IAErD,IAAI,CAAC,OAAO;QACV,QAAQ,GAAG,CAAC;QACZ,OAAO;IACT;IAEA,MAAM,UAAU,MAAM,mBAAmB;IACzC,QAAQ,GAAG,CAAC,4CAA4C,CAAC,CAAC;IAC1D,OAAO;AACT;AAGO,eAAe;IACpB,MAAM,cAAc,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAC1B,MAAM,QAAQ,YAAY,GAAG,CAAC,sBAAsB;IAEpD,IAAI,CAAC,OAAO;QACV,OAAO;IACT;IAEA,OAAO,MAAM,mBAAmB;AAClC;AAGO,SAAS,oBAAoB,QAAsB;IACxD,SAAS,OAAO,CAAC,MAAM,CAAC;IACxB,SAAS,OAAO,CAAC,MAAM,CAAC;AAC1B;AAGO,SAAS,kBAAkB,OAAoB,EAAE,aAAqB;IAC3E,MAAM,cAAc,QAAQ,OAAO,CAAC,GAAG,CAAC,mBAAmB;IAC3D,OAAO,gBAAgB,iBAAiB,gBAAgB;AAC1D;AAGO,MAAM,iBAAiB;IAC5B,YAAY;IACZ,UAAU;IACV,eAAe;QACb,QAAQ,oDAAyB;QACjC,UAAU;QACV,UAAU;QACV,QAAQ,mBAAmB;IAC7B;AACF;AAWO,eAAe,oBAAoB,YAAoB;IAC5D,MAAM,UAAU,MAAM,mBAAmB;IAEzC,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,4CAA4C;IAC5C,MAAM,mBAAkE;QACtE,SAAS,QAAQ,OAAO;QACxB,OAAO,QAAQ,KAAK;QACpB,WAAW,QAAQ,SAAS;QAC5B,UAAU,QAAQ,QAAQ;QAC1B,MAAM,QAAQ,IAAI;QAClB,aAAa,QAAQ,WAAW;QAChC,UAAU,QAAQ,QAAQ;IAC5B;IAEA,OAAO,MAAM,mBAAmB;AAClC", "debugId": null}}, {"offset": {"line": 251, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/app/api/auth/session/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { ConvexHttpClient } from 'convex/browser';\nimport { api } from '../../../../../convex/_generated/api';\nimport { \n  getSessionFromRequest, \n  createSessionToken, \n  setSessionCookie,\n  refreshSessionToken \n} from '@/lib/session';\n\n// Initialize Convex client for server-side operations\nconst convex = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL!);\n\nexport async function GET(request: NextRequest) {\n  try {\n    console.log('Session API: Starting session check');\n\n    // Get session from cookies\n    const session = await getSessionFromRequest(request);\n    console.log('Session API: Session from request:', session ? 'Found' : 'Not found');\n\n    if (!session) {\n      console.log('Session API: No valid session found');\n      return NextResponse.json(\n        { success: false, error: 'No valid session found' },\n        { status: 401 }\n      );\n    }\n\n    // Validate session with Convex (check if admin still exists and is active)\n    console.log('Session API: Validating with Convex, adminId:', session.adminId);\n    const validationResult = await convex.query(api.auth.validateAdminSession, {\n      adminId: session.adminId,\n    });\n    console.log('Session API: Validation result:', validationResult);\n\n    if (!validationResult.valid || !validationResult.admin) {\n      console.log('Session API: Session validation failed');\n      return NextResponse.json(\n        { success: false, error: 'Session is no longer valid' },\n        { status: 401 }\n      );\n    }\n\n    // Check if session needs refresh (if it's more than halfway to expiration)\n    const now = Date.now();\n    const sessionAge = now - session.loginTime;\n    const sessionDuration = session.expiresAt - session.loginTime;\n    const shouldRefresh = sessionAge > sessionDuration / 2;\n\n    let response = NextResponse.json({\n      success: true,\n      admin: {\n        _id: validationResult.admin._id,\n        email: validationResult.admin.email,\n        firstName: validationResult.admin.firstName,\n        lastName: validationResult.admin.lastName,\n        role: validationResult.admin.role,\n        permissions: validationResult.admin.permissions,\n        isActive: validationResult.admin.isActive,\n      },\n      sessionRefreshed: shouldRefresh,\n    });\n\n    // Refresh session if needed\n    if (shouldRefresh) {\n      const currentToken = request.cookies.get('benzochem-admin-session')?.value;\n      if (currentToken) {\n        const refreshedToken = await refreshSessionToken(currentToken);\n        if (refreshedToken) {\n          setSessionCookie(response, refreshedToken);\n        }\n      }\n    }\n\n    return response;\n  } catch (error) {\n    console.error('Session validation API error:', error);\n    return NextResponse.json(\n      { success: false, error: 'Session validation failed' },\n      { status: 500 }\n    );\n  }\n}\n\n// Handle preflight requests for CORS\nexport async function OPTIONS(request: NextRequest) {\n  return new NextResponse(null, {\n    status: 200,\n    headers: {\n      'Access-Control-Allow-Origin': '*',\n      'Access-Control-Allow-Methods': 'GET, OPTIONS',\n      'Access-Control-Allow-Headers': 'Content-Type',\n    },\n  });\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AACA;AACA;;;;;AAOA,sDAAsD;AACtD,MAAM,SAAS,IAAI,iKAAA,CAAA,mBAAgB;AAE5B,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,2BAA2B;QAC3B,MAAM,UAAU,MAAM,CAAA,GAAA,uHAAA,CAAA,wBAAqB,AAAD,EAAE;QAC5C,QAAQ,GAAG,CAAC,sCAAsC,UAAU,UAAU;QAEtE,IAAI,CAAC,SAAS;YACZ,QAAQ,GAAG,CAAC;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAyB,GAClD;gBAAE,QAAQ;YAAI;QAElB;QAEA,2EAA2E;QAC3E,QAAQ,GAAG,CAAC,iDAAiD,QAAQ,OAAO;QAC5E,MAAM,mBAAmB,MAAM,OAAO,KAAK,CAAC,6HAAA,CAAA,MAAG,CAAC,IAAI,CAAC,oBAAoB,EAAE;YACzE,SAAS,QAAQ,OAAO;QAC1B;QACA,QAAQ,GAAG,CAAC,mCAAmC;QAE/C,IAAI,CAAC,iBAAiB,KAAK,IAAI,CAAC,iBAAiB,KAAK,EAAE;YACtD,QAAQ,GAAG,CAAC;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAA6B,GACtD;gBAAE,QAAQ;YAAI;QAElB;QAEA,2EAA2E;QAC3E,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,aAAa,MAAM,QAAQ,SAAS;QAC1C,MAAM,kBAAkB,QAAQ,SAAS,GAAG,QAAQ,SAAS;QAC7D,MAAM,gBAAgB,aAAa,kBAAkB;QAErD,IAAI,WAAW,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAC/B,SAAS;YACT,OAAO;gBACL,KAAK,iBAAiB,KAAK,CAAC,GAAG;gBAC/B,OAAO,iBAAiB,KAAK,CAAC,KAAK;gBACnC,WAAW,iBAAiB,KAAK,CAAC,SAAS;gBAC3C,UAAU,iBAAiB,KAAK,CAAC,QAAQ;gBACzC,MAAM,iBAAiB,KAAK,CAAC,IAAI;gBACjC,aAAa,iBAAiB,KAAK,CAAC,WAAW;gBAC/C,UAAU,iBAAiB,KAAK,CAAC,QAAQ;YAC3C;YACA,kBAAkB;QACpB;QAEA,4BAA4B;QAC5B,IAAI,eAAe;YACjB,MAAM,eAAe,QAAQ,OAAO,CAAC,GAAG,CAAC,4BAA4B;YACrE,IAAI,cAAc;gBAChB,MAAM,iBAAiB,MAAM,CAAA,GAAA,uHAAA,CAAA,sBAAmB,AAAD,EAAE;gBACjD,IAAI,gBAAgB;oBAClB,CAAA,GAAA,uHAAA,CAAA,mBAAgB,AAAD,EAAE,UAAU;gBAC7B;YACF;QACF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAA4B,GACrD;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,QAAQ,OAAoB;IAChD,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,MAAM;QAC5B,QAAQ;QACR,SAAS;YACP,+BAA+B;YAC/B,gCAAgC;YAChC,gCAAgC;QAClC;IACF;AACF", "debugId": null}}]}