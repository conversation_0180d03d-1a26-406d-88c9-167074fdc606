import { storefrontFetch } from "./shopify"

// GraphQL Queries and Mutations
const CUSTOMER_CREATE_MUTATION = `
  mutation customerCreate($input: CustomerCreateInput!) {
    customerCreate(input: $input) {
      customer {
        id
        firstName
        lastName
        email
        phone
      }
      customerUserErrors {
        code
        field
        message
      }
    }
  }
`

const CUSTOMER_ACCESS_TOKEN_CREATE = `
  mutation customerAccessTokenCreate($input: CustomerAccessTokenCreateInput!) {
    customerAccessTokenCreate(input: $input) {
      customerAccessToken {
        accessToken
        expiresAt
      }
      customerUserErrors {
        code
        field
        message
      }
    }
  }
`

const CUSTOMER_QUERY = `
  query getCustomer($customerAccessToken: String!) {
    customer(customerAccessToken: $customerAccessToken) {
      id
      firstName
      lastName
      email
      phone
      acceptsMarketing
      createdAt
      defaultAddress {
        id
        address1
        address2
        city
        province
        country
        zip
        phone
      }
      orders(first: 10) {
        edges {
          node {
            id
            orderNumber
            processedAt
            financialStatus
            fulfillmentStatus
            totalPriceV2 {
              amount
              currencyCode
            }
          }
        }
      }
    }
  }
`

// Separate query for customer metafields (requires specific identifiers in Storefront API)
const CUSTOMER_METAFIELDS_QUERY = `
  query getCustomerMetafields($customerAccessToken: String!, $identifiers: [HasMetafieldsIdentifier!]!) {
    customer(customerAccessToken: $customerAccessToken) {
      id
      metafields(identifiers: $identifiers) {
        namespace
        key
        value
      }
    }
  }
`

// Local storage key for customer access token
const CUSTOMER_ACCESS_TOKEN_KEY = "shopify_customer_access_token"
const LEGACY_CUSTOMER_ACCESS_TOKEN_KEY = "SHOPIFY_CUSTOMER_ACCESS_TOKEN" // Uppercase legacy key

// Get customer access token from local storage
export function getCustomerAccessToken(): string | null {
  if (typeof window === "undefined") return null

  // First try the standard lowercase key
  let token = localStorage.getItem(CUSTOMER_ACCESS_TOKEN_KEY)

  // If not found, try the legacy uppercase key
  if (!token) {
    token = localStorage.getItem(LEGACY_CUSTOMER_ACCESS_TOKEN_KEY)

    // If found in legacy format, migrate it to the new format
    if (token) {
      console.log("🔄 Migrating token from legacy uppercase key to lowercase key")
      localStorage.setItem(CUSTOMER_ACCESS_TOKEN_KEY, token)

      // Try to get expiration from legacy format (if it exists)
      const legacyExpires = localStorage.getItem(`${LEGACY_CUSTOMER_ACCESS_TOKEN_KEY}_EXPIRES`)
      if (legacyExpires) {
        localStorage.setItem(`${CUSTOMER_ACCESS_TOKEN_KEY}_expires`, legacyExpires)
        localStorage.removeItem(`${LEGACY_CUSTOMER_ACCESS_TOKEN_KEY}_EXPIRES`)
      }

      // Clean up legacy key
      localStorage.removeItem(LEGACY_CUSTOMER_ACCESS_TOKEN_KEY)
      console.log("✅ Token migration completed")
    }
  }

  return token
}

// Save customer access token to local storage
export function saveCustomerAccessToken(token: string, expiresAt: string): void {
  if (typeof window === "undefined") return
  localStorage.setItem(CUSTOMER_ACCESS_TOKEN_KEY, token)
  localStorage.setItem(`${CUSTOMER_ACCESS_TOKEN_KEY}_expires`, expiresAt)
}

// Remove customer access token from local storage
export function removeCustomerAccessToken(): void {
  if (typeof window === "undefined") return
  localStorage.removeItem(CUSTOMER_ACCESS_TOKEN_KEY)
  localStorage.removeItem(`${CUSTOMER_ACCESS_TOKEN_KEY}_expires`)
}

// Check if customer access token is valid
export function isCustomerAccessTokenValid(): boolean {
  if (typeof window === "undefined") return false

  // Use the getCustomerAccessToken function which handles migration
  const token = getCustomerAccessToken()
  const expiresAt = localStorage.getItem(`${CUSTOMER_ACCESS_TOKEN_KEY}_expires`)

  if (!token || !expiresAt) return false

  const now = new Date()
  const expiry = new Date(expiresAt)

  return now < expiry
}

// Create a new customer
export async function createCustomer(input: {
  firstName: string
  lastName: string
  email: string
  password: string
  phone?: string
  acceptsMarketing?: boolean
  clerkId?: string
}) {
  try {
    // Create customer input with potential metafields
    const customerInput = {
      ...input,
      // Add metafields if clerkId is provided
      ...(input.clerkId && {
        metafields: [
          {
            namespace: "customer",
            key: "clerk_id",
            value: input.clerkId,
            valueType: "STRING"
          }
        ]
      })
    };
    
    // Remove clerkId from the top level as it's not part of the Shopify API
    if ('clerkId' in customerInput) {
      delete customerInput.clerkId;
    }
    
    const { data } = await storefrontFetch<any>({
      query: CUSTOMER_CREATE_MUTATION,
      variables: { input: customerInput },
    })

    const { customer, customerUserErrors } = data?.customerCreate || {}

    if (customerUserErrors?.length) {
      throw new Error(customerUserErrors[0].message)
    }

    return customer
  } catch (error) {
    console.error("Error creating customer:", error)
    throw error
  }
}

// Login customer and get access token
export async function loginCustomer(email: string, password: string) {
  try {
    const { data } = await storefrontFetch<any>({
      query: CUSTOMER_ACCESS_TOKEN_CREATE,
      variables: {
        input: { email, password },
      },
    })

    const { customerAccessToken, customerUserErrors } = data?.customerAccessTokenCreate || {}

    if (customerUserErrors?.length) {
      const errorMessage = customerUserErrors[0].message
      const errorCode = customerUserErrors[0].code

      // Provide more specific error messages based on error codes
      if (errorCode === "UNIDENTIFIED_CUSTOMER") {
        throw new Error("ACCOUNT_NOT_FOUND: No account found with this email address. Your account may need to be created in our system.")
      } else if (errorCode === "INVALID_CREDENTIALS" || errorCode === "INVALID") {
        throw new Error("INVALID_PASSWORD: The email and password combination is incorrect")
      } else if (errorMessage.toLowerCase().includes("password")) {
        throw new Error("INVALID_PASSWORD: " + errorMessage)
      } else if (errorMessage.toLowerCase().includes("email") ||
                errorMessage.toLowerCase().includes("customer")) {
        throw new Error("ACCOUNT_NOT_FOUND: " + errorMessage)
      } else {
        throw new Error(`AUTHENTICATION_FAILED: ${errorMessage}`)
      }
    }

    if (!customerAccessToken) {
      throw new Error("AUTHENTICATION_FAILED: Unable to create customer access token")
    }

    console.log("Saving customer access token to localStorage...");
    console.log("Token details:", {
      accessToken: customerAccessToken.accessToken ? "✅" : "❌",
      expiresAt: customerAccessToken.expiresAt ? "✅" : "❌"
    });

    saveCustomerAccessToken(customerAccessToken.accessToken, customerAccessToken.expiresAt)

    // Verify token was saved
    const savedToken = getCustomerAccessToken();
    console.log("Token saved successfully:", savedToken ? "✅" : "❌");

    return customerAccessToken
  } catch (error) {
    console.error("Error logging in customer:", error)
    throw error
  }
}

// Get customer metafields
export async function getCustomerMetafields(metafieldIdentifiers: string[] = []) {
  const customerAccessToken = getCustomerAccessToken()

  if (!customerAccessToken) {
    console.log("No customer access token found for metafields - returning empty object");
    return {};
  }

  // Default metafield identifiers for common business data
  const defaultIdentifiers = [
    "customer.business_name",
    "customer.gst_number",
    "customer.is_gst_verified",
    "customer.legal_name_of_business",
    "customer.trade_name",
    "customer.date_of_registration",
    "customer.constitution_of_business",
    "customer.taxpayer_type",
    "customer.principal_place_of_business",
    "customer.nature_of_core_business_activity"
  ]

  const identifiers = metafieldIdentifiers.length > 0 ? metafieldIdentifiers : defaultIdentifiers

  try {
    const { data, errors } = await storefrontFetch<any>({
      query: CUSTOMER_METAFIELDS_QUERY,
      variables: {
        customerAccessToken,
        identifiers: identifiers.map(id => {
          const [namespace, key] = id.split('.')
          return { namespace, key }
        })
      },
    })

    if (errors) {
      console.error("GraphQL errors fetching customer metafields:", errors)
      return {}
    }

    // Convert metafields array to object for easier access
    const metafields = data?.customer?.metafields?.reduce((acc: any, metafield: any) => {
      if (metafield) {
        const key = `${metafield.namespace}.${metafield.key}`
        acc[key] = metafield.value
      }
      return acc
    }, {}) || {}

    return metafields
  } catch (error) {
    console.error("Error fetching customer metafields:", error)
    return {}
  }
}

// Get customer data
export async function getCustomer() {
  console.log("Getting customer access token from localStorage...");
  const customerAccessToken = getCustomerAccessToken()
  console.log("Customer access token found:", customerAccessToken ? "✅" : "❌");

  if (!customerAccessToken) {
    console.log("No customer access token found - user is not authenticated");
    return null;
  }

  try {
    console.log("Fetching customer data from Shopify...");
    const response = await storefrontFetch<any>({
      query: CUSTOMER_QUERY,
      variables: { customerAccessToken },
    })

    console.log("Shopify response received:", response ? "✅" : "❌");
    console.log("Response data:", response?.data ? "✅" : "❌");
    console.log("Response errors:", response?.errors ? "❌" : "✅");

    if (response?.errors) {
      console.error("GraphQL errors:", response.errors);
      throw new Error(`GraphQL errors: ${JSON.stringify(response.errors)}`);
    }

    const { data } = response;
    console.log("Customer in response:", data?.customer ? "✅" : "❌");

    if (data?.customer) {
      console.log("Customer data fields:", {
        id: data.customer.id ? "✅" : "❌",
        email: data.customer.email ? "✅" : "❌",
        firstName: data.customer.firstName ? "✅" : "❌"
      });

      // Fetch metafields separately
      console.log("Fetching customer metafields...");
      const metafields = await getCustomerMetafields();
      console.log("Metafields fetched:", Object.keys(metafields).length > 0 ? "✅" : "❌");

      // Add metafields to customer data in the expected format for backward compatibility
      const customerWithMetafields = {
        ...data.customer,
        metafields: {
          edges: Object.entries(metafields).map(([key, value]) => {
            const [namespace, metaKey] = key.split('.');
            return {
              node: {
                namespace,
                key: metaKey,
                value
              }
            };
          })
        }
      };

      return customerWithMetafields;
    } else if (data?.customer === null) {
      console.warn("Customer is null - this might indicate an invalid or expired access token");
      throw new Error("Customer access token is invalid or expired");
    } else {
      console.warn("No customer data in response:", data);
      throw new Error("No customer data returned from Shopify");
    }
  } catch (error) {
    console.error("Error fetching customer data:", error)
    throw error
  }
}
