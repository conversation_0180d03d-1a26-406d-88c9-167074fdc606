// Test script to verify customer access token and localStorage data
// Run this in the browser console on http://localhost:3000/account

async function testCustomerToken() {
  console.log("🧪 Testing customer access token and localStorage data...");
  
  // Check localStorage data
  const storedUserData = localStorage.getItem("benzochem_user");
  const storedToken = localStorage.getItem("shopify_customer_access_token");
  const storedTokenExpires = localStorage.getItem("shopify_customer_access_token_expires");
  
  console.log("📦 localStorage data:");
  console.log("  benzochem_user:", storedUserData);
  console.log("  shopify_customer_access_token:", storedToken);
  console.log("  shopify_customer_access_token_expires:", storedTokenExpires);
  
  if (storedUserData) {
    try {
      const userData = JSON.parse(storedUserData);
      console.log("👤 Parsed user data:", userData);
    } catch (e) {
      console.error("❌ Error parsing user data:", e);
    }
  }
  
  if (storedToken) {
    console.log("🔑 Testing token with API...");
    try {
      const response = await fetch('/api/test-token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          customerAccessToken: storedToken
        })
      });
      
      const result = await response.json();
      console.log("🧪 API test result:", result);
      
      if (result.success && result.customer) {
        console.log("✅ Token is valid! Customer data:", result.customer);
      } else {
        console.log("❌ Token test failed:", result);
      }
    } catch (error) {
      console.error("❌ Error testing token:", error);
    }
  } else {
    console.log("❌ No customer access token found in localStorage");
  }
}

// Run the test
testCustomerToken();
