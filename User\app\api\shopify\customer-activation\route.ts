import { NextRequest, NextResponse } from 'next/server'
import { getShopifyConfig } from '@/lib/env-validation'

// Customer activation API endpoint using Shopify Storefront API
export async function POST(request: NextRequest) {
  let shopifyConfig;

  try {
    shopifyConfig = getShopifyConfig(true); // Need admin access for customer creation with invitation
    console.log('🔧 Shopify configuration loaded for customer activation');
  } catch (error) {
    console.error("Shopify environment variables are not properly configured:", error);
    return NextResponse.json(
      { error: 'Server configuration error' },
      { status: 500 }
    )
  }

  try {
    // Read the request body only once
    const requestBody = await request.json()
    const { email, action, firstName, lastName, phone, password } = requestBody

    console.log('Customer activation API called with:', { email, action, firstName, lastName, phone: phone ? 'provided' : 'missing' })

    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      )
    }

    if (action === 'recover') {
      // Send password recovery email using Storefront API
      const response = await fetch(`https://${shopifyConfig.storeDomain}/api/${shopifyConfig.apiVersion}/graphql.json`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Shopify-Storefront-Access-Token': shopifyConfig.storefrontAccessToken
        },
        body: JSON.stringify({
          query: `
            mutation customerRecover($email: String!) {
              customerRecover(email: $email) {
                customerUserErrors {
                  field
                  message
                  code
                }
              }
            }
          `,
          variables: { email }
        })
      })

      const result = await response.json()

      if (result.data?.customerRecover?.customerUserErrors?.length === 0) {
        return NextResponse.json({
          success: true,
          message: 'Password recovery email sent successfully'
        })
      } else {
        const errors = result.data?.customerRecover?.customerUserErrors || []
        return NextResponse.json({
          success: false,
          error: 'Failed to send recovery email',
          details: errors
        }, { status: 400 })
      }
    }

    if (action === 'create') {
      // Attempt to create customer (will trigger activation email if needed)
      if (!firstName || !lastName || !password) {
        return NextResponse.json(
          { error: 'firstName, lastName, and password are required for customer creation' },
          { status: 400 }
        )
      }

      // Create customer using Admin REST API to send welcome email
      const response = await fetch(`https://${shopifyConfig.storeDomain}/admin/api/${shopifyConfig.apiVersion}/customers.json`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Shopify-Access-Token': shopifyConfig.adminAccessToken!
        },
        body: JSON.stringify({
          customer: {
            email,
            first_name: firstName || 'User',
            last_name: lastName || 'Customer',
            phone: phone || '',
            accepts_marketing: false,
            verified_email: true, // Mark email as verified
            password: password, // Set password to activate account immediately
            password_confirmation: password,
            send_email_invite: false, // Don't send invitation email
            send_email_welcome: true // Send welcome email instead
          }
        })
      })

      const result = await response.json()
      console.log('Shopify customer creation response:', JSON.stringify(result, null, 2))

      if (result.customer) {
        const customer = result.customer

        return NextResponse.json({
          success: true,
          customer: customer,
          message: 'Account created successfully! Your account is now active. Please check your email for welcome instructions and you can now log in.'
        })
      } else {
        const errors = result.errors || {}

        // Check for specific error types
        if (errors.email && errors.email.includes('has already been taken')) {
          return NextResponse.json({
            success: false,
            error: 'CUSTOMER_EXISTS',
            message: 'An account with this email already exists. Please sign in instead.',
            details: errors
          }, { status: 400 })
        }

        return NextResponse.json({
          success: false,
          error: 'CREATION_FAILED',
          message: 'Failed to create customer account. Please try again.',
          details: errors
        }, { status: 400 })
      }
    }

    return NextResponse.json(
      { error: 'Invalid action. Use "recover" or "create"' },
      { status: 400 }
    )

  } catch (error) {
    console.error('Customer activation API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
