// Quick fix for missing Shopify customer access token
// Run this in browser console to manually get and save the token

async function fixMissingToken() {
  console.log("🔧 Attempting to fix missing Shopify customer access token...");
  
  // Get user data from localStorage
  const userDataRaw = localStorage.getItem("benzochem_user");
  if (!userDataRaw) {
    console.log("❌ No user data found in localStorage");
    return;
  }
  
  let userData;
  try {
    userData = JSON.parse(userDataRaw);
  } catch (e) {
    console.log("❌ Error parsing user data:", e);
    return;
  }
  
  console.log("👤 Found user:", userData.email);
  
  // Prompt for password to get Shopify token
  const password = prompt("Enter your password to get Shopify access token:");
  if (!password) {
    console.log("❌ Password required");
    return;
  }
  
  try {
    // Call Shopify login API directly
    const response = await fetch('https://benzochem.myshopify.com/api/2025-04/graphql.json', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Storefront-Access-Token': '95029391ce9ba8bad8a604e7faf7eb87'
      },
      body: JSON.stringify({
        query: `
          mutation customerAccessTokenCreate($input: CustomerAccessTokenCreateInput!) {
            customerAccessTokenCreate(input: $input) {
              customerAccessToken {
                accessToken
                expiresAt
              }
              customerUserErrors {
                field
                message
              }
            }
          }
        `,
        variables: {
          input: {
            email: userData.email,
            password: password
          }
        }
      })
    });
    
    const result = await response.json();
    console.log("🔍 Shopify API response:", result);
    
    if (result.data?.customerAccessTokenCreate?.customerAccessToken) {
      const token = result.data.customerAccessTokenCreate.customerAccessToken;
      
      // Save token to localStorage
      localStorage.setItem("shopify_customer_access_token", token.accessToken);
      localStorage.setItem("shopify_customer_access_token_expires", token.expiresAt);
      
      console.log("✅ Successfully saved Shopify customer access token!");
      console.log("🎉 You can now try adding items to cart!");
      
      return token;
    } else {
      console.log("❌ Failed to get token:", result.data?.customerAccessTokenCreate?.customerUserErrors);
    }
  } catch (error) {
    console.log("❌ Error calling Shopify API:", error);
  }
}

// Run the fix
fixMissingToken();
