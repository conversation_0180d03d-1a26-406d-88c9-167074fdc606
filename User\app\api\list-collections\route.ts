import { NextResponse } from "next/server"
import { storefrontFetch } from "@/lib/shopify"

export async function GET() {
  try {
    const { data, errors } = await storefrontFetch<any>({
      query: `
        query GetCollections {
          collections(first: 20) {
            edges {
              node {
                id
                title
                handle
                description
              }
            }
          }
        }
      `,
    })

    if (errors) {
      return NextResponse.json({ success: false, errors }, { status: 500 })
    }

    if (!data?.collections?.edges) {
      return NextResponse.json({ success: false, message: "No collections found" }, { status: 404 })
    }

    const collections = data.collections.edges.map((edge: any) => edge.node)

    return NextResponse.json({
      success: true,
      collections,
    })
  } catch (error) {
    console.error("Error listing collections:", error)
    return NextResponse.json(
      { success: false, message: "Error listing collections", error: String(error) },
      { status: 500 },
    )
  }
}
