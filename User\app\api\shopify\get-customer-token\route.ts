import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { email, shopifyCustomerId } = await request.json()

    if (!email || !shopifyCustomerId) {
      return NextResponse.json(
        { error: 'Email and Shopify Customer ID are required' },
        { status: 400 }
      )
    }

    console.log('🔑 Creating customer access token for:', email, 'Customer ID:', shopifyCustomerId)

    // For now, let's create a temporary solution that prompts for password
    // This is a temporary workaround until we implement proper token management
    return NextResponse.json(
      {
        error: 'Password required for token creation',
        requiresPassword: true,
        message: 'Please use the browser console script to manually get the token'
      },
      { status: 400 }
    )

  } catch (error) {
    console.error('Error in get-customer-token API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
