import { getProducts } from "@/lib/product-service"
import FeaturedProductsClient from "./featured-products-client"
import type { Product } from "@/lib/types"

export default async function FeaturedProductsServer() {
  let products: Product[] = []
  let error = null

  try {
    // Fetch featured products from Shopify
    products = await getProducts(true)

    // Limit to 8 products for the featured section
    products = products.slice(0, 8)

    console.log(`Fetched ${products.length} featured products`)
  } catch (err) {
    console.error("Error fetching featured products:", err)
    error = err instanceof Error ? err.message : "Failed to load products"
  }

  return <FeaturedProductsClient initialProducts={products} error={error} />
}
