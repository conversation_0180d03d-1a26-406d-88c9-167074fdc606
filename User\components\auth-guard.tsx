"use client"

import type React from "react"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { useAuth } from "@/contexts/shopify-auth-context"
import LoadingSpinner from "@/components/loading-spinner"

interface AuthGuardProps {
  children: React.ReactNode
  requireVerifiedGST?: boolean
}

export default function AuthGuard({ children, requireVerifiedGST = false }: AuthGuardProps) {
  const { user, isLoading } = useAuth()
  const router = useRouter()
  const [isClient, setIsClient] = useState(false)
  const [redirectPath, setRedirectPath] = useState("")

  useEffect(() => {
    setIsClient(true)
    setRedirectPath(window.location.pathname)
  }, [])

  useEffect(() => {
    if (isClient && !isLoading && !user) {
      // Simple redirect to login if user is not authenticated
      router.push(`/login?redirect=${encodeURIComponent(redirectPath)}`)
    } else if (isClient && !isLoading && requireVerifiedGST && !user?.isGstVerified) {
      // Redirect to GST verification if required
      router.push("/account/verify-gst")
    }
  }, [user, isLoading, router, isClient, requireVerifiedGST, redirectPath])

  // Show loading spinner while checking authentication
  if (!isClient || isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <LoadingSpinner />
      </div>
    )
  }

  // Don't render anything if not authenticated
  if (!user) {
    return null
  }

  // Don't render if GST verification is required but not verified
  if (requireVerifiedGST && !user.isGstVerified) {
    return null
  }

  // User is authenticated, render children
  return <>{children}</>
}
