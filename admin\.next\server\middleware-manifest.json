{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_185a879a._.js", "server/edge/chunks/[root-of-the-server]__7adad445._.js", "server/edge/chunks/edge-wrapper_6c593f21.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "93q2+ThywVMICBZGrjqWrlW2FJpdwcjpUekbGqXbIAA=", "__NEXT_PREVIEW_MODE_ID": "1baaba11cb6c38f8ef4030ed690a5a9e", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "03fb41692c22671aa2c3e563feb3f9c74354b8ed8b0058c9dad4acb7e5c77995", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "153f6e054fddd4cbd2bd43cab78a4ec48c6cac836157616a35989ea841148415"}}}, "sortedMiddleware": ["/"], "functions": {}}