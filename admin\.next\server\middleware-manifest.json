{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_185a879a._.js", "server/edge/chunks/[root-of-the-server]__7adad445._.js", "server/edge/chunks/edge-wrapper_6c593f21.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "93q2+ThywVMICBZGrjqWrlW2FJpdwcjpUekbGqXbIAA=", "__NEXT_PREVIEW_MODE_ID": "de0fe2aa326dbc4a7755cae3f71a597a", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "1d855b3519938669bb6d7618f659695d3377b892f03fe03d95f47462e4483e28", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "d50f425ece3f30a39e95020349ad0c1f27e02f389bde77b3fd45622980542b97"}}}, "sortedMiddleware": ["/"], "functions": {}}