import type React from "react"
import type { Metada<PERSON> } from "next"
import "./globals.css"
import "@/styles/typography.css"
import "@/styles/premium-typography.css"
import Header from "@/components/header"
import Footer from "@/components/footer"
import { ThemeProvider } from "@/components/theme-provider"
import { ShopifyAuthProvider } from "@/contexts/shopify-auth-context"
import { CartProvider } from "@/hooks/use-cart"
import { Toaster } from "@/components/ui/sonner"
import DevTools from "@/components/dev-tools"

// Import font configurations
import {
  sfPro,
  inter,
  poppins,
  montserrat,
  sourceSansPro,
  dmSans,
  spaceGrotesk,
  fontFamilies
} from '@/styles/fonts'

export const metadata: Metadata = {
  title: "Benzochem Industries | Premium Chemical Products",
  description:
    "Specialized chemical trading company offering high-quality powder and liquid products for industrial applications.",
  generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`
          ${sfPro.variable}
          ${inter.variable}
          ${poppins.variable}
          ${montserrat.variable}
          ${sourceSansPro.variable}
          ${dmSans.variable}
          ${spaceGrotesk.variable}
          font-sans antialiased
        `}
        style={{
          '--font-headings': fontFamilies.headings,
          '--font-body': fontFamilies.body,
          '--font-ui': fontFamilies.ui,
        } as React.CSSProperties}
      >
        <ThemeProvider attribute="class" defaultTheme="light" enableSystem disableTransitionOnChange>
          <ShopifyAuthProvider>
            <CartProvider>
              <Header />
              {children}
              <Footer />
              <Toaster />
              <DevTools />
            </CartProvider>
          </ShopifyAuthProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}
