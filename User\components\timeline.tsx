"use client"

import { motion } from "framer-motion"

interface TimelineEvent {
  year: string
  title: string
  description: string
}

interface TimelineProps {
  events: TimelineEvent[]
}

export default function Timeline({ events }: TimelineProps) {
  return (
    <div className="relative">
      {/* Center line */}
      <div className="absolute left-1/2 transform -translate-x-1/2 h-full w-px bg-neutral-200"></div>

      <div className="space-y-12">
        {events.map((event, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: "-50px" }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
            className={`relative flex items-center ${index % 2 === 0 ? "justify-start" : "justify-end"} md:justify-between`}
          >
            <div
              className={`w-full md:w-5/12 ${
                index % 2 === 0 ? "md:text-right md:pr-8" : "md:text-left md:pl-8 order-1 md:order-none"
              }`}
            >
              <div className="bg-white p-6 rounded-lg shadow-sm">
                <span className="text-teal-600 font-medium">{event.year}</span>
                <h3 className="text-lg font-medium mt-1 mb-2">{event.title}</h3>
                <p className="text-neutral-600">{event.description}</p>
              </div>
            </div>

            {/* Center dot */}
            <div className="absolute left-1/2 transform -translate-x-1/2 w-4 h-4 rounded-full bg-teal-600 border-4 border-white"></div>
          </motion.div>
        ))}
      </div>
    </div>
  )
}
