"use client"

import { useState, useEffect, useRef } from "react"
import Link from "next/link"
import Image from "next/image"
import { AnimatePresence, motion } from "framer-motion"
import { Search, ShoppingBag, Menu, X, User, Loader2, ChevronDown } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { useAuth } from "@/contexts/shopify-auth-context"
import { useCart } from "@/hooks/use-cart"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { searchProducts } from "@/lib/search-service"
import { Product } from "@/lib/types"

export default function Header() {
  const [isScrolled, setIsScrolled] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [isSearchOpen, setIsSearchOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [isProductsDropdownOpen, setIsProductsDropdownOpen] = useState(false)
  const [searchResults, setSearchResults] = useState<Product[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const searchRef = useRef<HTMLDivElement>(null)
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const { user, logout } = useAuth()
  const { itemCount } = useCart()

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10)
    }
    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsSearchOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [])

  // Listen for authentication state changes
  useEffect(() => {
    // We don't need to manually handle auth state changes here
    // The user state comes from the auth context which is already handling this
    // Just listen for the event to know when to check for UI updates
    const handleAuthStateChange = () => {
      // No need to do anything here - the auth context will update the user state
      // and the header will re-render automatically when the user state changes
    };

    // Listen for the custom auth state change event
    window.addEventListener('auth-state-change', handleAuthStateChange);

    return () => {
      window.removeEventListener('auth-state-change', handleAuthStateChange);
    };
  }, []);

  useEffect(() => {
    // Clear any existing timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current)
    }

    // Don't search if query is empty
    if (searchQuery.trim() === "") {
      setSearchResults([])
      setIsLoading(false)
      return
    }

    setIsLoading(true)

    // Set a new timeout for debouncing
    searchTimeoutRef.current = setTimeout(async () => {
      try {
        const results = await searchProducts(searchQuery)
        setSearchResults(results)
      } catch (error) {
        console.error("Error searching products:", error)
        setSearchResults([])
      } finally {
        setIsLoading(false)
      }
    }, 500) // 500ms debounce

    // Cleanup function
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current)
      }
    }
  }, [searchQuery])

  const handleSearchClick = () => {
    setIsSearchOpen(!isSearchOpen)
    if (!isSearchOpen) {
      setTimeout(() => {
        const searchInput = document.getElementById("search-input")
        if (searchInput) {
          searchInput.focus()
        }
      }, 100)
    }
  }

  const formatPrice = (amount: string, currencyCode: string) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currencyCode,
    }).format(parseFloat(amount))
  }

  const productCategories = [
    {
      title: "Powder Products",
      href: "/categories/powder",
      description: "High-quality powder formulations for various applications"
    },
    {
      title: "Liquid Products",
      href: "/categories/liquid",
      description: "Premium liquid solutions and concentrates"
    }
  ]

  return (
    <header
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled ? "bg-white/90 backdrop-blur-md shadow-sm" : "bg-transparent"
      }`}
    >
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16 md:h-20">
          <Link href="/" className="flex items-center">
            <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.5 }}>
              <Image src="/images/logo.png" alt="Benzochem Industries" width={120} height={120} className="md:w-32 w-24" />
            </motion.div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            {/* Products Dropdown with Custom Animation */}
            <div
              className="relative"
              onMouseEnter={() => setIsProductsDropdownOpen(true)}
              onMouseLeave={() => setIsProductsDropdownOpen(false)}
            >
              <button className="flex items-center space-x-1 text-sm font-medium hover:text-teal-600 transition-colors py-2">
                <span>Products</span>
                <motion.div
                  animate={{ rotate: isProductsDropdownOpen ? 180 : 0 }}
                  transition={{ duration: 0.2 }}
                >
                  <ChevronDown className="h-4 w-4" />
                </motion.div>
              </button>

              {/* Animated Dropdown */}
              <AnimatePresence>
                {isProductsDropdownOpen && (
                  <motion.div
                    initial={{ opacity: 0, y: -10, scale: 0.95 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    exit={{ opacity: 0, y: -10, scale: 0.95 }}
                    transition={{
                      duration: 0.2,
                      ease: "easeOut"
                    }}
                    className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 w-80 bg-white rounded-lg shadow-xl border border-gray-100 overflow-hidden"
                  >
                    <div className="py-2">
                      {productCategories.map((category, index) => (
                        <motion.div
                          key={category.href}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{
                            duration: 0.2,
                            delay: index * 0.05,
                            ease: "easeOut"
                          }}
                        >
                          <Link
                            href={category.href}
                            className="block px-4 py-3 hover:bg-gray-50 transition-colors group"
                          >
                            <div className="flex flex-col">
                              <span className="text-sm font-medium text-gray-900 group-hover:text-teal-600 transition-colors">
                                {category.title}
                              </span>
                              <span className="text-xs text-gray-500 mt-1">
                                {category.description}
                              </span>
                            </div>
                          </Link>
                        </motion.div>
                      ))}
                    </div>

                    {/* Bottom accent */}
                    <motion.div
                      initial={{ scaleX: 0 }}
                      animate={{ scaleX: 1 }}
                      transition={{ duration: 0.3, delay: 0.1 }}
                      className="h-1 bg-gradient-to-r from-teal-500 to-teal-600"
                    />
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
            <Link href="/about" className="text-sm font-medium hover:text-teal-600 transition-colors">
              About
            </Link>
            <Link href="/contact" className="text-sm font-medium hover:text-teal-600 transition-colors">
              Contact
            </Link>
          </nav>

          <div className="hidden md:flex items-center space-x-4">
            <div className="relative" ref={searchRef}>
              <Button
                variant="ghost"
                size="icon"
                aria-label="Search"
                onClick={handleSearchClick}
              >
                <Search className="h-5 w-5" />
              </Button>

              {isSearchOpen && (
                <div className="absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg overflow-hidden z-50">
                  <div className="p-1">
                    <Input
                      id="search-input"
                      type="text"
                      placeholder="Search products..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-full"
                    />
                  </div>

                  {isLoading ? (
                    <div className="flex justify-center items-center p-4">
                      <Loader2 className="h-6 w-6 animate-spin text-teal-600" />
                    </div>
                  ) : searchQuery && searchResults.length === 0 ? (
                    <div className="p-4 text-center text-gray-500">
                      No products found
                    </div>
                  ) : (
                    <div className="max-h-96 overflow-y-auto">
                      {searchResults.map((product) => (
                        <Link
                          key={product.id}
                          href={`/products/${product.id.split('/').pop()}`}
                          onClick={() => {
                            setIsSearchOpen(false)
                            setSearchQuery("")
                          }}
                          className="block p-3 hover:bg-gray-50 border-t border-gray-100"
                        >
                          <div className="flex items-center space-x-3">
                            {product.images?.edges?.[0]?.node?.url && (
                              <div className="flex-shrink-0 h-10 w-10 bg-gray-100 rounded-md overflow-hidden">
                                <Image
                                  src={product.images.edges[0].node.url}
                                  alt={product.title}
                                  width={40}
                                  height={40}
                                  className="h-full w-full object-cover"
                                />
                              </div>
                            )}
                            <div className="flex-1 min-w-0">
                              <p className="text-sm font-medium text-gray-900 truncate">
                                {product.title}
                              </p>
                              <p className="text-xs text-gray-500 truncate">
                                {product.collections?.edges?.[0]?.node?.title || ""}
                              </p>
                            </div>
                            <div className="flex-shrink-0">
                              <p className="text-sm font-medium text-teal-600">
                                {formatPrice(
                                  product.priceRange.minVariantPrice.amount,
                                  product.priceRange.minVariantPrice.currencyCode
                                )}
                              </p>
                            </div>
                          </div>
                        </Link>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>

            <Button variant="ghost" size="icon" aria-label="Cart" asChild>
              <Link href="/cart" className="relative">
                <ShoppingBag className="h-5 w-5" />
                {itemCount > 0 && (
                  <span className="absolute top-0 right-0 h-4 w-4 rounded-full bg-teal-600 text-[10px] font-medium text-white flex items-center justify-center">
                    {itemCount}
                  </span>
                )}
              </Link>
            </Button>

            {user ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon" className="relative">
                    <User className="h-5 w-5" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  <DropdownMenuItem asChild>
                    <Link href="/account" className="w-full">
                      Account Dashboard
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/account/orders" className="w-full">
                      Order History
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/account/saved" className="w-full">
                      Saved Products
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <button onClick={logout} className="w-full text-left">
                      Logout
                    </button>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <Button variant="ghost" asChild>
                <Link href="/login">Login</Link>
              </Button>
            )}
          </div>

          {/* Mobile Icons: Cart and Menu Button */}
          <div className="flex items-center space-x-6 md:hidden">
            <Button variant="ghost" size="icon" aria-label="Cart" asChild>
              <Link href="/cart" className="relative">
                <ShoppingBag className="h-8 w-8" />
                {itemCount > 0 && (
                  <span className="absolute top-0 right-0 h-4 w-4 rounded-full bg-teal-600 text-[10px] font-medium text-white flex items-center justify-center">
                    {itemCount}
                  </span>
                )}
              </Link>
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              aria-label="Menu"
            >
              {isMobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </Button>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMobileMenuOpen && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: "100vh" }}
          exit={{ opacity: 0, height: 0 }}
          transition={{ duration: 0.3 }}
          className="md:hidden bg-white border-t"
        >
          <div className="container mx-auto px-4 py-4">
            {/* Mobile Search */}
            <div className="mb-4">
              <div className="relative">
                <Input
                  type="text"
                  placeholder="Search products..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pr-10"
                />
                {isLoading ? (
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                    <Loader2 className="h-4 w-4 animate-spin text-teal-600" />
                  </div>
                ) : (
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                    <Search className="h-4 w-4 text-gray-400" />
                  </div>
                )}
              </div>

              {searchQuery && searchResults.length > 0 && (
                <div className="mt-2 bg-white rounded-md shadow-lg overflow-hidden max-h-60 overflow-y-auto">
                  {searchResults.map((product) => (
                    <Link
                      key={product.id}
                      href={`/products/${product.id.split('/').pop()}`}
                      onClick={() => {
                        setIsMobileMenuOpen(false)
                        setSearchQuery("")
                      }}
                      className="block p-3 hover:bg-gray-50 border-t border-gray-100"
                    >
                      <div className="flex items-center space-x-3">
                        {product.images?.edges?.[0]?.node?.url && (
                          <div className="flex-shrink-0 h-10 w-10 bg-gray-100 rounded-md overflow-hidden">
                            <Image
                              src={product.images.edges[0].node.url}
                              alt={product.title}
                              width={40}
                              height={40}
                              className="h-full w-full object-cover"
                            />
                          </div>
                        )}
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {product.title}
                          </p>
                          <p className="text-sm text-teal-600">
                            {formatPrice(
                              product.priceRange.minVariantPrice.amount,
                              product.priceRange.minVariantPrice.currencyCode
                            )}
                          </p>
                        </div>
                      </div>
                    </Link>
                  ))}
                </div>
              )}
            </div>

            <nav className="flex flex-col space-y-4">
              <Link
                href="/categories/powder"
                className="text-sm font-medium py-2 hover:text-teal-600 transition-colors"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Powder Products
              </Link>
              <Link
                href="/categories/liquid"
                className="text-sm font-medium py-2 hover:text-teal-600 transition-colors"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Liquid Products
              </Link>
              <Link
                href="/about"
                className="text-sm font-medium py-2 hover:text-teal-600 transition-colors"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                About
              </Link>
              <Link
                href="/contact"
                className="text-sm font-medium py-2 hover:text-teal-600 transition-colors"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Contact
              </Link>

              {user ? (
                <>
                  <Link
                    href="/account"
                    className="text-sm font-medium py-2 hover:text-teal-600 transition-colors flex items-center"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    Account Dashboard
                  </Link>
                  <Link
                    href="/logout"
                    className="text-sm font-medium py-2 hover:text-teal-600 transition-colors"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    Logout
                  </Link>
                </>
              ) : (
                <Link
                  href="/login"
                  className="text-sm font-medium py-2 hover:text-teal-600 transition-colors"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  Login / Register
                </Link>
              )}

            </nav>
          </div>
        </motion.div>
      )}
    </header>
  )
}
