// Test script for Shopify metafields functionality
// This script demonstrates how to use the metafield functions

const BASE_URL = process.env.NODE_ENV === 'production' 
  ? 'https://your-domain.com' 
  : 'http://localhost:3000';

// Test customer ID (replace with actual customer ID)
const TEST_CUSTOMER_ID = 'gid://shopify/Customer/123456789';

// Test metafield operations
async function testMetafieldOperations() {
  console.log('🧪 Testing Shopify Metafields Functionality...\n');

  try {
    // Test 1: Create basic metafields
    console.log('📝 Test 1: Creating basic metafields...');
    const createResponse = await fetch(`${BASE_URL}/api/test-metafields`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        action: 'create',
        customerId: TEST_CUSTOMER_ID,
        metafields: [
          {
            namespace: "custom",
            key: "business_type",
            value: "Manufacturing",
            type: "single_line_text_field"
          },
          {
            namespace: "custom",
            key: "annual_revenue",
            value: "5000000",
            type: "number_decimal"
          },
          {
            namespace: "preferences",
            key: "newsletter_subscription",
            value: "true",
            type: "boolean"
          }
        ]
      })
    });

    const createResult = await createResponse.json();
    console.log('Create Response:', JSON.stringify(createResult, null, 2));

    // Test 2: Retrieve metafields
    console.log('\n📖 Test 2: Retrieving metafields...');
    const getResponse = await fetch(`${BASE_URL}/api/test-metafields`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        action: 'get',
        customerId: TEST_CUSTOMER_ID
      })
    });

    const getResult = await getResponse.json();
    console.log('Get Response:', JSON.stringify(getResult, null, 2));

    // Test 3: Update GST information
    console.log('\n🏢 Test 3: Updating GST information...');
    const gstResponse = await fetch(`${BASE_URL}/api/test-metafields`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        action: 'updateGST',
        customerId: TEST_CUSTOMER_ID,
        gstData: {
          gstNumber: "27AABCU9603R1ZM",
          legalNameOfBusiness: "Benzochem Industries Pvt Ltd",
          tradeName: "Benzochem",
          dateOfRegistration: "2020-01-15",
          constitutionOfBusiness: "Private Limited Company",
          taxpayerType: "Regular",
          gstStatus: "Active",
          principalPlaceOfBusinessAddress: "123 Industrial Area, Mumbai, Maharashtra 400001",
          principalPlaceOfBusinessEmail: "<EMAIL>",
          principalPlaceOfBusinessMobile: "+91-9876543210",
          natureOfCoreBusinessActivity: "Chemical Manufacturing and Trading"
        }
      })
    });

    const gstResult = await gstResponse.json();
    console.log('GST Update Response:', JSON.stringify(gstResult, null, 2));

    // Test 4: Update business preferences
    console.log('\n💼 Test 4: Updating business preferences...');
    const prefsResponse = await fetch(`${BASE_URL}/api/test-metafields`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        action: 'updatePreferences',
        customerId: TEST_CUSTOMER_ID,
        preferences: {
          preferredPaymentMethod: "Net 30",
          creditLimit: 100000,
          businessCategory: "Chemical Manufacturing",
          annualTurnover: "10-50 Crores",
          numberOfEmployees: "50-100",
          businessType: "B2B",
          industryType: "Chemicals",
          notes: "Preferred supplier for specialty chemicals. Bulk order discounts applicable."
        }
      })
    });

    const prefsResult = await prefsResponse.json();
    console.log('Preferences Update Response:', JSON.stringify(prefsResult, null, 2));

    // Test 5: Get specific metafield value
    console.log('\n🔍 Test 5: Getting specific metafield value...');
    const valueResponse = await fetch(`${BASE_URL}/api/test-metafields`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        action: 'getValue',
        customerId: TEST_CUSTOMER_ID,
        namespace: 'custom',
        key: 'business_type'
      })
    });

    const valueResult = await valueResponse.json();
    console.log('Get Value Response:', JSON.stringify(valueResult, null, 2));

    // Test 6: Get GST information
    console.log('\n📋 Test 6: Getting GST information...');
    const getGstResponse = await fetch(`${BASE_URL}/api/test-metafields`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        action: 'getGST',
        customerId: TEST_CUSTOMER_ID
      })
    });

    const getGstResult = await getGstResponse.json();
    console.log('Get GST Response:', JSON.stringify(getGstResult, null, 2));

  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

// Browser console helpers
function createBrowserHelpers() {
  if (typeof window !== 'undefined') {
    // Helper to test metafields from browser console
    (window as any).testMetafields = async (customerId) => {
      console.log(`Testing metafields for customer: ${customerId}`);
      
      try {
        // Create test metafields
        const response = await fetch('/api/test-metafields', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'create',
            customerId: customerId,
            metafields: [
              {
                namespace: "test",
                key: "browser_test",
                value: "Test from browser console",
                type: "single_line_text_field"
              }
            ]
          })
        });

        const result = await response.json();
        console.log('✅ Metafield test result:', result);
        return result;
      } catch (error) {
        console.error('❌ Metafield test failed:', error);
        return { success: false, error: error.message };
      }
    };

    // Helper to get customer metafields
    (window as any).getCustomerMetafields = async (customerId, namespace) => {
      console.log(`Getting metafields for customer: ${customerId}, namespace: ${namespace || 'all'}`);
      
      try {
        const response = await fetch('/api/test-metafields', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'get',
            customerId: customerId,
            namespace: namespace
          })
        });

        const result = await response.json();
        console.log('✅ Customer metafields:', result);
        return result;
      } catch (error) {
        console.error('❌ Failed to get metafields:', error);
        return { success: false, error: error.message };
      }
    };

    // Helper to update GST info
    (window as any).updateGSTInfo = async (customerId, gstData) => {
      console.log(`Updating GST info for customer: ${customerId}`);
      
      try {
        const response = await fetch('/api/test-metafields', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'updateGST',
            customerId: customerId,
            gstData: gstData
          })
        });

        const result = await response.json();
        console.log('✅ GST info updated:', result);
        return result;
      } catch (error) {
        console.error('❌ Failed to update GST info:', error);
        return { success: false, error: error.message };
      }
    };

    console.log('🧪 Metafield test helpers loaded!');
    console.log('Available functions:');
    console.log('- window.testMetafields(customerId) - Test basic metafield operations');
    console.log('- window.getCustomerMetafields(customerId, namespace?) - Get customer metafields');
    console.log('- window.updateGSTInfo(customerId, gstData) - Update GST information');
  }
}

// Example usage data
const EXAMPLE_GST_DATA = {
  gstNumber: "27AABCU9603R1ZM",
  legalNameOfBusiness: "Test Company Pvt Ltd",
  tradeName: "Test Company",
  dateOfRegistration: "2020-01-15",
  constitutionOfBusiness: "Private Limited Company",
  taxpayerType: "Regular",
  gstStatus: "Active"
};

const EXAMPLE_PREFERENCES = {
  preferredPaymentMethod: "Net 30",
  creditLimit: 50000,
  businessCategory: "Trading",
  annualTurnover: "1-5 Crores",
  numberOfEmployees: "10-50",
  businessType: "B2B",
  industryType: "Chemicals"
};

// Run tests if this is the main module
if (require.main === module) {
  testMetafieldOperations().catch(console.error);
}

// Export for use in other modules
module.exports = {
  testMetafieldOperations,
  createBrowserHelpers,
  EXAMPLE_GST_DATA,
  EXAMPLE_PREFERENCES
};
