import type { Metadata } from "next"
import { notFound } from "next/navigation"
import Image from "next/image"
import Link from "next/link"
import { Suspense } from 'react' // Added
import { ArrowLeft } from "lucide-react"
import { getBlogPostBySlug, getRelatedBlogPosts } from "@/lib/blog"
import BlogPostCard from "@/components/blog-post-card"
import { Skeleton } from "@/components/ui/skeleton" // Added

interface BlogPostPageProps {
  params: {
    slug: string
  }
}

export async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {
  // getBlogPostBySlug is synchronous, but metadata generation is fine this way.
  const post = getBlogPostBySlug(params.slug)

  if (!post) {
    return {
      title: "Post Not Found | Benzochem Industries",
      description: "The requested blog post could not be found.",
    }
  }

  return {
    title: `${post.title} | Benzochem Industries Blog`,
    description: post.excerpt,
  }
}

// Skeleton for an individual related blog post card (similar to the one in blog/page.tsx)
function RelatedBlogPostCardSkeleton() {
  return (
    <div className="bg-white rounded-lg overflow-hidden shadow-sm">
      <Skeleton className="aspect-[16/9] w-full rounded-t-lg" /> {/* Image */}
      <div className="p-6">
        <div className="flex items-center mb-4">
          <Skeleton className="h-5 w-20 rounded-full mr-3" /> {/* Category */}
          <Skeleton className="h-4 w-16 rounded" /> {/* Date */}
        </div>
        <Skeleton className="h-6 w-3/4 mb-3 rounded" /> {/* Title */}
        <Skeleton className="h-4 w-full mb-1 rounded" /> {/* Excerpt line 1 */}
        <Skeleton className="h-4 w-5/6 mb-4 rounded" /> {/* Excerpt line 2 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Skeleton className="w-8 h-8 rounded-full mr-2" /> {/* Author Avatar */}
            <Skeleton className="h-4 w-24 rounded" /> {/* Author Name */}
          </div>
          <Skeleton className="h-4 w-20 rounded" /> {/* Read More */}
        </div>
      </div>
    </div>
  )
}

// Skeleton for the entire blog post page
function BlogPostPageSkeleton() {
  return (
    <main className="flex min-h-screen flex-col pt-20">
      <article className="py-16 md:py-24">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            {/* Back to Blog Link Skeleton */}
            <Skeleton className="h-6 w-32 mb-8 rounded" />

            <div className="mb-8">
              {/* Category and Date Skeleton */}
              <div className="flex items-center mb-4">
                <Skeleton className="h-6 w-24 rounded-full mr-3" />
                <Skeleton className="h-5 w-20 rounded" />
              </div>
              {/* Title Skeleton */}
              <Skeleton className="h-10 md:h-12 lg:h-14 w-full mb-6 rounded" />
              {/* Author Skeleton */}
              <div className="flex items-center">
                <Skeleton className="w-10 h-10 rounded-full mr-3" />
                <div>
                  <Skeleton className="h-5 w-32 mb-1 rounded" />
                  <Skeleton className="h-4 w-24 rounded" />
                </div>
              </div>
            </div>

            {/* Main Image Skeleton */}
            <Skeleton className="aspect-[16/9] w-full rounded-lg mb-10" />

            {/* Content Skeleton */}
            <div className="prose prose-lg max-w-none">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="mb-6 space-y-2">
                  <Skeleton className="h-4 w-full rounded" />
                  <Skeleton className="h-4 w-full rounded" />
                  <Skeleton className="h-4 w-3/4 rounded" />
                  <Skeleton className="h-4 w-5/6 rounded" />
                </div>
              ))}
            </div>
          </div>
        </div>
      </article>

      {/* Related Articles Skeleton */}
      <section className="py-16 md:py-24 bg-neutral-50">
        <div className="container mx-auto px-4">
          <Skeleton className="h-8 w-1/2 md:w-1/3 mx-auto mb-16 rounded" /> {/* "Related Articles" Title */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[...Array(3)].map((_, i) => (
              <RelatedBlogPostCardSkeleton key={i} />
            ))}
          </div>
        </div>
      </section>
    </main>
  )
}

// Component to fetch and render actual blog post content
async function BlogPostPageContent({ slug }: { slug: string }) {
  // Wrap synchronous calls in Promise.resolve for Suspense compatibility with async components
  const post = await Promise.resolve(getBlogPostBySlug(slug))

  if (!post) {
    notFound()
  }

  const relatedPosts = await Promise.resolve(getRelatedBlogPosts(slug, post.category))

  return (
    <main className="flex min-h-screen flex-col pt-20">
      <article className="py-16 md:py-24">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <Link href="/blog" className="inline-flex items-center text-neutral-600 hover:text-teal-600 mb-8">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Blog
            </Link>

            <div className="mb-8">
              <div className="flex items-center mb-4">
                <span className="text-sm font-medium bg-teal-100 text-teal-800 px-3 py-1 rounded-full mr-3">
                  {post.category}
                </span>
                <span className="text-sm text-neutral-500">{post.date}</span>
              </div>
              <h1 className="text-3xl md:text-4xl lg:text-5xl font-medium mb-6">{post.title}</h1>
              <div className="flex items-center">
                <div className="relative w-10 h-10 rounded-full overflow-hidden mr-3">
                  <Image
                    src={post.author.avatar || "/placeholder.svg"}
                    alt={post.author.name}
                    fill
                    className="object-cover"
                  />
                </div>
                <div>
                  <p className="font-medium">{post.author.name}</p>
                  <p className="text-sm text-neutral-500">{post.author.title}</p>
                </div>
              </div>
            </div>

            <div className="relative aspect-[16/9] rounded-lg overflow-hidden mb-10">
              <Image src={post.image || "/placeholder.svg"} alt={post.title} fill className="object-cover" priority />
            </div>

            <div className="prose prose-lg max-w-none">
              {post.content.map((paragraph: string, index: number) => (
                <p key={index} className="mb-6 text-neutral-700">
                  {paragraph}
                </p>
              ))}
            </div>
          </div>
        </div>
      </article>

      {relatedPosts.length > 0 && (
        <section className="py-16 md:py-24 bg-neutral-50">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-medium text-center mb-16">Related Articles</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {relatedPosts.map((relatedPost) => (
                <BlogPostCard key={relatedPost.slug} post={relatedPost} />
              ))}
            </div>
          </div>
        </section>
      )}
    </main>
  )
}

export default async function BlogPostPage({ params }: BlogPostPageProps) {
  const { slug } = await params
  return (
    <Suspense fallback={<BlogPostPageSkeleton />}>
      <BlogPostPageContent slug={slug} />
    </Suspense>
  )
}
