// Product interface
export interface Product {
  id: string
  title: string
  description: string
  descriptionHtml?: string; // Added optional descriptionHtml
  tags: string[]
  collections: {
    edges: Array<{
      node: {
        title: string
      }
    }>
  }
  images: {
    edges: Array<{
      node: {
        url: string
      }
    }>
  }
  media: {
    edges: Array<{
      node: {
        id: string
        image: {
          url: string
        }
      }
    }>
  }
  priceRange: {
    minVariantPrice: {
      amount: string
      currencyCode: string
    }
    maxVariantPrice: {
      amount: string
      currencyCode: string
    }
  }
  compareAtPriceRange: {
    minVariantPrice: {
      amount: string
      currencyCode: string
    }
    maxVariantPrice: {
      amount: string
      currencyCode: string
    }
  }
  metafields: Array<{
    id: string
    key: string
    namespace: string
    value: string
    type: string
  }>
  variants?: { // Make it optional as not all product listings might need full variant details
    edges: Array<{
      node: ProductVariant
    }>
  }
}

// Interface for Product Variant Options (e.g., Size, Color)
export interface ProductVariantOption {
  name: string;
  value: string;
}

// Interface for Product Variants
export interface ProductVariant {
  id: string; // This is the GID for the ProductVariant, e.g., "gid://shopify/ProductVariant/12345"
  title: string; // e.g., "Small / Red" or "25kg Bag"
  availableForSale: boolean;
  quantityAvailable?: number | null;
  priceV2: {
    amount: string;
    currencyCode: string;
  };
  compareAtPriceV2?: {
    amount: string;
    currencyCode: string;
  } | null;
  selectedOptions: ProductVariantOption[];
  image?: { // Optional: Variant-specific image
    url: string;
    altText?: string | null;
  } | null;
}

// Navigation menu item interface
export interface NavItem {
  name: string
  href: string
  description?: string
}

// Hero section interface
export interface HeroSection {
  title: string
  subtitle: string
  backgroundImage: string
  ctaText: string
  ctaLink: string
}

// Feature interface
export interface Feature {
  title: string
  description: string
  icon: string
}

// Testimonial interface
export interface Testimonial {
  name: string
  role: string
  company: string
  content: string
  image: string
}

// FAQ interface
export interface FAQ {
  question: string
  answer: string
}

// Contact form interface
export interface ContactForm {
  name: string
  email: string
  message: string
}

// Social media link interface
export interface SocialLink {
  platform: string
  url: string
  icon: string
}

// Footer link interface
export interface FooterLink {
  title: string
  links: Array<{
    name: string
    href: string
  }>
}
