# 🔐 API Security Guide - Benzochem Industries

## 🚨 IMMEDIATE SECURITY ACTIONS REQUIRED

Your current `.env.local` files contain **REAL API KEYS** that need to be secured immediately.

### Step 1: Secure Your Current Keys

1. **Check if keys are compromised**:
   - If this project was ever pushed to GitHub/GitLab, your keys may be compromised
   - If shared via email/chat, consider keys compromised
   - If unsure, regenerate all keys as a precaution

2. **Backup current working keys** (securely):
   - Copy your current `.env.local` files to a secure location
   - Use a password manager or encrypted storage
   - These keys are currently working and valuable

### Step 2: Verify API Key Status

Run this command to test your current API keys:
```bash
cd User
npm run dev
```

Then test each service:
- **Shopify**: Visit product pages, try login/registration
- **GST Verification**: Go to `/account/verify-gst` and test with a valid GST number
- **Google Places**: Try address autocomplete in forms

## 🔧 API Service Setup Guides

### 1. Shopify API Keys

**Your Current Keys Appear Valid** - No action needed unless compromised.

If you need to regenerate:
1. Go to [Shopify Partners](https://partners.shopify.com/) or your store admin
2. Navigate to Apps > Private Apps (for Admin API)
3. For Storefront API: Settings > Apps and sales channels > Develop apps

**Key Types**:
- `NEXT_PUBLIC_SHOPIFY_STOREFRONT_ACCESS_TOKEN`: Safe for frontend (read-only)
- `SHOPIFY_ADMIN_API_ACCESS_TOKEN`: Server-side only (full access)
- `SHOPIFY_WEBHOOK_SECRET`: For webhook verification

### 2. RapidAPI GST Verification

**Your Current Key Appears Valid** - No action needed unless compromised.

If you need a new key:
1. Visit [RapidAPI](https://rapidapi.com/)
2. Sign up/login to your account
3. Go to [GST Return Status API](https://rapidapi.com/gst-api-gst-api-default/api/gst-return-status/)
4. Subscribe to the API (free tier available)
5. Copy your API key from the dashboard

**Usage**: Validates Indian business GST numbers for customer verification.

### 3. Google Places API

**Your Current Key Appears Valid** - No action needed unless compromised.

If you need a new key:
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing
3. Enable Places API and Places API (New)
4. Go to Credentials > Create Credentials > API Key
5. Restrict the key to Places APIs only

**See `GOOGLE_PLACES_SETUP.md` for detailed instructions.**

## 🛡️ Security Best Practices

### Environment Variables
- ✅ Use `.env.local` for local development
- ✅ Use `.env.production` for production (if applicable)
- ✅ Never commit `.env*` files to version control
- ✅ Use different keys for development and production

### API Key Restrictions
1. **Shopify**: Set up proper scopes and permissions
2. **RapidAPI**: Monitor usage and set up billing alerts
3. **Google Places**: Restrict to specific APIs and domains

### Monitoring
- Set up usage alerts for all APIs
- Monitor for unusual activity
- Rotate keys regularly (quarterly recommended)

## 🚀 Next Steps

1. **Immediate**: Ensure `.gitignore` properly excludes `.env*` files ✅ (Done)
2. **Test**: Verify all APIs work with current keys
3. **Secure**: Set up API key restrictions in each service console
4. **Monitor**: Set up usage alerts and billing notifications
5. **Document**: Keep secure backup of working configuration

## 📞 Support

If you encounter issues:
- **Shopify**: Check Shopify Partner Dashboard or store admin
- **RapidAPI**: Contact RapidAPI support or check API documentation
- **Google Places**: Check Google Cloud Console quotas and billing
