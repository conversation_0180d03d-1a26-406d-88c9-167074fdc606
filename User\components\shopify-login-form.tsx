"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { useAuth } from "@/contexts/shopify-auth-context"
import { useRouter, useSearchParams } from "next/navigation"
import Link from "next/link"
import { Eye, EyeOff, Loader2, Mail, Lock } from "lucide-react"

export default function ShopifyLoginForm() {
  const { login, isLoading } = useAuth()
  const router = useRouter()
  const searchParams = useSearchParams()
  
  const [formData, setFormData] = useState({
    email: "",
    password: "",
    rememberMe: false,
  })

  const [fieldErrors, setFieldErrors] = useState<Record<string, string | null>>({})
  const [error, setError] = useState<string | null>(null)
  const [showPassword, setShowPassword] = useState(false)

  // Check for messages from URL params
  const message = searchParams.get('message')

  // Validation functions
  const validateEmail = (email: string) => {
    if (!email) return "Please enter your email address"
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) return "Please enter a valid email address"
    return null
  }

  const validatePassword = (password: string) => {
    if (!password) return "Please enter your password"
    return null
  }

  const validateForm = () => {
    const errors: Record<string, string | null> = {}
    errors.email = validateEmail(formData.email)
    errors.password = validatePassword(formData.password)

    setFieldErrors(errors)
    return Object.values(errors).every((err) => err === null)
  }

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear field error when user starts typing
    if (fieldErrors[field]) {
      setFieldErrors(prev => ({ ...prev, [field]: null }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError(null)

    if (!validateForm()) {
      return
    }

    try {
      const result = await login(formData.email, formData.password)

      if (result.success) {
        // Redirect to dashboard or intended page
        const redirectTo = searchParams.get('redirect') || '/account'
        router.push(redirectTo)
      } else {
        // Handle specific error types
        if (result.error?.includes('ACCOUNT_NOT_FOUND')) {
          setError("No account found with this email address. Please check your email or create a new account.")
        } else if (result.error?.includes('INVALID_PASSWORD')) {
          setError("Incorrect password. Please try again or reset your password.")
        } else if (result.error?.includes('CUSTOMER_DISABLED')) {
          setError("Your account needs to be activated. Please check your email for activation instructions.")
        } else {
          setError(result.error || "Login failed. Please try again.")
        }
      }
    } catch (error) {
      console.error("Login error:", error)
      setError("An unexpected error occurred. Please try again.")
    }
  }

  const handleForgotPassword = async () => {
    if (!formData.email) {
      setError("Please enter your email address first, then click 'Forgot Password'")
      return
    }

    try {
      const response = await fetch('/api/shopify/customer-activation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: formData.email,
          action: 'recover'
        })
      })

      const result = await response.json()

      if (result.success) {
        setError(null)
        alert("Password reset email sent! Please check your email for instructions.")
      } else {
        setError("Failed to send password reset email. Please try again or contact support.")
      }
    } catch (error) {
      console.error("Password reset error:", error)
      setError("Failed to send password reset email. Please try again.")
    }
  }

  return (
    <div className="w-full mx-auto">
      <form onSubmit={handleSubmit} className="space-y-4">
        {message && (
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-md">
            <p className="text-blue-600 text-sm">{message}</p>
          </div>
        )}

        {error && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-md">
            <p className="text-red-600 text-sm">{error}</p>
          </div>
        )}

        {/* Email Field */}
        <div className="space-y-2">
          <div className="flex items-center gap-2 text-teal-600">
            <Mail size={16} />
            <Label htmlFor="email" className="text-sm font-medium text-gray-700">Email Address</Label>
          </div>
          <Input
            id="email"
            type="email"
            value={formData.email}
            onChange={(e) => handleInputChange("email", e.target.value)}
            className={`h-10 text-sm border-gray-200 focus:border-teal-500 focus:ring-1 focus:ring-teal-500 ${fieldErrors.email ? "border-red-500" : ""}`}
            placeholder="Enter your email address"
          />
          {fieldErrors.email && (
            <p className="text-red-500 text-xs mt-1">{fieldErrors.email}</p>
          )}
        </div>

        {/* Password Field */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 text-teal-600">
              <Lock size={16} />
              <Label htmlFor="password" className="text-sm font-medium text-gray-700">Password</Label>
            </div>
            <button
              type="button"
              onClick={handleForgotPassword}
              className="text-xs text-teal-600 hover:underline font-medium"
            >
              Forgot password?
            </button>
          </div>
          <div className="relative">
            <Input
              id="password"
              type={showPassword ? "text" : "password"}
              value={formData.password}
              onChange={(e) => handleInputChange("password", e.target.value)}
              className={`h-10 text-sm border-gray-200 focus:border-teal-500 focus:ring-1 focus:ring-teal-500 pr-10 ${fieldErrors.password ? "border-red-500" : ""}`}
              placeholder="Enter your password"
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            >
              {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
            </button>
          </div>
          {fieldErrors.password && (
            <p className="text-red-500 text-xs mt-1">{fieldErrors.password}</p>
          )}
        </div>

        {/* Remember Me */}
        <div className="flex items-center space-x-2">
          <Checkbox
            id="rememberMe"
            checked={formData.rememberMe}
            onCheckedChange={(checked) =>
              handleInputChange("rememberMe", checked as boolean)
            }
            className="data-[state=checked]:bg-teal-600 data-[state=checked]:border-teal-600"
          />
          <Label htmlFor="rememberMe" className="text-sm font-medium text-gray-700">
            Remember me
          </Label>
        </div>

        <Button
          type="submit"
          className="w-full h-10 text-sm bg-teal-600 hover:bg-teal-700 text-white font-medium"
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Signing In...
            </>
          ) : (
            "Sign In"
          )}
        </Button>

        <p className="text-center text-sm text-gray-600">
          Don't have an account?{" "}
          <Link href="/register" className="text-teal-600 hover:underline">
            Create one
          </Link>
        </p>
      </form>

      {/* Account Activation Help */}
      <div className="mt-8 p-4 bg-gray-50 rounded-md">
        <h4 className="font-medium text-gray-900 mb-2">Need Help?</h4>
        <ul className="text-sm text-gray-600 space-y-1">
          <li>• If you just registered, check your email for activation instructions</li>
          <li>• Make sure to check your spam/junk folder</li>
          <li>• Use "Forgot Password" to reset your password or activate your account</li>
          <li>• Contact support if you continue having issues</li>
        </ul>
      </div>
    </div>
  )
}
