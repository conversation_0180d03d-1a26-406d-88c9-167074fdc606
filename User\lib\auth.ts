import {
  create<PERSON><PERSON><PERSON>,
  loginCustomer,
  getCustomer,
  isCustomerAccessTokenValid,
} from "@/lib/customer-service"

export interface UserData {
  id: string
  email: string
  firstName: string
  lastName: string
  name: string
  phone: string
  businessName?: string
  gstNumber?: string
  isGstVerified?: boolean
  role: string
  shopifyCustomerId?: string
}

// Register a new user
export async function registerUser(userData: {
  email: string
  password: string
  firstName: string
  lastName: string
  phone: string
  businessName?: string
  gstNumber?: string
}): Promise<{ success: boolean; error?: string; user?: UserData; accessToken?: string }> {
  try {
    const { firstName, lastName } = userData

    // Create customer in Shopify
    await createCustomer({
      firstName,
      lastName,
      email: userData.email,
      password: userData.password,
      phone: userData.phone,
      // Business name and GST number could also be stored as metafields
    })

    // Login the user after registration and get the user data
    const loginResult = await loginUser(userData.email, userData.password)
    
    if (!loginResult.success || !loginResult.user) {
      throw new Error("User created but login failed. Please try logging in manually.")
    }
    
    // Return the user data along with success status and access token
    return { 
      success: true,
      user: loginResult.user,
      accessToken: loginResult.accessToken
    }
  } catch (error: any) {
    console.error("Registration error:", error)
    return { success: false, error: error.message || "Registration failed" }
  }
}

// Login a user with email and password
export async function loginUser(email: string, password: string): Promise<{ success: boolean; error?: string; user?: UserData; accessToken?: string }> {
  try {
    console.log("Starting login process for:", email);

    // Authenticate with Shopify
    const customerAccessToken = await loginCustomer(email, password)
    console.log("Customer access token obtained:", customerAccessToken ? "✅" : "❌");

    // Fetch customer data using the token with retry mechanism
    console.log("Fetching customer data...");
    let customerData = null;
    let retryCount = 0;
    const maxRetries = 3;

    while (!customerData && retryCount < maxRetries) {
      try {
        customerData = await getCustomer();
        console.log("Customer data received:", customerData ? "✅" : "❌");
      } catch (error) {
        retryCount++;
        console.log(`Attempt ${retryCount}/${maxRetries} failed:`, error instanceof Error ? error.message : String(error));

        if (retryCount < maxRetries) {
          console.log("Retrying in 1 second...");
          await new Promise(resolve => setTimeout(resolve, 1000));
        } else {
          throw error;
        }
      }
    }

    if (!customerData) {
      throw new Error("Failed to fetch customer data after multiple attempts")
    }

    // Transform Shopify customer data to our UserData format
    const userData: UserData = {
      id: customerData.id,
      email: customerData.email,
      firstName: customerData.firstName || "",
      lastName: customerData.lastName || "",
      name: `${customerData.firstName || ""} ${customerData.lastName || ""}`.trim(),
      phone: customerData.phone || "",
      businessName: customerData.metafields?.edges.find(
        (edge: any) => edge.node.namespace === "customer" && edge.node.key === "business_name",
      )?.node.value,
      gstNumber: customerData.metafields?.edges.find(
        (edge: any) => edge.node.namespace === "customer" && edge.node.key === "gst_number",
      )?.node.value,
      isGstVerified:
        customerData.metafields?.edges.find(
          (edge: any) => edge.node.namespace === "customer" && edge.node.key === "is_gst_verified",
        )?.node.value === "true",
      // Store Shopify customer ID for reference
      shopifyCustomerId: customerData.id,
      role: "user", // Default role
    }

    return { 
      success: true, 
      user: userData, 
      accessToken: customerAccessToken.accessToken 
    }
  } catch (error: any) {
    console.error("Login error:", error)
    return { success: false, error: error.message || "Login failed" }
  }
}

// Check if user is authenticated using Shopify only
export function isAuthenticated(): boolean {
  try {
    // Server-side check should return false
    if (typeof window === 'undefined') return false;

    // Check for valid Shopify customer access token
    const hasShopifyToken = isCustomerAccessTokenValid();

    // Check for local user data (for immediate post-registration state)
    let hasValidLocalUserData = false;
    const storedUserData = window.localStorage &&
      window.localStorage.getItem('benzochem_user');

    if (storedUserData) {
      try {
        // Validate the stored user data structure
        const userData = JSON.parse(storedUserData);
        hasValidLocalUserData = Boolean(userData && userData.email);
      } catch (e) {
        console.error('Invalid user data in localStorage', e);
        // Clean up invalid data
        window.localStorage.removeItem('benzochem_user');
      }
    }

    // Authentication based on Shopify token or valid local user data
    return hasShopifyToken || hasValidLocalUserData;
  } catch (error) {
    console.error('Error checking authentication status:', error);
    return false; // Fail secure - if there's an error, treat as not authenticated
  }
}

// Logout user by clearing all authentication data
export function logoutUser(): void {
  try {
    if (typeof window !== 'undefined') {
      // Clear all authentication-related localStorage items
      window.localStorage.removeItem('benzochem_user')
      window.localStorage.removeItem('shopify_customer_access_token')
      window.localStorage.removeItem('shopify_customer_access_token_expires')

      // Trigger auth state change event
      window.dispatchEvent(new CustomEvent('auth-state-change'))
    }
  } catch (error) {
    console.error('Error during logout:', error)
  }
}

// Verify GST number
export async function verifyGST(gstNumber: string): Promise<{
  valid: boolean
  businessName?: string
  address?: string
  error?: string
}> {
  // In a real implementation, this would call a GST verification API
  // For demo purposes, we'll simulate a successful verification for specific GST numbers

  // Simulate API call delay
  await new Promise((resolve) => setTimeout(resolve, 1000))

  // Simple validation pattern for Indian GST numbers
  const gstPattern = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/

  if (!gstPattern.test(gstNumber)) {
    return {
      valid: false,
      error: "Invalid GST number format",
    }
  }

  // For demo, accept specific GST numbers as valid
  if (gstNumber === "27AADCB2230M1ZT" || gstNumber === "29AABCU9603R1ZJ") {
    return {
      valid: true,
      businessName: gstNumber === "27AADCB2230M1ZT" ? "Demo Chemicals Pvt Ltd" : "Universal Chemicals",
      address:
        gstNumber === "27AADCB2230M1ZT"
          ? "123 Industrial Area, Mumbai, Maharashtra"
          : "456 Chemical Zone, Bangalore, Karnataka",
    }
  }

  // For any other GST number, return a generic success
  return {
    valid: true,
    businessName: "Verified Business",
    address: "Registered Address",
  }
}
