import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { customerAccessToken } = await request.json()

    if (!customerAccessToken) {
      return NextResponse.json(
        { error: 'Customer access token is required' },
        { status: 400 }
      )
    }

    console.log('🧪 Testing customer access token:', customerAccessToken.substring(0, 10) + '...')
    console.log('🧪 Full token for debugging:', customerAccessToken)

    // Test the token with Shopify Storefront API
    const response = await fetch(`https://benzochem.myshopify.com/api/2025-04/graphql.json`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Storefront-Access-Token': '95029391ce9ba8bad8a604e7faf7eb87'
      },
      body: JSON.stringify({
        query: `
          query getCustomer($customerAccessToken: String!) {
            customer(customerAccessToken: $customerAccessToken) {
              id
              firstName
              lastName
              email
              phone
            }
          }
        `,
        variables: {
          customerAccessToken
        }
      })
    })

    const result = await response.json()
    console.log('🧪 Shopify API response:', JSON.stringify(result, null, 2))

    if (result.errors) {
      return NextResponse.json(
        { success: false, errors: result.errors },
        { status: 400 }
      )
    }

    if (result.data?.customer) {
      return NextResponse.json({
        success: true,
        customer: result.data.customer
      })
    }

    return NextResponse.json(
      { success: false, error: 'No customer data found' },
      { status: 404 }
    )

  } catch (error) {
    console.error('🧪 Error testing token:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
