globalThis.__RSC_MANIFEST = globalThis.__RSC_MANIFEST || {};
globalThis.__RSC_MANIFEST["/_not-found/page"] = {"moduleLoading":{"prefix":"","crossOrigin":null},"clientModules":{"[project]/node_modules/next/dist/esm/client/components/layout-router.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-page.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-page.js":{"id":"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-segment.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-segment.js":{"id":"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js":{"id":"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/client/components/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/client/components/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/src/providers/convex-provider.tsx <module evaluation>":{"id":"[project]/src/providers/convex-provider.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_99fc49e2._.js","/_next/static/chunks/_39ed56ed._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/src/providers/convex-provider.tsx":{"id":"[project]/src/providers/convex-provider.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_99fc49e2._.js","/_next/static/chunks/_39ed56ed._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/src/providers/theme-provider.tsx <module evaluation>":{"id":"[project]/src/providers/theme-provider.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_99fc49e2._.js","/_next/static/chunks/_39ed56ed._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/src/providers/theme-provider.tsx":{"id":"[project]/src/providers/theme-provider.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_99fc49e2._.js","/_next/static/chunks/_39ed56ed._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/src/contexts/auth-context.tsx <module evaluation>":{"id":"[project]/src/contexts/auth-context.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_99fc49e2._.js","/_next/static/chunks/_39ed56ed._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/src/contexts/auth-context.tsx":{"id":"[project]/src/contexts/auth-context.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_99fc49e2._.js","/_next/static/chunks/_39ed56ed._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/src/contexts/re-auth-context.tsx <module evaluation>":{"id":"[project]/src/contexts/re-auth-context.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_99fc49e2._.js","/_next/static/chunks/_39ed56ed._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/src/contexts/re-auth-context.tsx":{"id":"[project]/src/contexts/re-auth-context.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_99fc49e2._.js","/_next/static/chunks/_39ed56ed._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/src/components/auth/re-auth-manager.tsx <module evaluation>":{"id":"[project]/src/components/auth/re-auth-manager.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_99fc49e2._.js","/_next/static/chunks/_39ed56ed._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/src/components/auth/re-auth-manager.tsx":{"id":"[project]/src/components/auth/re-auth-manager.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_99fc49e2._.js","/_next/static/chunks/_39ed56ed._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/src/components/ui/sonner.tsx <module evaluation>":{"id":"[project]/src/components/ui/sonner.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_99fc49e2._.js","/_next/static/chunks/_39ed56ed._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/src/components/ui/sonner.tsx":{"id":"[project]/src/components/ui/sonner.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_99fc49e2._.js","/_next/static/chunks/_39ed56ed._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false}},"ssrModuleMapping":{"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/layout-router.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_84bfc1e5._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_84bfc1e5._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-page.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_84bfc1e5._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-segment.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_84bfc1e5._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_84bfc1e5._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_84bfc1e5._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_84bfc1e5._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_84bfc1e5._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/src/providers/convex-provider.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/providers/convex-provider.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_1469d31a._.js","server/chunks/ssr/[root-of-the-server]__c3ac516a._.js"],"async":false}},"[project]/src/providers/theme-provider.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/providers/theme-provider.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_1469d31a._.js","server/chunks/ssr/[root-of-the-server]__c3ac516a._.js"],"async":false}},"[project]/src/contexts/auth-context.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/contexts/auth-context.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_1469d31a._.js","server/chunks/ssr/[root-of-the-server]__c3ac516a._.js"],"async":false}},"[project]/src/contexts/re-auth-context.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/contexts/re-auth-context.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_1469d31a._.js","server/chunks/ssr/[root-of-the-server]__c3ac516a._.js"],"async":false}},"[project]/src/components/auth/re-auth-manager.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/components/auth/re-auth-manager.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_1469d31a._.js","server/chunks/ssr/[root-of-the-server]__c3ac516a._.js"],"async":false}},"[project]/src/components/ui/sonner.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/components/ui/sonner.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_1469d31a._.js","server/chunks/ssr/[root-of-the-server]__c3ac516a._.js"],"async":false}}},"edgeSSRModuleMapping":{},"rscModuleMapping":{"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/layout-router.js (client reference/proxy)","name":"*","chunks":["server/app/_not-found/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js (client reference/proxy)","name":"*","chunks":["server/app/_not-found/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-page.js (client reference/proxy)","name":"*","chunks":["server/app/_not-found/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-segment.js (client reference/proxy)","name":"*","chunks":["server/app/_not-found/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js (client reference/proxy)","name":"*","chunks":["server/app/_not-found/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js (client reference/proxy)","name":"*","chunks":["server/app/_not-found/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js (client reference/proxy)","name":"*","chunks":["server/app/_not-found/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js (client reference/proxy)","name":"*","chunks":["server/app/_not-found/page.js"],"async":false}},"[project]/src/providers/convex-provider.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/providers/convex-provider.tsx (client reference/proxy)","name":"*","chunks":["server/app/_not-found/page.js"],"async":false}},"[project]/src/providers/theme-provider.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/providers/theme-provider.tsx (client reference/proxy)","name":"*","chunks":["server/app/_not-found/page.js"],"async":false}},"[project]/src/contexts/auth-context.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/contexts/auth-context.tsx (client reference/proxy)","name":"*","chunks":["server/app/_not-found/page.js"],"async":false}},"[project]/src/contexts/re-auth-context.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/contexts/re-auth-context.tsx (client reference/proxy)","name":"*","chunks":["server/app/_not-found/page.js"],"async":false}},"[project]/src/components/auth/re-auth-manager.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/components/auth/re-auth-manager.tsx (client reference/proxy)","name":"*","chunks":["server/app/_not-found/page.js"],"async":false}},"[project]/src/components/ui/sonner.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/components/ui/sonner.tsx (client reference/proxy)","name":"*","chunks":["server/app/_not-found/page.js"],"async":false}}},"edgeRscModuleMapping":{},"entryCSSFiles":{"[project]/src/app/favicon.ico":[],"[project]/src/app/layout":[{"path":"static/chunks/[root-of-the-server]__8ebb6d4b._.css","inlined":false}]},"entryJSFiles":{"[project]/src/app/favicon.ico":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"[project]/src/app/layout":["static/chunks/node_modules_99fc49e2._.js","static/chunks/_39ed56ed._.js","static/chunks/src_app_layout_tsx_c0237562._.js"]}}
