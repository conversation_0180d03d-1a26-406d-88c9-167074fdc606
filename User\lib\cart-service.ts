import { storefrontFetch } from "./shopify"
import {
  CART_CREATE_MUTATION,
  CART_LINES_ADD_MUTATION,
  CART_LINES_UPDATE_MUTATION,
  CART_LINES_REMOVE_MUTATION,
  CART_QUERY,
  CART_BUYER_IDENTITY_UPDATE_MUTATION,
} from "./shopify-queries" // Assuming this file exists and exports these queries

// Get customer access token from localStorage
function getCustomerAccessToken(): string | null {
  if (typeof window === "undefined") return null
  try {
    // First try the standard lowercase key
    let token = localStorage.getItem("shopify_customer_access_token")

    // If not found, try the legacy uppercase key
    if (!token) {
      token = localStorage.getItem("SHOPIFY_CUSTOMER_ACCESS_TOKEN")

      // If found in legacy format, migrate it to the new format
      if (token) {
        console.log("🔄 [cart-service] Migrating token from legacy uppercase key")
        localStorage.setItem("shopify_customer_access_token", token)

        // Try to get expiration from legacy format
        const legacyExpires = localStorage.getItem("SHOPIFY_CUSTOMER_ACCESS_TOKEN_EXPIRES")
        if (legacyExpires) {
          localStorage.setItem("shopify_customer_access_token_expires", legacyExpires)
          localStorage.removeItem("SHOPIFY_CUSTOMER_ACCESS_TOKEN_EXPIRES")
        }

        // Clean up legacy key
        localStorage.removeItem("SHOPIFY_CUSTOMER_ACCESS_TOKEN")
        console.log("✅ [cart-service] Token migration completed")
      }
    }

    return token
  } catch (e) {
    console.error("Failed to access localStorage for customer token:", e)
    return null
  }
}

// Function to prompt user for password and get Shopify customer access token
async function getShopifyCustomerAccessToken(): Promise<string | null> {
  if (typeof window === "undefined") return null

  try {
    // Get user data from localStorage
    const userDataRaw = localStorage.getItem("benzochem_user")
    if (!userDataRaw) {
      return null
    }

    const userData = JSON.parse(userDataRaw)
    if (!userData.email) {
      return null
    }

    // Debug: Log user data structure (without sensitive info)
    console.log("User data for cart sync:", {
      email: userData.email,
      firstName: userData.firstName,
      lastName: userData.lastName,
      name: userData.name,
      phone: userData.phone,
      hasShopifyCustomerId: !!userData.shopifyCustomerId,
      shopifyCustomerId: userData.shopifyCustomerId
    })

    // If user already has a Shopify customer ID, they might need account activation
    if (userData.shopifyCustomerId) {
      console.log("User has existing Shopify customer ID, account may need activation")
    }

    // Prompt user for password
    const password = prompt(`To enable cart sync across devices, please enter your password for ${userData.email}:`)
    if (!password) {
      return null
    }

    // Call Shopify Storefront API directly to get customer access token
    const response = await fetch('https://benzochem.myshopify.com/api/2025-04/graphql.json', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Storefront-Access-Token': '95029391ce9ba8bad8a604e7faf7eb87'
      },
      body: JSON.stringify({
        query: `
          mutation customerAccessTokenCreate($input: CustomerAccessTokenCreateInput!) {
            customerAccessTokenCreate(input: $input) {
              customerAccessToken {
                accessToken
                expiresAt
              }
              customerUserErrors {
                field
                message
                code
              }
            }
          }
        `,
        variables: {
          input: {
            email: userData.email,
            password: password
          }
        }
      })
    })

    const result = await response.json()

    if (result.data?.customerAccessTokenCreate?.customerAccessToken) {
      const token = result.data.customerAccessTokenCreate.customerAccessToken

      // Save token to localStorage
      localStorage.setItem("shopify_customer_access_token", token.accessToken)
      localStorage.setItem("shopify_customer_access_token_expires", token.expiresAt)

      return token.accessToken
    } else if (result.data?.customerAccessTokenCreate?.customerUserErrors) {
      const errors = result.data.customerAccessTokenCreate.customerUserErrors

      // Handle specific error cases
      if (errors.some((e: any) => e.code === "UNIDENTIFIED_CUSTOMER")) {
        // Check if user already has a Shopify customer ID
        if (userData.shopifyCustomerId) {
          // User has a customer ID but authentication fails - likely account needs activation
          const shouldSendActivation = confirm("Your account exists but may need activation or password setup.\n\nWould you like us to send you an activation email? This can help if:\n• Your account was created but never activated\n• You need to set up a password\n• Your account is disabled\n\nClick OK to send activation email, or Cancel to contact support.")

          if (shouldSendActivation) {
            const activationSent = await triggerCustomerActivation(userData.email)
            if (activationSent) {
              alert("Activation email sent! Please check your email (including spam folder) for instructions to activate your account and set up your password.")
            } else {
              alert("Failed to send activation email. Please contact support for assistance.")
            }
          } else {
            alert("Please contact support for assistance with your account activation.")
          }
          return null
        }

        // Account doesn't exist in Shopify - try to create it
        console.log("UNIDENTIFIED_CUSTOMER error - attempting to create account")
        const createResult = await attemptShopifyCustomerCreationAPI(userData, password)
        if (createResult.success) {
          // Customer created successfully, try authentication again
          alert("Your account has been created in our system. Cart sync is now enabled!")
          return await getShopifyCustomerAccessToken()
        } else if (createResult.error === 'CUSTOMER_DISABLED') {
          alert("Your account has been created but needs email verification.\n\nPlease check your email (including spam folder) for a verification link from Shopify, then try cart sync again after clicking the verification link.")
          return null
        } else {
          alert("Account not found in Shopify and automatic account creation failed. This may happen if:\n\n• Your account was created through a different method\n• There's a temporary system issue\n• Password doesn't meet requirements\n\nPlease try logging out and back in, or contact support if the issue persists.")
        }
      } else if (errors.some((e: any) => e.code === "INVALID")) {
        alert("Invalid password. Please check your password and try again.")
      } else {
        alert(`Authentication failed: ${errors[0]?.message || 'Unknown error'}\n\nPlease try again or contact support if the issue persists.`)
      }
    }
  } catch (error) {
    console.error("Error in getShopifyCustomerAccessToken:", error)
    alert("Failed to authenticate with Shopify. Please try again.")
  }

  return null
}

// Helper function to attempt creating a Shopify customer when account is not found
async function attemptShopifyCustomerCreation(userData: any, password: string): Promise<boolean | string> {
  try {
    // Prepare customer input with validation
    const customerInput = {
      email: userData.email,
      password: password,
      firstName: userData.firstName || userData.first_name || 'User',
      lastName: userData.lastName || userData.last_name || 'Customer',
      phone: userData.phone || '',
      acceptsMarketing: false
    }

    // Log the input for debugging (without password)
    console.log("Attempting to create customer with input:", {
      ...customerInput,
      password: '[REDACTED]'
    })

    const response = await fetch('https://benzochem.myshopify.com/api/2025-04/graphql.json', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Storefront-Access-Token': '95029391ce9ba8bad8a604e7faf7eb87'
      },
      body: JSON.stringify({
        query: `
          mutation customerCreate($input: CustomerCreateInput!) {
            customerCreate(input: $input) {
              customer {
                id
                email
                firstName
                lastName
              }
              customerUserErrors {
                field
                message
                code
              }
            }
          }
        `,
        variables: {
          input: customerInput
        }
      })
    })

    const result = await response.json()
    console.log("Customer creation response:", result)

    if (result.data?.customerCreate?.customer) {
      console.log("Successfully created customer:", result.data.customerCreate.customer.id)
      return true
    } else if (result.data?.customerCreate?.customerUserErrors) {
      const errors = result.data.customerCreate.customerUserErrors

      // Check for specific error types
      const customerDisabledError = errors.find((e: any) => e.code === 'CUSTOMER_DISABLED')
      if (customerDisabledError) {
        console.log("Customer exists but is disabled - needs activation")
        return 'CUSTOMER_DISABLED'
      }

      // Check if customer already exists (race condition)
      if (errors.some((e: any) => e.code === 'TAKEN' || e.message.includes('already exists'))) {
        console.log("Customer already exists, proceeding")
        return true
      }

      // Log specific errors for debugging
      console.error("Customer creation failed with errors:", errors)

      // Check for specific validation errors and provide user feedback
      const passwordErrors = errors.filter((e: any) => e.field?.includes('password'))
      const emailErrors = errors.filter((e: any) => e.field?.includes('email'))

      if (passwordErrors.length > 0) {
        console.error("Password validation errors:", passwordErrors)
        // Check for common password issues
        const passwordError = passwordErrors[0]
        if (passwordError.message.toLowerCase().includes('too short') || passwordError.message.toLowerCase().includes('minimum')) {
          alert("Password is too short. Shopify requires passwords to be at least 5 characters long. Please try again with a longer password.")
          return false
        } else if (passwordError.message.toLowerCase().includes('weak') || passwordError.message.toLowerCase().includes('strength')) {
          alert("Password is too weak. Please try a stronger password with a mix of letters and numbers.")
          return false
        }
      }
      if (emailErrors.length > 0) {
        console.error("Email validation errors:", emailErrors)
        const emailError = emailErrors[0]
        if (emailError.code === 'TAKEN') {
          console.log("Email already taken - customer exists")
          return true
        }
      }
    } else {
      console.error("Unexpected response structure:", result)
    }

    return false
  } catch (error) {
    console.error("Error in attemptShopifyCustomerCreation:", error)
    return false
  }
}

// API-based customer creation function
async function attemptShopifyCustomerCreationAPI(userData: any, password: string): Promise<{success: boolean, error?: string}> {
  try {
    console.log("Attempting customer creation via API for:", userData.email)

    const response = await fetch('/api/shopify/customer-activation', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: userData.email,
        password: password,
        firstName: userData.firstName || userData.first_name || 'User',
        lastName: userData.lastName || userData.last_name || 'Customer',
        phone: userData.phone || '',
        action: 'create'
      })
    })

    const result = await response.json()
    console.log("Customer creation API response:", result)

    if (result.success) {
      return { success: true }
    } else {
      return { success: false, error: result.error }
    }
  } catch (error) {
    console.error("Error in API customer creation:", error)
    return { success: false, error: 'API_ERROR' }
  }
}

// Function to trigger customer account activation email using API endpoint
async function triggerCustomerActivation(email: string): Promise<boolean> {
  try {
    console.log("Triggering customer activation for:", email)

    const response = await fetch('/api/shopify/customer-activation', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: email,
        action: 'recover'
      })
    })

    const result = await response.json()
    console.log("Customer activation response:", result)

    if (result.success) {
      console.log("Activation email sent successfully")
      return true
    } else {
      console.log("Activation email failed:", result.error)
      return false
    }
  } catch (error) {
    console.error("Error triggering customer activation:", error)
    return false
  }
}



// Get customer ID from localStorage (extracted from user data)
function getCustomerId(): string | null {
  if (typeof window === "undefined") return null
  try {
    const userData = localStorage.getItem("benzochem_user")
    if (userData) {
      const user = JSON.parse(userData)
      return user.id || null
    }
    return null
  } catch (e) {
    console.error("Failed to get customer ID:", e)
    return null
  }
}



// Check if user is authenticated and try to get token if missing
async function isUserAuthenticated(): Promise<boolean> {
  let customerAccessToken = getCustomerAccessToken()
  const customerId = getCustomerId()

  // If we have customer ID but no access token, try to get one
  if (customerId && !customerAccessToken) {
    customerAccessToken = await getShopifyCustomerAccessToken()
  }

  return !!(customerAccessToken && customerId)
}



// Define a basic structure for the cart for type safety
// This should ideally match the structure returned by your Shopify queries
interface ShopifyCart {
  id: string;
  checkoutUrl: string;
  lines: {
    edges: Array<{
      node: {
        id: string;
        quantity: number;
        merchandise: {
          id: string;
          product: {
            title: string;
            productType?: string;
            images: {
              edges: Array<{
                node: {
                  url: string;
                };
              }>;
            };
            collections?: { // Added optional collections
              edges: Array<{
                node: {
                  id: string;
                  title: string;
                  handle: string;
                };
              }>;
            };
          };
          priceV2: { // Ensure your queries fetch priceV2
            amount: string;
            currencyCode: string;
          };
        };
      };
    }>;
  };
  buyerIdentity?: {
    email?: string;
    phone?: string;
    customer?: {
      id: string;
    };
    countryCode?: string;
  };
  // Add other cart properties as needed, e.g., cost
}

interface CartUserError {
  field: string[];
  message: string;
}

interface CartOperationResponse {
  cart: ShopifyCart | null;
  userErrors?: CartUserError[];
}


// Create a new cart - REQUIRES AUTHENTICATION
export async function createCart(lines: { merchandiseId: string; quantity: number }[] = []): Promise<ShopifyCart | null> {
  try {
    let customerAccessToken = getCustomerAccessToken()
    const customerId = getCustomerId()

    // If no token but we have customer ID, try to get token
    if (!customerAccessToken && customerId) {
      customerAccessToken = await getShopifyCustomerAccessToken()
    }

    // Require authentication for cart creation
    if (!customerAccessToken || !customerId) {
      throw new Error("Authentication required to create cart. Please log in first.")
    }

    // Build cart input with buyer identity
    const cartInput: any = {
      lines: lines.map((line) => ({
        merchandiseId: line.merchandiseId,
        quantity: line.quantity,
      })),
      buyerIdentity: {
        customerAccessToken: customerAccessToken,
      }
    }

    const { data, errors } = await storefrontFetch<{ cartCreate: CartOperationResponse }>({
      query: CART_CREATE_MUTATION,
      variables: {
        input: cartInput,
      },
      includeCustomerToken: true, // Always include customer token since we ensure it exists above
    })

    if (errors) {
        throw new Error("Error creating cart due to GraphQL issues.");
    }

    const cart = data?.cartCreate?.cart
    const userErrors = data?.cartCreate?.userErrors

    if (userErrors && userErrors.length > 0) {
      throw new Error(userErrors.map((e: CartUserError) => e.message).join(", "));
    }

    return cart
  } catch (error) {
    throw error
  }
}

// Get cart by ID
export async function getCart(cartId: string): Promise<ShopifyCart | null> {
  try {
    const customerAccessToken = getCustomerAccessToken()

    const { data, errors } = await storefrontFetch<{ cart: ShopifyCart | null }>({
      query: CART_QUERY,
      variables: { cartId },
      includeCustomerToken: !!customerAccessToken, // Include customer token if available
    })

    if (errors) {
        throw new Error("Error fetching cart due to GraphQL issues.");
    }

    // If cart is null (e.g., ID invalid or cart expired), Shopify returns null for data.cart
    if (data && data.cart === null) {
        return null;
    }

    return data?.cart
  } catch (error) {
    throw error
  }
}

// Add items to cart
export async function addToCart(cartId: string, lines: { merchandiseId: string; quantity: number; attributes?: { key: string; value: string }[] }[]): Promise<ShopifyCart | null> {
  try {
    const customerAccessToken = getCustomerAccessToken()

    const { data, errors } = await storefrontFetch<{ cartLinesAdd: CartOperationResponse }>({
      query: CART_LINES_ADD_MUTATION,
      variables: {
        cartId,
        lines: lines.map((line) => ({
          merchandiseId: line.merchandiseId,
          quantity: line.quantity,
          attributes: line.attributes || []
        })),
      },
      includeCustomerToken: !!customerAccessToken, // Include customer token if available
    })

    if (errors) {
        throw new Error("Error adding to cart due to GraphQL issues.");
    }

    const cart = data?.cartLinesAdd?.cart
    const userErrors = data?.cartLinesAdd?.userErrors

    if (userErrors && userErrors.length > 0) {
      throw new Error(userErrors.map((e: CartUserError) => e.message).join(", "));
    }
    return cart
  } catch (error) {
    throw error
  }
}

// Update cart items
export async function updateCartItem(cartId: string, lineId: string, quantity: number): Promise<ShopifyCart | null> {
  try {
    const customerAccessToken = getCustomerAccessToken()

    const { data, errors } = await storefrontFetch<{ cartLinesUpdate: CartOperationResponse }>({
      query: CART_LINES_UPDATE_MUTATION,
      variables: {
        cartId,
        lines: [
          {
            id: lineId,
            quantity,
          },
        ],
      },
      includeCustomerToken: !!customerAccessToken, // Include customer token if available
    })

    if (errors) {
        throw new Error("Error updating cart item due to GraphQL issues.");
    }

    const cart = data?.cartLinesUpdate?.cart
    const userErrors = data?.cartLinesUpdate?.userErrors

    if (userErrors && userErrors.length > 0) {
      throw new Error(userErrors.map((e: CartUserError) => e.message).join(", "));
    }
    return cart
  } catch (error) {
    throw error
  }
}

// Remove items from cart
export async function removeFromCart(cartId: string, lineIds: string[]): Promise<ShopifyCart | null> {
  try {
    const customerAccessToken = getCustomerAccessToken()

    const { data, errors } = await storefrontFetch<{ cartLinesRemove: CartOperationResponse }>({
      query: CART_LINES_REMOVE_MUTATION,
      variables: {
        cartId,
        lineIds,
      },
      includeCustomerToken: !!customerAccessToken, // Include customer token if available
    })

    if (errors) {
        console.error("GraphQL errors during cartLinesRemove:", errors);
        throw new Error("Error removing from cart due to GraphQL issues.");
    }

    const cart = data?.cartLinesRemove?.cart
    const userErrors = data?.cartLinesRemove?.userErrors

    if (userErrors && userErrors.length > 0) {
      // Note: Shopify might return errors if a lineId is invalid, but still process valid ones.
      // Depending on desired behavior, you might not want to throw for all userErrors here.
    }
    return cart
  } catch (error) {
    throw error
  }
}

// Get or create cart - REQUIRES AUTHENTICATION
export async function getOrCreateCart(initialCartId?: string | null): Promise<ShopifyCart | null> {
  // Check if user is authenticated first (async to allow token retrieval)
  const isAuthenticated = await isUserAuthenticated()
  if (!isAuthenticated) {
    return null
  }

  // If a cart ID is provided, try to fetch it
  if (initialCartId) {
    try {
      const cart = await getCart(initialCartId)
      if (cart) {
        return cart
      }
    } catch (error) {
      // Will create new cart below
    }
  }

  // Create new cart (requires authentication)
  try {
    return await createCart() // Create an empty cart
  } catch (error) {
    return null;
  }
}

// Update cart buyer identity to associate with authenticated customer
export async function updateCartBuyerIdentity(cartId: string): Promise<ShopifyCart | null> {
  try {
    const customerAccessToken = getCustomerAccessToken()

    if (!customerAccessToken) {
      console.warn("Cannot update cart buyer identity - no customer access token available")
      return null
    }

    const { data, errors } = await storefrontFetch<{ cartBuyerIdentityUpdate: CartOperationResponse }>({
      query: CART_BUYER_IDENTITY_UPDATE_MUTATION,
      variables: {
        cartId,
        buyerIdentity: {
          customerAccessToken: customerAccessToken,
        },
      },
      includeCustomerToken: true,
    })

    if (errors) {
      console.error("GraphQL errors during cartBuyerIdentityUpdate:", errors);
      throw new Error("Error updating cart buyer identity due to GraphQL issues.");
    }

    const cart = data?.cartBuyerIdentityUpdate?.cart
    const userErrors = data?.cartBuyerIdentityUpdate?.userErrors

    if (userErrors && userErrors.length > 0) {
      console.error("User errors updating cart buyer identity:", userErrors)
      throw new Error(userErrors.map((e: CartUserError) => e.message).join(", "));
    }

    if (cart) {
      console.log("Cart buyer identity updated successfully:", cart.id, "Customer associated:", !!cart.buyerIdentity?.customer?.id)
    }

    return cart
  } catch (error) {
    console.error("Error in updateCartBuyerIdentity function:", error)
    throw error
  }
}
