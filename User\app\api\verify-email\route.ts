import { NextResponse } from 'next/server';
import { getShopifyConfig } from '@/lib/env-validation';

export async function POST(request: Request) {
  let shopifyConfig;

  try {
    shopifyConfig = getShopifyConfig(true); // Include server-side variables
    console.log('🔧 Shopify configuration loaded for email verification');
  } catch (error) {
    console.error("Shopify environment variables are not properly configured:", error);
    return NextResponse.json({
      isAvailable: false,
      message: "Server configuration error."
    }, { status: 500 });
  }

  try {
    const { email } = await request.json();

    if (!email || typeof email !== 'string') {
      return NextResponse.json({ 
        isAvailable: false, 
        message: "Email is required." 
      }, { status: 400 });
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json({ 
        isAvailable: false, 
        message: "Invalid email format." 
      }, { status: 400 });
    }

    // Check if email exists in Shopify customers
    const shopifyUrl = `https://${shopifyConfig.storeDomain}/admin/api/${shopifyConfig.apiVersion}/customers/search.json?query=email:${encodeURIComponent(email)}`;

    console.log(`Checking email availability in Shopify: ${email}`);

    const response = await fetch(shopifyUrl, {
      method: 'GET',
      headers: {
        'X-Shopify-Access-Token': shopifyConfig.adminAccessToken!,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      console.error(`Shopify API error: ${response.status} ${response.statusText}`);
      return NextResponse.json({ 
        isAvailable: false, 
        message: "Error checking email availability." 
      }, { status: 500 });
    }

    const data = await response.json();
    console.log("Shopify email search response:", JSON.stringify(data, null, 2));

    const isEmailTaken = data.customers && data.customers.length > 0;

    return NextResponse.json({
      isAvailable: !isEmailTaken,
      message: isEmailTaken 
        ? "This email is already registered. Please use a different email or sign in." 
        : "Email is available.",
      email: email
    }, { status: 200 });

  } catch (error: any) {
    console.error("API Error verifying email:", error);
    let message = "An unexpected error occurred while checking email availability.";
    if (error.message) {
      message = error.message;
    }
    return NextResponse.json({ 
      isAvailable: false, 
      message 
    }, { status: 500 });
  }
}
