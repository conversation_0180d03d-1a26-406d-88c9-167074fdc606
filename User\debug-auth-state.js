// Debug Authentication State
// Run this in browser console to check current authentication state

function debugAuthState() {
  console.log("=== AUTHENTICATION DEBUG ===");
  
  // Check localStorage directly
  const shopifyToken = localStorage.getItem("shopify_customer_access_token");
  const shopifyTokenExpires = localStorage.getItem("shopify_customer_access_token_expires");
  const userDataRaw = localStorage.getItem("benzochem_user");
  const clerkJWT = localStorage.getItem("clerk-db-jwt");
  
  console.log("📦 localStorage Data:");
  console.log("  shopify_customer_access_token:", shopifyToken ? `${shopifyToken.substring(0, 20)}...` : "❌ NOT FOUND");
  console.log("  shopify_customer_access_token_expires:", shopifyTokenExpires);
  console.log("  benzochem_user:", userDataRaw ? "✅ EXISTS" : "❌ NOT FOUND");
  console.log("  clerk-db-jwt:", clerkJWT ? "✅ EXISTS" : "❌ NOT FOUND");
  
  // Parse user data
  let userData = null;
  if (userDataRaw) {
    try {
      userData = JSON.parse(userDataRaw);
      console.log("👤 User Data:");
      console.log("  ID:", userData.id);
      console.log("  Email:", userData.email);
      console.log("  Name:", userData.name);
    } catch (e) {
      console.log("❌ Error parsing user data:", e);
    }
  }
  
  // Check token expiration
  if (shopifyTokenExpires) {
    const expiryDate = new Date(shopifyTokenExpires);
    const now = new Date();
    const isExpired = now >= expiryDate;
    console.log("⏰ Token Expiration:");
    console.log("  Expires at:", expiryDate.toLocaleString());
    console.log("  Current time:", now.toLocaleString());
    console.log("  Is expired:", isExpired ? "❌ YES" : "✅ NO");
  }
  
  // Check what our functions would return
  console.log("🔧 Function Results:");
  console.log("  getCustomerAccessToken():", shopifyToken ? `${shopifyToken.substring(0, 20)}...` : "null");
  console.log("  getCustomerId():", userData?.id || "null");
  console.log("  isUserAuthenticated():", !!(shopifyToken && userData?.id));
  
  console.log("=== END DEBUG ===");
  
  return {
    hasShopifyToken: !!shopifyToken,
    hasUserData: !!userData,
    tokenExpired: shopifyTokenExpires ? new Date() >= new Date(shopifyTokenExpires) : null,
    userId: userData?.id,
    userEmail: userData?.email
  };
}

// Run the debug function
debugAuthState();
