// Environment Variables Test Script
// Run this in browser console to test environment configuration

console.log('🔧 Testing Environment Variables Configuration');
console.log('==============================================');

// Test Shopify configuration
async function testShopifyConfig() {
  console.log('\n📦 Testing Shopify Configuration...');
  
  try {
    // Test email verification API
    console.log('Testing email verification API...');
    const emailResponse = await fetch('/api/verify-email', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email: '<EMAIL>' })
    });
    
    if (emailResponse.ok) {
      console.log('✅ Email verification API: Configuration OK');
    } else {
      const error = await emailResponse.json();
      console.log('❌ Email verification API:', error.message);
    }
  } catch (error) {
    console.log('❌ Email verification API error:', error.message);
  }

  try {
    // Test phone verification API
    console.log('Testing phone verification API...');
    const phoneResponse = await fetch('/api/verify-phone', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ phone: '1234567890', countryCode: '+91' })
    });
    
    if (phoneResponse.ok) {
      console.log('✅ Phone verification API: Configuration OK');
    } else {
      const error = await phoneResponse.json();
      console.log('❌ Phone verification API:', error.message);
    }
  } catch (error) {
    console.log('❌ Phone verification API error:', error.message);
  }

  try {
    // Test customer activation API
    console.log('Testing customer activation API...');
    const activationResponse = await fetch('/api/shopify/customer-activation', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        email: '<EMAIL>', 
        action: 'recover' 
      })
    });
    
    if (activationResponse.ok || activationResponse.status === 400) {
      console.log('✅ Customer activation API: Configuration OK');
    } else {
      const error = await activationResponse.json();
      console.log('❌ Customer activation API:', error.message);
    }
  } catch (error) {
    console.log('❌ Customer activation API error:', error.message);
  }
}

// Test GST verification configuration
async function testGSTConfig() {
  console.log('\n🚀 Testing GST Verification Configuration...');
  
  try {
    const gstResponse = await fetch('/api/verify-gst-details', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ gstNumber: '27AAPFU0939F1ZV' }) // Sample GST number
    });
    
    if (gstResponse.ok || gstResponse.status === 400) {
      console.log('✅ GST verification API: Configuration OK');
    } else {
      const error = await gstResponse.json();
      console.log('❌ GST verification API:', error.message);
    }
  } catch (error) {
    console.log('❌ GST verification API error:', error.message);
  }
}

// Test client-side environment variables
function testClientSideEnv() {
  console.log('\n🌐 Testing Client-side Environment Variables...');
  
  // These should be available on client-side
  const storeDomain = process?.env?.NEXT_PUBLIC_SHOPIFY_STORE_DOMAIN;
  const storefrontToken = process?.env?.NEXT_PUBLIC_SHOPIFY_STOREFRONT_ACCESS_TOKEN;
  
  console.log('Store Domain:', storeDomain ? '✅ Available' : '❌ Missing');
  console.log('Storefront Token:', storefrontToken ? '✅ Available' : '❌ Missing');
  
  // Check if we can access them via window (they should be bundled)
  console.log('\nNote: Client-side env vars are bundled at build time.');
  console.log('Check the Network tab to see if API calls are working.');
}

// Run all tests
async function runAllTests() {
  console.log('🧪 Running Environment Configuration Tests...\n');
  
  testClientSideEnv();
  await testShopifyConfig();
  await testGSTConfig();
  
  console.log('\n✅ Environment testing completed!');
  console.log('Check the results above for any configuration issues.');
}

// Auto-run the tests
runAllTests();
