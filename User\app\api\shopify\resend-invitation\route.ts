import { NextRequest, NextResponse } from 'next/server'
import { getShopifyConfig } from '@/lib/env-validation'

// API to send password reset email for existing customers (since we use welcome emails instead of invitations)
export async function POST(request: NextRequest) {
  let shopifyConfig;

  try {
    shopifyConfig = getShopifyConfig(true); // Need admin access
    console.log('🔧 Shopify configuration loaded for sending password reset');
  } catch (error) {
    console.error("Shopify environment variables are not properly configured:", error);
    return NextResponse.json(
      { error: 'Server configuration error' },
      { status: 500 }
    )
  }

  try {
    const { email } = await request.json()

    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      )
    }

    console.log('Sending password reset email for:', email)

    // Since we're using welcome emails instead of invitations, we'll send a password reset email
    // Use Shopify Storefront API to send password recovery email
    const response = await fetch(`https://${shopifyConfig.storeDomain}/api/${shopifyConfig.apiVersion}/graphql.json`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Storefront-Access-Token': shopifyConfig.storefrontAccessToken
      },
      body: JSON.stringify({
        query: `
          mutation customerRecover($email: String!) {
            customerRecover(email: $email) {
              customerUserErrors {
                field
                message
                code
              }
            }
          }
        `,
        variables: { email }
      })
    })

    const result = await response.json()
    console.log('Password recovery response:', JSON.stringify(result, null, 2))

    if (result.data?.customerRecover?.customerUserErrors?.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'Password reset email sent successfully! Please check your email (including spam folder) for instructions to reset your password.'
      })
    } else {
      const errors = result.data?.customerRecover?.customerUserErrors || []
      console.error('Password recovery failed:', errors)

      // Check if customer doesn't exist
      const customerNotFound = errors.some(error =>
        error.code === 'UNIDENTIFIED_CUSTOMER' ||
        error.message?.toLowerCase().includes('not found')
      )

      if (customerNotFound) {
        return NextResponse.json({
          success: false,
          error: 'CUSTOMER_NOT_FOUND',
          message: 'No customer found with this email address.'
        }, { status: 404 })
      }

      return NextResponse.json({
        success: false,
        error: 'RECOVERY_FAILED',
        message: 'Failed to send password reset email. Please try again or contact support.',
        details: errors
      }, { status: 400 })
    }

  } catch (error) {
    console.error('Password reset API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
