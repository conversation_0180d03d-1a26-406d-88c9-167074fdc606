"use client"

import type React from "react"
import { createContext, useContext, useEffect, useState, useRef } from "react"
import { useRouter } from "next/navigation"
import type { UserData } from "@/lib/auth"
import { loginUser, isAuthenticated } from "@/lib/auth"

import { updateCustomerBusinessPreferences } from "@/actions/shopifyActions"
import { useRealTimeUserData } from "@/hooks/useRealTimeUserData"

interface AuthContextType {
  user: UserData | null
  isLoading: boolean
  isRefreshing: boolean
  lastUpdated: Date | null
  login: (email: string, password: string) => Promise<{ success: boolean; error?: string }>
  register: (userData: {
    firstName: string
    lastName: string
    email: string
    password: string
    phone: string
    businessName?: string
    gstNumber?: string
    // Business information
    legalNameOfBusiness?: string
    tradeName?: string
    dateOfRegistration?: string
    constitutionOfBusiness?: string
    taxpayerType?: string
    principalPlaceOfBusiness?: string
    natureOfCoreBusinessActivity?: string
    // Marketing consent
    agreedToEmailMarketing?: boolean
    agreedToSmsMarketing?: boolean
  }) => Promise<{ success: boolean; error?: string; message?: string }>
  logout: () => Promise<void>
  verifyGST: (gstNumber: string) => Promise<{ success: boolean; error?: string; data?: any }>
  refreshUserData: () => Promise<void>
  enableRealTimeUpdates: (enabled: boolean) => void
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}

export const ShopifyAuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [user, setUser] = useState<UserData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [realTimeEnabled, setRealTimeEnabled] = useState(true)
  const router = useRouter()

  // Use a ref to track the current user state to prevent unnecessary re-renders
  const userRef = useRef<UserData | null>(null)

  // Real-time user data hook
  const {
    userData: realTimeUserData,
    isRefreshing,
    lastUpdated,
    refreshUserData: refreshRealTimeData,
    startPolling,
    stopPolling
  } = useRealTimeUserData(user, {
    enablePolling: realTimeEnabled,
    pollingInterval: 30000, // 30 seconds
    onDataUpdate: (updatedData) => {
      // Update user state when real-time data changes
      if (JSON.stringify(updatedData) !== JSON.stringify(userRef.current)) {
        setUser(updatedData)
        userRef.current = updatedData

        // Dispatch custom event for other components to listen
        window.dispatchEvent(new CustomEvent('user-data-updated', {
          detail: updatedData
        }))
      }
    },
    onError: (error) => {
      console.error('Real-time user data error:', error)
    }
  })

  // Initialize auth state from localStorage
  useEffect(() => {
    const initializeAuth = () => {
      try {
        const storedUserData = localStorage.getItem("benzochem_user")
        if (storedUserData) {
          const userData = JSON.parse(storedUserData)
          
          // Validate stored user data
          if (userData && userData.email) {
            // Only update user state if it's different from current state
            const currentUserStr = userRef.current ? JSON.stringify(userRef.current) : null
            const newUserStr = JSON.stringify(userData)
            
            if (currentUserStr !== newUserStr) {
              userRef.current = userData
              setUser(userData)
            }
          }
        }
      } catch (e) {
        console.error("Error parsing stored user data", e)
        // Clean up corrupted data
        localStorage.removeItem("benzochem_user")
        if (userRef.current !== null) {
          setUser(null)
          userRef.current = null
        }
      }
      setIsLoading(false)
    }

    initializeAuth()

    // Listen for auth state changes from other tabs/windows
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === "benzochem_user") {
        if (e.newValue) {
          try {
            const userData = JSON.parse(e.newValue)
            setUser(userData)
            userRef.current = userData
          } catch (error) {
            console.error("Error parsing user data from storage event", error)
          }
        } else {
          // User data was removed (logout)
          setUser(null)
          userRef.current = null
        }
      }
    }

    window.addEventListener('storage', handleStorageChange)
    return () => window.removeEventListener('storage', handleStorageChange)
  }, [])

  const login = async (email: string, password: string) => {
    setIsLoading(true)
    try {
      // Input validation
      if (!email || !email.includes('@') || !password) {
        setIsLoading(false)
        return { success: false, error: "Please enter a valid email and password" }
      }

      // Attempt Shopify authentication
      const shopifyLoginResult = await loginUser(email, password)
      if (shopifyLoginResult.success && shopifyLoginResult.user) {
        const userData: UserData = shopifyLoginResult.user
        
        // Store user data in localStorage
        localStorage.setItem("benzochem_user", JSON.stringify(userData))
        
        // Update user state
        setUser(userData)
        userRef.current = userData
        
        // Trigger auth state change event
        setTimeout(() => {
          window.dispatchEvent(new CustomEvent('auth-state-change'))
        }, 0)
        
        setIsLoading(false)
        return { success: true }
      } else {
        setIsLoading(false)
        return { 
          success: false, 
          error: shopifyLoginResult.error || "Authentication failed" 
        }
      }
    } catch (error: any) {
      console.error("Login error:", error)
      setIsLoading(false)
      
      // Preserve specific error messages from Shopify
      if (error.message) {
        return { 
          success: false, 
          error: error.message
        }
      }
      
      return { 
        success: false, 
        error: "An unexpected error occurred. Please try again." 
      }
    }
  }

  const register = async (userData: {
    firstName: string
    lastName: string
    email: string
    password: string
    phone: string
    businessName?: string
    gstNumber?: string
    // Business information
    legalNameOfBusiness?: string
    tradeName?: string
    dateOfRegistration?: string
    constitutionOfBusiness?: string
    taxpayerType?: string
    principalPlaceOfBusiness?: string
    natureOfCoreBusinessActivity?: string
    gstStatus?: string
    // Marketing consent
    agreedToEmailMarketing?: boolean
    agreedToSmsMarketing?: boolean
  }) => {
    setIsLoading(true)
    console.log("Registration data received:", {
      email: userData.email,
      agreedToEmailMarketing: userData.agreedToEmailMarketing,
      agreedToSmsMarketing: userData.agreedToSmsMarketing
    })

    try {
      // Step 1: Create customer account with welcome email (accounts are activated immediately)
      const response = await fetch('/api/shopify/send-customer-invite', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: userData.email,
          firstName: userData.firstName,
          lastName: userData.lastName,
          password: userData.password,
          phone: userData.phone,
          acceptsMarketing: userData.agreedToEmailMarketing,
          acceptsSmsMarketing: userData.agreedToSmsMarketing
        })
      })

      const result = await response.json()

      if (result.success && result.customer) {
        const customerId = result.customer.id
        console.log("Customer created successfully, storing metafields for:", customerId)

        // Step 2: Store GST information as metafields if provided
        if (userData.gstNumber || userData.legalNameOfBusiness) {
          console.log("Storing GST information for customer:", customerId)

          try {
            // Import here to avoid circular dependencies
            const { updateCustomerGSTInfo } = await import('@/actions/shopifyActions')

            const gstResult = await updateCustomerGSTInfo(customerId, {
              gstNumber: userData.gstNumber,
              legalNameOfBusiness: userData.legalNameOfBusiness,
              tradeName: userData.tradeName,
              dateOfRegistration: userData.dateOfRegistration,
              constitutionOfBusiness: userData.constitutionOfBusiness,
              taxpayerType: userData.taxpayerType,
              principalPlaceOfBusinessAddress: userData.principalPlaceOfBusiness,
              natureOfCoreBusinessActivity: userData.natureOfCoreBusinessActivity,
              gstStatus: userData.gstStatus || 'Active'
            })

            if (gstResult.success) {
              console.log("✅ GST information stored successfully during registration")
            } else {
              console.error("❌ Failed to store GST information:", gstResult.error)
              // Don't fail the registration, just log the error
            }
          } catch (error) {
            console.error("❌ Error storing GST information:", error)
            // Don't fail the registration, just log the error
          }
        }

        // Step 3: Update marketing consent using GraphQL API for better reliability
        if (userData.agreedToEmailMarketing !== undefined || userData.agreedToSmsMarketing !== undefined) {
          console.log("Updating marketing consent for customer:", customerId)

          try {
            // Import here to avoid circular dependencies
            const { updateCustomerMarketingConsent } = await import('@/actions/shopifyActions')

            const consentResult = await updateCustomerMarketingConsent(customerId, {
              emailMarketing: userData.agreedToEmailMarketing,
              smsMarketing: userData.agreedToSmsMarketing
            })

            if (consentResult.success) {
              console.log("✅ Marketing consent updated successfully during registration")
            } else {
              console.error("❌ Failed to update marketing consent:", consentResult.error)
              // Don't fail the registration, just log the error
            }
          } catch (error) {
            console.error("❌ Error updating marketing consent:", error)
            // Don't fail the registration, just log the error
          }
        }

        // Step 4: Store business preferences and marketing consent as metafields
        console.log("Storing business preferences and marketing consent for customer:", customerId)
        console.log("Marketing consent:", {
          email: userData.agreedToEmailMarketing,
          sms: userData.agreedToSmsMarketing
        })

        try {
          const preferencesResult = await updateCustomerBusinessPreferences(customerId, {
            businessType: "B2B",
            industryType: "Chemicals",
            businessCategory: userData.businessName ? "Trading" : "Individual",
            notes: `Marketing Consent - Email: ${userData.agreedToEmailMarketing ? 'Yes' : 'No'}, SMS: ${userData.agreedToSmsMarketing ? 'Yes' : 'No'}`
          })

          if (preferencesResult.success) {
            console.log("✅ Business preferences and marketing consent stored successfully")
          } else {
            console.error("❌ Failed to store business preferences:", preferencesResult.error)
            // Don't throw here, just log the error and continue
          }
        } catch (error) {
          console.error("❌ Error storing business preferences:", error)
          // Don't throw here, just log the error and continue
        }

        setIsLoading(false)
        return {
          success: true,
          message: result.message || "Account created successfully! Your account is now active and you can log in."
        }
      } else if (result.error === 'CUSTOMER_EXISTS') {
        setIsLoading(false)
        return {
          success: false,
          error: "An account with this email already exists. Please try logging in instead, or use 'Forgot Password' if you need to reset your password."
        }
      } else {
        setIsLoading(false)
        return {
          success: false,
          error: result.message || "Failed to create account. Please try again."
        }
      }
    } catch (error) {
      console.error("Registration error:", error)
      setIsLoading(false)
      return {
        success: false,
        error: "An unexpected error occurred. Please try again."
      }
    }
  }

  const logout = async () => {
    try {
      // Clear localStorage
      localStorage.removeItem("benzochem_user")
      localStorage.removeItem("shopify_customer_access_token")
      localStorage.removeItem("shopify_customer_access_token_expires")
      
      // Update state
      setUser(null)
      userRef.current = null
      
      // Trigger auth state change event
      window.dispatchEvent(new CustomEvent('auth-state-change'))
      
      // Redirect to home page
      router.push('/')
    } catch (error) {
      console.error("Logout error:", error)
    }
  }

  const verifyGST = async (gstNumber: string) => {
    try {
      const response = await fetch('/api/verify-gst', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ gstNumber }),
      })

      const result = await response.json()
      return result
    } catch (error) {
      console.error("GST verification error:", error)
      return {
        success: false,
        error: "Failed to verify GST number. Please try again."
      }
    }
  }

  // Manual refresh function
  const refreshUserData = async () => {
    try {
      await refreshRealTimeData()
    } catch (error) {
      console.error('Error refreshing user data:', error)
    }
  }

  // Enable/disable real-time updates
  const enableRealTimeUpdates = (enabled: boolean) => {
    setRealTimeEnabled(enabled)
    if (enabled) {
      startPolling()
    } else {
      stopPolling()
    }
  }

  const value = {
    user,
    isLoading,
    isRefreshing,
    lastUpdated,
    login,
    register,
    logout,
    verifyGST,
    refreshUserData,
    enableRealTimeUpdates,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}
