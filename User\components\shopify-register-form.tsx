"use client"

import { useState, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useAuth } from "@/contexts/shopify-auth-context"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { Eye, EyeOff, Loader2, User, Building } from "lucide-react"

export default function ShopifyRegisterForm() {
  const { register, isLoading } = useAuth()
  const router = useRouter()
  
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    phone: "",
    countryCode: "+91",
    gstNumber: "",
    legalNameOfBusiness: "",
    tradeName: "",
    dateOfRegistration: "",
    constitutionOfBusiness: "",
    taxpayerType: "",
    principalPlaceOfBusiness: "",
    natureOfCoreBusinessActivity: "",
    agreedToEmailMarketing: false,
    agreedToSmsMarketing: false,
  })

  const [fieldErrors, setFieldErrors] = useState<Record<string, string | null>>({})
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [showPassword, setShowPassword] = useState(false)
  const [currentTab, setCurrentTab] = useState("personal")
  const [isVerifyingGST, setIsVerifyingGST] = useState(false)
  const [gstVerified, setGstVerified] = useState(false)
  const [gstVerificationMessage, setGstVerificationMessage] = useState("")

  // Email verification states
  const [isVerifyingEmail, setIsVerifyingEmail] = useState(false)
  const [emailVerificationStatus, setEmailVerificationStatus] = useState<'idle' | 'checking' | 'available' | 'taken'>('idle')
  const [emailVerificationMessage, setEmailVerificationMessage] = useState("")

  // Phone verification states
  const [isVerifyingPhone, setIsVerifyingPhone] = useState(false)
  const [phoneVerificationStatus, setPhoneVerificationStatus] = useState<'idle' | 'checking' | 'available' | 'taken'>('idle')
  const [phoneVerificationMessage, setPhoneVerificationMessage] = useState("")

  // Refs for debouncing
  const emailTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const phoneTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Validation functions
  const validateEmail = (email: string) => {
    if (!email) return "Please enter your email address"
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) return "Please enter a valid email address"
    return null
  }

  const validatePassword = (password: string) => {
    if (!password) return "Please enter a password"
    if (password.length < 5) return "Password must be at least 5 characters long"
    return null
  }

  const validatePhone = (phone: string, countryCode: string) => {
    if (!phone) return "Please enter your phone number"
    if (!countryCode) return "Please select a country code"
    const fullPhone = `${countryCode}${phone}`
    const phoneRegex = /^\+[1-9]\d{1,14}$/
    if (!phoneRegex.test(fullPhone)) {
      return "Please enter a valid phone number including country code"
    }
    return null
  }

  const validateName = (name: string, field: "firstName" | "lastName") => {
    if (!name) return `Please enter your ${field === "firstName" ? "first" : "last"} name`
    if (name.length < 2) return `${field === "firstName" ? "First" : "Last"} name must be at least 2 characters long`
    return null
  }

  const validatePersonalInfo = () => {
    const errors: Record<string, string | null> = {}
    errors.firstName = validateName(formData.firstName, "firstName")
    errors.lastName = validateName(formData.lastName, "lastName")

    // Email validation with verification status
    const emailError = validateEmail(formData.email)
    if (emailError) {
      errors.email = emailError
    } else if (emailVerificationStatus === 'taken') {
      errors.email = "This email is already registered"
    } else if (emailVerificationStatus === 'checking') {
      errors.email = "Please wait for email verification to complete"
    } else {
      errors.email = null
    }

    errors.password = validatePassword(formData.password)

    // Phone validation with verification status
    const phoneError = validatePhone(formData.phone, formData.countryCode)
    if (phoneError) {
      errors.phone = phoneError
    } else if (phoneVerificationStatus === 'taken') {
      errors.phone = "This phone number is already registered"
    } else if (phoneVerificationStatus === 'checking') {
      errors.phone = "Please wait for phone verification to complete"
    } else {
      errors.phone = null
    }

    setFieldErrors(errors)
    return Object.values(errors).every((err) => err === null)
  }

  // Email verification function
  const verifyEmail = async (email: string) => {
    if (!email || validateEmail(email) !== null) {
      setEmailVerificationStatus('idle')
      setEmailVerificationMessage("")
      return
    }

    setIsVerifyingEmail(true)
    setEmailVerificationStatus('checking')
    setEmailVerificationMessage("Checking email availability...")

    try {
      const response = await fetch('/api/verify-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()

      if (result.isAvailable) {
        setEmailVerificationStatus('available')
        setEmailVerificationMessage("✓ Email is available")
      } else {
        setEmailVerificationStatus('taken')
        setEmailVerificationMessage(result.message || "Email is already registered")
      }
    } catch (error) {
      console.error("Email verification failed:", error)
      setEmailVerificationStatus('idle')
      setEmailVerificationMessage("Unable to verify email. Please try again.")
    } finally {
      setIsVerifyingEmail(false)
    }
  }

  // Phone verification function
  const verifyPhone = async (phone: string, countryCode: string) => {
    if (!phone || validatePhone(phone, countryCode) !== null) {
      setPhoneVerificationStatus('idle')
      setPhoneVerificationMessage("")
      return
    }

    setIsVerifyingPhone(true)
    setPhoneVerificationStatus('checking')
    setPhoneVerificationMessage("Checking phone availability...")

    try {
      const response = await fetch('/api/verify-phone', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ phone, countryCode }),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()

      if (result.isAvailable) {
        setPhoneVerificationStatus('available')
        setPhoneVerificationMessage("✓ Phone number is available")
      } else {
        setPhoneVerificationStatus('taken')
        setPhoneVerificationMessage(result.message || "Phone number is already registered")
      }
    } catch (error) {
      console.error("Phone verification failed:", error)
      setPhoneVerificationStatus('idle')
      setPhoneVerificationMessage("Unable to verify phone. Please try again.")
    } finally {
      setIsVerifyingPhone(false)
    }
  }

  const handleVerifyGST = async () => {
    if (!formData.gstNumber) {
      setGstVerificationMessage("Please enter a GST number")
      return
    }

    setIsVerifyingGST(true)
    setGstVerificationMessage("")

    try {
      const response = await fetch('/api/verify-gst-details', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ gstNumber: formData.gstNumber }),
      })

      const result = await response.json()

      if (result.isValid) {
        // Populate form fields with verified GST data
        setFormData(prev => ({
          ...prev,
          legalNameOfBusiness: result.legalNameOfBusiness || "",
          tradeName: result.tradeName || "",
          dateOfRegistration: result.dateOfRegistration || "",
          constitutionOfBusiness: result.constitutionOfBusiness || "",
          taxpayerType: result.taxpayerType || "",
          principalPlaceOfBusiness: result.principalPlaceOfBusiness || "",
          natureOfCoreBusinessActivity: result.natureOfCoreBusinessActivity || "",
        }))

        setGstVerified(true)
        setGstVerificationMessage("GST verified successfully! Business details have been populated.")
      } else {
        setGstVerified(false)
        setGstVerificationMessage(result.message || "GST verification failed")
      }
    } catch (error) {
      console.error("GST verification failed:", error)
      setGstVerified(false)
      setGstVerificationMessage("Failed to verify GST. Please try again.")
    } finally {
      setIsVerifyingGST(false)
    }
  }

  const handleContinueToBusinessInfo = () => {
    if (validatePersonalInfo()) {
      setCurrentTab("business")
    }
  }

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear field error when user starts typing
    if (fieldErrors[field]) {
      setFieldErrors(prev => ({ ...prev, [field]: null }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError(null)
    setSuccess(null)

    if (!validatePersonalInfo()) {
      return
    }

    try {
      const result = await register({
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        password: formData.password,
        phone: `${formData.countryCode}${formData.phone}`,
        businessName: formData.legalNameOfBusiness || formData.tradeName,
        gstNumber: formData.gstNumber,
        // Business information
        legalNameOfBusiness: formData.legalNameOfBusiness,
        tradeName: formData.tradeName,
        dateOfRegistration: formData.dateOfRegistration,
        constitutionOfBusiness: formData.constitutionOfBusiness,
        taxpayerType: formData.taxpayerType,
        principalPlaceOfBusiness: formData.principalPlaceOfBusiness,
        natureOfCoreBusinessActivity: formData.natureOfCoreBusinessActivity,
        // Marketing consent
        agreedToEmailMarketing: formData.agreedToEmailMarketing,
        agreedToSmsMarketing: formData.agreedToSmsMarketing,
      })

      if (result.success) {
        setSuccess("🎉 Account created successfully! Your account is now active and you can start using it immediately. Please check your email for welcome instructions.")
        // Clear form data
        setFormData({
          firstName: "",
          lastName: "",
          email: "",
          password: "",
          phone: "",
          countryCode: "+91",
          legalNameOfBusiness: "",
          tradeName: "",
          gstNumber: "",
          dateOfRegistration: "",
          constitutionOfBusiness: "",
          taxpayerType: "",
          principalPlaceOfBusiness: "",
          natureOfCoreBusinessActivity: "",
          agreedToEmailMarketing: false,
          agreedToSmsMarketing: false,
        })
        // Reset verification states
        setEmailVerificationStatus('idle')
        setEmailVerificationMessage("")
        setPhoneVerificationStatus('idle')
        setPhoneVerificationMessage("")
        setGstVerified(false)
        setGstVerificationMessage("")
        // Redirect to login page after a delay
        setTimeout(() => {
          router.push('/login?message=Account created successfully! You can now log in with your credentials.')
        }, 4000)
      } else {
        setError(result.error || "Registration failed. Please try again.")
      }
    } catch (error) {
      console.error("Registration error:", error)
      setError("An unexpected error occurred. Please try again.")
    }
  }

  return (
    <div className="max-w-md mx-auto">
      {error && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-md mb-4">
          <p className="text-red-600 text-xs">{error}</p>
        </div>
      )}

      {success && (
        <div className="p-4 bg-green-50 border border-green-200 rounded-md mb-4">
          <div className="text-green-600 text-sm">
            <p className="font-medium mb-2">{success}</p>
            <div className="text-xs space-y-1">
              <p>📧 <strong>Check your email:</strong> You'll receive a welcome message with account details</p>
              <p>📱 <strong>Check spam folder:</strong> Sometimes emails end up there</p>
              <p>🔑 <strong>Ready to login:</strong> Your account is active and you can log in immediately</p>
              <p>🛍️ <strong>Start shopping:</strong> Browse our catalog and place orders right away</p>
            </div>
          </div>
        </div>
      )}

      <Tabs value={currentTab} onValueChange={setCurrentTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2 mb-6">
          <TabsTrigger value="personal" className="flex items-center gap-2 text-sm">
            <User size={14} />
            Personal Info
          </TabsTrigger>
          <TabsTrigger value="business" className="flex items-center gap-2 text-sm">
            <Building size={14} />
            Business Info
          </TabsTrigger>
        </TabsList>

        <TabsContent value="personal" className="space-y-4">
          <div className="space-y-4">
            <h2 className="text-lg font-semibold text-gray-900">Your Details</h2>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="firstName" className="text-sm font-medium text-gray-700">First Name</Label>
                <Input
                  id="firstName"
                  type="text"
                  value={formData.firstName}
                  onChange={(e) => handleInputChange("firstName", e.target.value)}
                  className={`h-10 text-sm border-gray-200 focus:border-teal-500 focus:ring-1 focus:ring-teal-500 ${fieldErrors.firstName ? "border-red-500" : ""}`}
                  placeholder="Enter your first name"
                />
                {fieldErrors.firstName && (
                  <p className="text-red-500 text-xs mt-1">{fieldErrors.firstName}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="lastName" className="text-sm font-medium text-gray-700">Last Name</Label>
                <Input
                  id="lastName"
                  type="text"
                  value={formData.lastName}
                  onChange={(e) => handleInputChange("lastName", e.target.value)}
                  className={`h-10 text-sm border-gray-200 focus:border-teal-500 focus:ring-1 focus:ring-teal-500 ${fieldErrors.lastName ? "border-red-500" : ""}`}
                  placeholder="Enter your last name"
                />
                {fieldErrors.lastName && (
                  <p className="text-red-500 text-xs mt-1">{fieldErrors.lastName}</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="email" className="text-sm font-medium text-gray-700">Email Address</Label>
              <div className="relative">
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => {
                    const email = e.target.value
                    handleInputChange("email", email)

                    // Reset verification status when email changes
                    if (emailVerificationStatus !== 'idle') {
                      setEmailVerificationStatus('idle')
                      setEmailVerificationMessage("")
                    }

                    // Debounce email verification
                    if (emailTimeoutRef.current) {
                      clearTimeout(emailTimeoutRef.current)
                    }
                    emailTimeoutRef.current = setTimeout(() => {
                      verifyEmail(email)
                    }, 1000)
                  }}
                  className={`h-10 text-sm border-gray-200 focus:border-teal-500 focus:ring-1 focus:ring-teal-500 pr-8 ${
                    fieldErrors.email ? "border-red-500" :
                    emailVerificationStatus === 'available' ? "border-green-500" :
                    emailVerificationStatus === 'taken' ? "border-red-500" : ""
                  }`}
                  placeholder="Enter your email address"
                />
                {isVerifyingEmail && (
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
                  </div>
                )}
                {emailVerificationStatus === 'available' && (
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <span className="text-green-500 text-sm">✓</span>
                  </div>
                )}
                {emailVerificationStatus === 'taken' && (
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <span className="text-red-500 text-sm">✗</span>
                  </div>
                )}
              </div>
              {fieldErrors.email && (
                <p className="text-red-500 text-xs mt-1">{fieldErrors.email}</p>
              )}
              {emailVerificationMessage && !fieldErrors.email && (
                <p className={`text-xs mt-1 ${
                  emailVerificationStatus === 'available' ? 'text-green-600' :
                  emailVerificationStatus === 'taken' ? 'text-red-600' :
                  'text-gray-600'
                }`}>
                  {emailVerificationMessage}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="password" className="text-sm font-medium text-gray-700">Password</Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  value={formData.password}
                  onChange={(e) => handleInputChange("password", e.target.value)}
                  className={`h-10 text-sm border-gray-200 focus:border-teal-500 focus:ring-1 focus:ring-teal-500 pr-10 ${fieldErrors.password ? "border-red-500" : ""}`}
                  placeholder="Create a password"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                </button>
              </div>
              {fieldErrors.password && (
                <p className="text-red-500 text-xs mt-1">{fieldErrors.password}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="phone" className="text-sm font-medium text-gray-700">Phone Number</Label>
              <div className="flex gap-2">
                <Select value={formData.countryCode} onValueChange={(value) => {
                  handleInputChange("countryCode", value)
                  // Re-verify phone when country code changes
                  if (formData.phone) {
                    if (phoneTimeoutRef.current) {
                      clearTimeout(phoneTimeoutRef.current)
                    }
                    phoneTimeoutRef.current = setTimeout(() => {
                      verifyPhone(formData.phone, value)
                    }, 500)
                  }
                }}>
                  <SelectTrigger className="w-24 h-10 border-gray-200 focus:border-teal-500 focus:ring-1 focus:ring-teal-500 text-sm">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="+91">🇮🇳 +91</SelectItem>
                    <SelectItem value="+1">🇺🇸 +1</SelectItem>
                    <SelectItem value="+44">🇬🇧 +44</SelectItem>
                  </SelectContent>
                </Select>
                <div className="relative flex-1">
                  <Input
                    id="phone"
                    type="tel"
                    value={formData.phone}
                    onChange={(e) => {
                      const phone = e.target.value
                      handleInputChange("phone", phone)

                      // Reset verification status when phone changes
                      if (phoneVerificationStatus !== 'idle') {
                        setPhoneVerificationStatus('idle')
                        setPhoneVerificationMessage("")
                      }

                      // Debounce phone verification
                      if (phoneTimeoutRef.current) {
                        clearTimeout(phoneTimeoutRef.current)
                      }
                      phoneTimeoutRef.current = setTimeout(() => {
                        verifyPhone(phone, formData.countryCode)
                      }, 1000)
                    }}
                    className={`h-10 text-sm border-gray-200 focus:border-teal-500 focus:ring-1 focus:ring-teal-500 pr-8 ${
                      fieldErrors.phone ? "border-red-500" :
                      phoneVerificationStatus === 'available' ? "border-green-500" :
                      phoneVerificationStatus === 'taken' ? "border-red-500" : ""
                    }`}
                    placeholder="Enter your phone number"
                  />
                  {isVerifyingPhone && (
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                      <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
                    </div>
                  )}
                  {phoneVerificationStatus === 'available' && (
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                      <span className="text-green-500 text-sm">✓</span>
                    </div>
                  )}
                  {phoneVerificationStatus === 'taken' && (
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                      <span className="text-red-500 text-sm">✗</span>
                    </div>
                  )}
                </div>
              </div>
              {fieldErrors.phone && (
                <p className="text-red-500 text-xs mt-1">{fieldErrors.phone}</p>
              )}
              {phoneVerificationMessage && !fieldErrors.phone && (
                <p className={`text-xs mt-1 ${
                  phoneVerificationStatus === 'available' ? 'text-green-600' :
                  phoneVerificationStatus === 'taken' ? 'text-red-600' :
                  'text-gray-600'
                }`}>
                  {phoneVerificationMessage}
                </p>
              )}
            </div>

            <Button
              type="button"
              onClick={handleContinueToBusinessInfo}
              className="w-full h-10 text-sm bg-teal-600 hover:bg-teal-700 text-white font-medium mt-6"
            >
              Continue
            </Button>

            <p className="text-center text-sm text-gray-600 mt-4">
              Already have an account?{" "}
              <Link href="/login" className="text-teal-600 hover:underline">
                Sign in →
              </Link>
            </p>
          </div>
        </TabsContent>

        <TabsContent value="business" className="space-y-4">
          <form onSubmit={handleSubmit}>
            {/* GST Verification */}
            <div className="space-y-4">
              <h2 className="text-lg font-semibold text-gray-900">GST Verification</h2>

              <div className="space-y-2">
                <Label htmlFor="gstNumber" className="text-sm font-medium text-gray-700">GST Number</Label>
                <div className="flex gap-2">
                  <Input
                    id="gstNumber"
                    type="text"
                    value={formData.gstNumber}
                    onChange={(e) => {
                      handleInputChange("gstNumber", e.target.value)
                      // Reset verification status when GST number changes
                      if (gstVerified) {
                        setGstVerified(false)
                        setGstVerificationMessage("")
                      }
                    }}
                    className="h-10 text-sm border-gray-200 focus:border-teal-500 focus:ring-1 focus:ring-teal-500 flex-1"
                    placeholder="Enter your 15-digit GST number"
                    disabled={gstVerified}
                  />
                  <Button
                    type="button"
                    onClick={handleVerifyGST}
                    disabled={!formData.gstNumber || isVerifyingGST}
                    className={`h-10 px-4 text-sm font-medium ${gstVerified
                      ? 'bg-green-50 text-green-600 border border-green-200'
                      : 'bg-teal-50 text-teal-600 border border-teal-200 hover:bg-teal-100'
                    }`}
                  >
                    {isVerifyingGST ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : gstVerified ? (
                      "✓ Verified"
                    ) : (
                      "Verify GST"
                    )}
                  </Button>
                </div>

                {gstVerificationMessage && (
                  <div className={`p-2 rounded-md text-xs ${
                    gstVerified
                      ? 'bg-green-50 text-green-700 border border-green-200'
                      : 'bg-red-50 text-red-700 border border-red-200'
                  }`}>
                    {gstVerificationMessage}
                  </div>
                )}
              </div>
            </div>

            {/* Business Information */}
            <div className="space-y-4">
              <h2 className="text-lg font-semibold text-gray-900">Business Information</h2>

              <div className="space-y-2">
                <Label htmlFor="legalNameOfBusiness" className="text-sm font-medium text-gray-700">Legal Name of Business</Label>
                <Input
                  id="legalNameOfBusiness"
                  type="text"
                  value={formData.legalNameOfBusiness}
                  onChange={(e) => handleInputChange("legalNameOfBusiness", e.target.value)}
                  className={`h-10 text-sm border-gray-200 focus:border-teal-500 focus:ring-1 focus:ring-teal-500 ${gstVerified ? 'bg-gray-50' : ''}`}
                  placeholder="As per GST registration"
                  disabled={gstVerified}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="tradeName" className="text-sm font-medium text-gray-700">Trade Name <span className="text-gray-500">(Optional)</span></Label>
                <Input
                  id="tradeName"
                  type="text"
                  value={formData.tradeName}
                  onChange={(e) => handleInputChange("tradeName", e.target.value)}
                  className={`h-10 text-sm border-gray-200 focus:border-teal-500 focus:ring-1 focus:ring-teal-500 ${gstVerified ? 'bg-gray-50' : ''}`}
                  placeholder="Your business brand name"
                  disabled={gstVerified}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="dateOfRegistration" className="text-sm font-medium text-gray-700">Date of Registration</Label>
                  <Input
                    id="dateOfRegistration"
                    type="date"
                    value={formData.dateOfRegistration}
                    onChange={(e) => handleInputChange("dateOfRegistration", e.target.value)}
                    className={`h-10 text-sm border-gray-200 focus:border-teal-500 focus:ring-1 focus:ring-teal-500 ${gstVerified ? 'bg-gray-50' : ''}`}
                    disabled={gstVerified}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="constitutionOfBusiness" className="text-sm font-medium text-gray-700">Constitution of Business</Label>
                  <Input
                    id="constitutionOfBusiness"
                    type="text"
                    value={formData.constitutionOfBusiness}
                    onChange={(e) => handleInputChange("constitutionOfBusiness", e.target.value)}
                    className={`h-10 text-sm border-gray-200 focus:border-teal-500 focus:ring-1 focus:ring-teal-500 ${gstVerified ? 'bg-gray-50' : ''}`}
                    placeholder="e.g., Proprietorship, Partnership, Pvt Ltd"
                    disabled={gstVerified}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="taxpayerType" className="text-sm font-medium text-gray-700">Taxpayer Type</Label>
                <Input
                  id="taxpayerType"
                  type="text"
                  value={formData.taxpayerType}
                  onChange={(e) => handleInputChange("taxpayerType", e.target.value)}
                  className={`h-10 text-sm border-gray-200 focus:border-teal-500 focus:ring-1 focus:ring-teal-500 ${gstVerified ? 'bg-gray-50' : ''}`}
                  placeholder="e.g., Regular, Composition"
                  disabled={gstVerified}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="principalPlaceOfBusiness" className="text-sm font-medium text-gray-700">Principal Place of Business Address</Label>
                <Textarea
                  id="principalPlaceOfBusiness"
                  value={formData.principalPlaceOfBusiness}
                  onChange={(e) => handleInputChange("principalPlaceOfBusiness", e.target.value)}
                  className={`min-h-[60px] text-sm border-gray-200 focus:border-teal-500 focus:ring-1 focus:ring-teal-500 ${gstVerified ? 'bg-gray-50' : ''}`}
                  placeholder="Enter the full address"
                  disabled={gstVerified}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="natureOfCoreBusinessActivity" className="text-sm font-medium text-gray-700">Nature of Core Business Activity</Label>
                <Textarea
                  id="natureOfCoreBusinessActivity"
                  value={formData.natureOfCoreBusinessActivity}
                  onChange={(e) => handleInputChange("natureOfCoreBusinessActivity", e.target.value)}
                  className={`min-h-[60px] text-sm border-gray-200 focus:border-teal-500 focus:ring-1 focus:ring-teal-500 ${gstVerified ? 'bg-gray-50' : ''}`}
                  placeholder="Describe your main business activity"
                  disabled={gstVerified}
                />
              </div>
            </div>

            {/* Marketing Preferences */}
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="emailMarketing"
                  checked={formData.agreedToEmailMarketing}
                  onCheckedChange={(checked) =>
                    handleInputChange("agreedToEmailMarketing", checked as boolean)
                  }
                  className="data-[state=checked]:bg-teal-600 data-[state=checked]:border-teal-600"
                />
                <Label htmlFor="emailMarketing" className="text-sm font-medium text-gray-700">
                  I agree to receive marketing emails.
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="smsMarketing"
                  checked={formData.agreedToSmsMarketing}
                  onCheckedChange={(checked) =>
                    handleInputChange("agreedToSmsMarketing", checked as boolean)
                  }
                  className="data-[state=checked]:bg-teal-600 data-[state=checked]:border-teal-600"
                />
                <Label htmlFor="smsMarketing" className="text-sm font-medium text-gray-700">
                  I agree to receive marketing SMS messages.
                </Label>
              </div>
            </div>

            <Button
              type="submit"
              className="w-full h-10 text-sm bg-teal-600 hover:bg-teal-700 text-white font-medium mt-6"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating Account...
                </>
              ) : (
                "Complete Registration"
              )}
            </Button>

            <p className="text-center text-sm text-gray-600">
              Already have an account?{" "}
              <Link href="/login" className="text-teal-600 hover:underline">
                Sign in →
              </Link>
            </p>
          </form>
        </TabsContent>
      </Tabs>
    </div>
  )
}
