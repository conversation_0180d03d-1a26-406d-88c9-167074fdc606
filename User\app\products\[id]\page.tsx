import type { Metada<PERSON> } from "next"
import { notFound } from "next/navigation"
import { Suspense } from 'react' // Added
import Breadcrumb from "@/components/breadcrumb"
import ProductDetail from "@/components/product-detail"
import ProductSpecifications from "@/components/product-specifications"
import RelatedProducts from "@/components/related-products"
import { getProducts, getProductWithCollections, getCollectionInfoForBreadcrumb } from "@/lib/product-service"
import { Skeleton } from "@/components/ui/skeleton" // Added

interface ProductPageProps {
  params: {
    id: string
  }
}

export async function generateMetadata({ params }: ProductPageProps): Promise<Metadata> {
  try {
    const { id } = await params
    const productId = `gid://shopify/Product/${id}`
    const product = await getProductWithCollections(productId)
    
    if (!product) {
      throw new Error('Product not found')
    }

    return {
      title: `${product.title} | Benzochem Industries`,
      description: product.description,
    }
  } catch (error) {
    return {
      title: "Product Not Found | Benzochem Industries",
      description: "The requested product could not be found.",
    }
  }
}

// Skeleton component for the page
function ProductPageLoadingSkeleton() {
  return (
    <main className="flex min-h-screen flex-col pt-20">
      <div className="container mx-auto px-4 py-8">
        {/* Skeleton for Breadcrumbs */}
        <div className="mb-4 flex space-x-2">
          <Skeleton className="h-4 w-16 rounded" />
          <Skeleton className="h-4 w-24 rounded" />
          <Skeleton className="h-4 w-32 rounded" />
        </div>

        {/* Skeleton for ProductDetail */}
        <div className="mt-8 grid md:grid-cols-2 gap-8">
          <div> {/* Left column: Image Gallery */}
            <Skeleton className="h-[400px] w-full rounded-lg" /> {/* Main Image */}
            <div className="mt-4 flex space-x-2"> {/* Thumbnails */}
              <Skeleton className="h-16 w-16 rounded" />
              <Skeleton className="h-16 w-16 rounded" />
              <Skeleton className="h-16 w-16 rounded" />
            </div>
          </div>
          <div> {/* Right column: Product Info */}
            <Skeleton className="h-8 w-3/4 mb-4 rounded" /> {/* Title */}
            <Skeleton className="h-6 w-1/4 mb-2 rounded" /> {/* Price */}
            {/* Description */}
            <Skeleton className="h-4 w-full mb-1 rounded" />
            <Skeleton className="h-4 w-full mb-1 rounded" />
            <Skeleton className="h-4 w-5/6 mb-4 rounded" />
            <Skeleton className="h-10 w-1/2 mt-6 rounded" /> {/* Add to cart button / Quantity selector */}
          </div>
        </div>

        {/* Skeleton for ProductSpecifications */}
        <div className="mt-16">
          <Skeleton className="h-8 w-1/3 mb-6 rounded" /> {/* Section Title: "Product Specifications" */}
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => ( // Assuming 3 spec rows for skeleton
              <div key={i} className="grid grid-cols-3 gap-4"> {/* Key-value pair */}
                <Skeleton className="h-4 w-1/3 rounded" /> {/* Spec Name */}
                <Skeleton className="h-4 w-2/3 col-span-2 rounded" /> {/* Spec Value */}
              </div>
            ))}
          </div>
        </div>

        {/* Skeleton for RelatedProducts */}
        <div className="mt-24 mb-16">
          <Skeleton className="h-8 w-1/4 mb-8 rounded" /> {/* Section Title */}
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <Skeleton className="h-40 w-full rounded-md mb-4" /> {/* Image */}
                <Skeleton className="h-6 w-3/4 mb-2 rounded" /> {/* Title */}
                <Skeleton className="h-4 w-1/2 rounded" /> {/* Price */}
              </div>
            ))}
          </div>
        </div>
      </div>
    </main>
  );
}

// Component to fetch and render actual content
async function ProductPageContent({ productId }: { productId: string }) {
  try {
    // Get detailed product with collection handles
    const product = await getProductWithCollections(productId)
    
    if (!product) {
      // This will be caught by Next.js and render the not-found page
      notFound()
    }

    // Get all products for related products (using existing function)
    const allProducts = await getProducts()
    
    // Get related products from the same collection
    const relatedProducts = allProducts
      .filter(p => {
        const productCollection = product.collections.edges[0]?.node.title
        const pCollection = p.collections.edges[0]?.node.title
        return p.id !== product.id && productCollection === pCollection
      })
      .slice(0, 4)

    // Get collection info for breadcrumb
    const collectionInfo = getCollectionInfoForBreadcrumb(product)
    
    // console.log('🏷️ Product:', product.title)
    // console.log('📁 Collection Info:', collectionInfo)

    return (
      <main className="flex min-h-screen flex-col pt-20">
        <div className="container mx-auto px-4 py-8">
          <Breadcrumb
            items={[
              { label: "Home", href: "/" },
              {
                label: collectionInfo.title + " Products", 
                href: collectionInfo.href,
              },
              { 
                label: product.title, 
                href: `/products/${product.id.split('/').pop()}` 
              },
            ]}
          />

          <div className="mt-8">
            <ProductDetail product={product} />
          </div>

          <div className="mt-16">
            <ProductSpecifications product={product} />
          </div>

          {relatedProducts.length > 0 && (
            <div className="mt-24 mb-16">
              <h2 className="text-3xl font-medium mb-8">Related Products</h2>
              <RelatedProducts products={relatedProducts} />
            </div>
          )}
        </div>
      </main>
    )
  } catch (error) {
    console.error("Error in ProductPageContent:", error)
    notFound()
  }
}

export default async function ProductPage({ params }: ProductPageProps) {
  const { id } = await params
  const productId = `gid://shopify/Product/${id}`

  return (
    <Suspense fallback={<ProductPageLoadingSkeleton />}>
      <ProductPageContent productId={productId} />
    </Suspense>
  )
}