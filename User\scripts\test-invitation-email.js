// Test script to verify customer invitation email functionality
// Run this script to test if the invitation emails are being sent correctly

const SHOPIFY_DOMAIN = process.env.NEXT_PUBLIC_SHOPIFY_STORE_DOMAIN || 'benzochem.myshopify.com';
const BASE_URL = process.env.NODE_ENV === 'production' 
  ? `https://${SHOPIFY_DOMAIN}` 
  : 'http://localhost:3000';

// Test data
const testCustomer = {
  email: '<EMAIL>',
  firstName: 'Test',
  lastName: 'Customer',
  password: 'TestPassword123!',
  phone: '+**********'
};

async function testCustomerInvitation() {
  console.log('🧪 Testing Customer Invitation Email Functionality...\n');
  
  try {
    // Test 1: Create new customer with invitation
    console.log('📧 Test 1: Creating new customer with invitation email...');
    const createResponse = await fetch(`${BASE_URL}/api/shopify/send-customer-invite`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testCustomer)
    });

    const createResult = await createResponse.json();
    console.log('Create Response Status:', createResponse.status);
    console.log('Create Response:', JSON.stringify(createResult, null, 2));

    if (createResult.success) {
      console.log('✅ Customer created successfully with invitation email');
      console.log(`📧 Invitation email should have been sent to: ${testCustomer.email}`);
      
      // Test 2: Try to resend invitation for existing customer
      console.log('\n📧 Test 2: Resending invitation for existing customer...');
      const resendResponse = await fetch(`${BASE_URL}/api/shopify/resend-invitation`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: testCustomer.email
        })
      });

      const resendResult = await resendResponse.json();
      console.log('Resend Response Status:', resendResponse.status);
      console.log('Resend Response:', JSON.stringify(resendResult, null, 2));

      if (resendResult.success) {
        console.log('✅ Invitation email resent successfully');
      } else {
        console.log('⚠️ Resend failed (this might be expected if customer is already activated)');
      }

    } else if (createResult.error === 'CUSTOMER_EXISTS') {
      console.log('⚠️ Customer already exists, testing resend functionality...');
      
      // Test resend for existing customer
      const resendResponse = await fetch(`${BASE_URL}/api/shopify/resend-invitation`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: testCustomer.email
        })
      });

      const resendResult = await resendResponse.json();
      console.log('Resend Response Status:', resendResponse.status);
      console.log('Resend Response:', JSON.stringify(resendResult, null, 2));

      if (resendResult.success) {
        console.log('✅ Invitation email resent successfully for existing customer');
      } else {
        console.log('❌ Failed to resend invitation email');
      }
    } else {
      console.log('❌ Failed to create customer:', createResult.error);
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

async function testRegistrationFlow() {
  console.log('\n🧪 Testing Registration Flow with Auth Context...\n');
  
  try {
    // Test the registration endpoint that the auth context uses
    const registrationResponse = await fetch(`${BASE_URL}/api/shopify/send-customer-invite`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        firstName: 'Registration',
        lastName: 'Test',
        password: 'TestPassword123!',
        phone: '+**********'
      })
    });

    const registrationResult = await registrationResponse.json();
    console.log('Registration Response Status:', registrationResponse.status);
    console.log('Registration Response:', JSON.stringify(registrationResult, null, 2));

    if (registrationResult.success) {
      console.log('✅ Registration flow working correctly');
      console.log('📧 Customer Account Invite email should have been sent');
    } else {
      console.log('❌ Registration flow failed:', registrationResult.error);
    }

  } catch (error) {
    console.error('❌ Registration flow test failed:', error);
  }
}

async function checkShopifyConfiguration() {
  console.log('\n🔧 Checking Shopify Configuration...\n');
  
  // Check environment variables
  const requiredVars = [
    'NEXT_PUBLIC_SHOPIFY_STORE_DOMAIN',
    'NEXT_PUBLIC_SHOPIFY_STOREFRONT_ACCESS_TOKEN',
    'SHOPIFY_ADMIN_API_ACCESS_TOKEN',
    'SHOPIFY_API_VERSION'
  ];

  let allConfigured = true;
  
  requiredVars.forEach(varName => {
    const value = process.env[varName];
    if (value) {
      console.log(`✅ ${varName}: Configured`);
    } else {
      console.log(`❌ ${varName}: Missing`);
      allConfigured = false;
    }
  });

  if (allConfigured) {
    console.log('\n✅ All required Shopify environment variables are configured');
  } else {
    console.log('\n❌ Some required environment variables are missing');
    console.log('Please check your .env.local file');
  }

  return allConfigured;
}

// Main test function
async function runTests() {
  console.log('🚀 Starting Customer Invitation Email Tests...\n');
  
  // Check configuration first
  const configOk = await checkShopifyConfiguration();
  
  if (!configOk) {
    console.log('\n❌ Configuration issues detected. Please fix environment variables first.');
    return;
  }

  // Run tests
  await testCustomerInvitation();
  await testRegistrationFlow();
  
  console.log('\n📋 Test Summary:');
  console.log('================');
  console.log('1. Check your email inbox (including spam folder) for invitation emails');
  console.log('2. The emails should have subject: "Customer account invite"');
  console.log('3. The emails should contain an activation link');
  console.log('4. If you don\'t receive emails, check Shopify Admin > Settings > Notifications');
  console.log('5. Ensure the "Customer account invite" email template is enabled and configured');
  
  console.log('\n✅ Tests completed!');
}

// Run the tests
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  testCustomerInvitation,
  testRegistrationFlow,
  checkShopifyConfiguration
};
