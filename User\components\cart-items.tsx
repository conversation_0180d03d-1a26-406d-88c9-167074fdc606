"use client"

import Image from "next/image"
import Link from "next/link"
import { motion } from "framer-motion"
import { Trash2, Plus, Minus, ShoppingCart } from "lucide-react" // Added ShoppingCart for empty state
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { useCart } from "@/hooks/use-cart" // Added
import { Skeleton } from "@/components/ui/skeleton" // Added

// Skeleton for a single cart item row
function CartItemRowSkeleton() {
  return (
    <li className="p-6 flex items-center">
      <Skeleton className="w-20 h-20 rounded-md flex-shrink-0" /> {/* Image */}
      <div className="ml-4 flex-grow">
        <Skeleton className="h-5 w-3/4 mb-1 rounded" /> {/* Name */}
        <Skeleton className="h-4 w-1/2 mb-1 rounded" /> {/* Category */}
        <Skeleton className="h-4 w-1/3 mb-2 rounded" /> {/* Package Size */}
        <Skeleton className="h-5 w-1/4 rounded" /> {/* Price */}
      </div>
      <div className="flex items-center ml-4">
        <div className="flex items-center border rounded-md mr-4">
          <Skeleton className="h-8 w-8 rounded-none" /> {/* Minus Button */}
          <Skeleton className="h-8 w-10" /> {/* Quantity */}
          <Skeleton className="h-8 w-8 rounded-none" /> {/* Plus Button */}
        </div>
        <Skeleton className="h-8 w-8 rounded" /> {/* Remove Button */}
      </div>
    </li>
  )
}

// Skeleton for the entire CartItems component
function CartItemsSkeleton() {
  return (
    <div className="bg-white border rounded-lg overflow-hidden">
      <div className="p-6 border-b">
        <Skeleton className="h-6 w-1/2 rounded" /> {/* "Cart Items (X)" Header */}
      </div>
      <ul className="divide-y">
        <CartItemRowSkeleton />
        <CartItemRowSkeleton />
        <CartItemRowSkeleton /> 
      </ul>
      <div className="p-6 bg-neutral-50 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Skeleton className="h-10 w-48 rounded-md" /> {/* Coupon Input */}
          <Skeleton className="h-10 w-20 rounded-md" /> {/* Apply Button */}
        </div>
        <Skeleton className="h-10 w-36 rounded-md" /> {/* Continue Shopping Button */}
      </div>
    </div>
  )
}

export default function CartItems() {
  const { items, removeItem, updateQuantity, isLoading, itemCount } = useCart()

  if (isLoading) {
    return <CartItemsSkeleton />
  }

  if (itemCount === 0) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="bg-white border rounded-lg p-8 text-center"
      >
        <div className="w-16 h-16 bg-neutral-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <ShoppingCart className="w-8 h-8 text-neutral-500" />
        </div>
        <h3 className="text-lg font-medium mb-2">Your cart is empty</h3>
        <p className="text-neutral-600 mb-6">Looks like you haven't added any products to your cart yet.</p>
        <Button asChild className="bg-teal-600 hover:bg-teal-700 text-white">
          <Link href="/categories/powder">Browse Products</Link>
        </Button>
      </motion.div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="bg-white border rounded-lg overflow-hidden"
    >
      <div className="p-6 border-b">
        <h2 className="text-xl font-medium">Cart Items ({itemCount})</h2>
      </div>

      <ul className="divide-y">
        {items.map((item) => (
          <motion.li
            key={item.id} // Assuming item.id from useCart is the line item ID
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, height: 0 }} // Needs AnimatePresence to work
            className="p-6"
          >
            <div className="flex items-center">
              <div className="relative w-20 h-20 rounded-md overflow-hidden flex-shrink-0">
                <Image src={item.image || "/placeholder.svg"} alt={item.name} fill className="object-cover" />
              </div>

              <div className="ml-4 flex-grow">
                {/* Link to product page using variantId or a product slug if available */}
                <Link href={`/products/${item.variantId.split('/').pop()}`} className="font-medium hover:text-teal-600 transition-colors">
                  {item.name}
                </Link>
                <p className="text-sm text-neutral-500 mb-1">Category: {item.category}</p>
                {item.packageSize && (
                  <p className="text-sm text-neutral-500 mb-2">Size: {item.packageSize}</p>
                )}
                <p className="font-medium">₹{item.price.toFixed(2)}</p> 
              </div>

              <div className="flex items-center ml-4">
                <div className="flex items-center border rounded-md mr-4">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 rounded-none"
                    onClick={() => updateQuantity(item.id, item.quantity - 1)}
                    disabled={item.quantity <= 1}
                  >
                    <Minus className="h-3 w-3" />
                  </Button>
                  <span className="w-10 text-center font-medium">{item.quantity}</span>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 rounded-none"
                    onClick={() => updateQuantity(item.id, item.quantity + 1)}
                  >
                    <Plus className="h-3 w-3" />
                  </Button>
                </div>

                <Button
                  variant="ghost"
                  size="icon"
                  className="text-neutral-500 hover:text-red-500"
                  onClick={() => removeItem(item.id)}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </motion.li>
        ))}
      </ul>

      <div className="p-6 bg-neutral-50 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Input placeholder="Coupon code" className="w-48 bg-white" />
          <Button variant="outline">Apply</Button>
        </div>
        <Button variant="ghost" asChild>
          <Link href="/">Continue Shopping</Link>
        </Button>
      </div>
    </motion.div>
  )
}
