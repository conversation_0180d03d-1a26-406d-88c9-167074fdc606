import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

// Simple middleware without Clerk protection
export function middleware(request: NextRequest) {
  // Allow all requests to pass through
  // Authentication will be handled client-side with Shopify
  return NextResponse.next()
}

export const config = {
  matcher: ['/((?!.+\.[\w]+$|_next).*)', '/', '/(api|trpc)(.*)'],
};
