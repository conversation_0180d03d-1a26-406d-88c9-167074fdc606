"use client"

import { useRef } from "react"
import { motion, useScroll, useTransform } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"
import { ChevronRight } from "lucide-react"
import MolecularBackground from "@/components/molecular-background"
import { Router } from "next/router"
import { useRouter } from "next/navigation"

export default function Hero() {
  const ref = useRef(null)
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start start", "end start"],
  })

  const opacity = useTransform(scrollYProgress, [0, 1], [1, 0])
  const scale = useTransform(scrollYProgress, [0, 1], [1, 1.1])

  const router = useRouter();

  return (
    <motion.div
      ref={ref}
      className="relative h-screen flex items-center justify-center overflow-hidden"
      style={{ opacity, scale }}
    >
      <div className="absolute inset-0 z-0">
        <MolecularBackground />
      </div>
      <div className="absolute inset-0 bg-gradient-to-b from-black/10 via-transparent to-white z-10"></div>

      <div className="container mx-auto px-4 z-20 text-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="max-w-3xl mx-auto"
        >
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-medium tracking-tight mb-6">
            Premium Chemical Solutions for Modern Industry
          </h1>
          <p className="text-lg md:text-xl text-neutral-700 mb-8 max-w-2xl mx-auto">
            Benzochem Industries delivers high-quality powder and liquid chemical products with precision, purity, and
            performance.
          </p>
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
            <Button onClick={() => router.push("/categories/powder")} size="lg" className="bg-teal-600 hover:bg-teal-700 text-white">
              Explore Products
            </Button>
            <Button onClick={() => router.push("/about")} variant="outline" size="lg" className="group">
              Learn More
              <ChevronRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
            </Button>                                                         
          </div>
        </motion.div>                                                                           
      </div>
    </motion.div>
  )
}
