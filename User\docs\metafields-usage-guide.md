# Shopify Metafields Usage Guide

## Overview

The `actions/shopifyActions.ts` file now includes comprehensive metafield management functions for storing and retrieving customer data in Shopify. This guide shows how to use these functions effectively.

## Available Functions

### Core Metafield Functions

1. **`setCustomerMetafields(customerId, metafields)`** - Create or update metafields
2. **`getCustomerMetafields(customerId, namespace?)`** - Retrieve metafields
3. **`getCustomerMetafieldValue(customerId, namespace, key)`** - Get specific value
4. **`deleteCustomerMetafields(metafieldIds)`** - Delete metafields

### Utility Functions

1. **`updateCustomerGSTInfo(customerId, gstData)`** - Update GST information
2. **`updateCustomerBusinessPreferences(customerId, preferences)`** - Update business data
3. **`getCustomerGSTInfo(customerId)`** - Retrieve GST information

## Usage Examples

### 1. Basic Metafield Operations

```typescript
import { 
  setCustomerMetafields, 
  getCustomerMetafields, 
  getCustomerMetafieldValue 
} from '@/actions/shopifyActions';

// Create/Update metafields
const result = await setCustomerMetafields('gid://shopify/Customer/123', [
  {
    namespace: "custom",
    key: "business_type",
    value: "Manufacturing",
    type: "single_line_text_field"
  },
  {
    namespace: "custom",
    key: "annual_revenue",
    value: "5000000",
    type: "number_decimal"
  }
]);

// Get all metafields for a customer
const metafields = await getCustomerMetafields('gid://shopify/Customer/123');

// Get specific metafield value
const businessType = await getCustomerMetafieldValue(
  'gid://shopify/Customer/123',
  'custom',
  'business_type'
);
```

### 2. GST Information Management

```typescript
import { updateCustomerGSTInfo, getCustomerGSTInfo } from '@/actions/shopifyActions';

// Update GST information
const gstResult = await updateCustomerGSTInfo('gid://shopify/Customer/123', {
  gstNumber: "27AABCU9603R1ZM",
  legalNameOfBusiness: "Benzochem Industries Pvt Ltd",
  tradeName: "Benzochem",
  dateOfRegistration: "2020-01-15",
  constitutionOfBusiness: "Private Limited Company",
  taxpayerType: "Regular",
  gstStatus: "Active",
  principalPlaceOfBusinessAddress: "123 Industrial Area, Mumbai, Maharashtra 400001",
  principalPlaceOfBusinessEmail: "<EMAIL>",
  principalPlaceOfBusinessMobile: "+91-9876543210",
  natureOfCoreBusinessActivity: "Chemical Manufacturing and Trading"
});

// Retrieve GST information
const gstInfo = await getCustomerGSTInfo('gid://shopify/Customer/123');
if (gstInfo.success) {
  console.log('GST Number:', gstInfo.gstInfo?.gstNumber);
  console.log('Legal Name:', gstInfo.gstInfo?.legalNameOfBusiness);
}
```

### 3. Business Preferences

```typescript
import { updateCustomerBusinessPreferences } from '@/actions/shopifyActions';

// Update business preferences
const preferencesResult = await updateCustomerBusinessPreferences('gid://shopify/Customer/123', {
  preferredPaymentMethod: "Net 30",
  creditLimit: 100000,
  businessCategory: "Chemical Manufacturing",
  annualTurnover: "10-50 Crores",
  numberOfEmployees: "50-100",
  businessType: "B2B",
  industryType: "Chemicals",
  notes: "Preferred supplier for specialty chemicals. Bulk order discounts applicable."
});
```

## Metafield Types and Namespaces

### Common Metafield Types

- `single_line_text_field` - Short text (up to 255 characters)
- `multi_line_text_field` - Long text (up to 65,535 characters)
- `rich_text_field` - Formatted text with JSON structure
- `number_integer` - Whole numbers
- `number_decimal` - Decimal numbers
- `date` - Date values (YYYY-MM-DD format)
- `date_time` - Date and time values
- `boolean` - True/false values
- `url` - URL values
- `json` - JSON data

### Recommended Namespaces

- `custom` - GST and regulatory information
- `business` - Business preferences and settings
- `preferences` - User preferences and settings
- `verification` - Verification status and data
- `analytics` - Tracking and analytics data

## Integration with Registration Form

### In Registration Component

```typescript
// After successful customer creation
const customerId = result.customer.id;

// Store GST information
if (formData.gstNumber) {
  await updateCustomerGSTInfo(customerId, {
    gstNumber: formData.gstNumber,
    legalNameOfBusiness: formData.legalNameOfBusiness,
    tradeName: formData.tradeName,
    dateOfRegistration: formData.dateOfRegistration,
    constitutionOfBusiness: formData.constitutionOfBusiness,
    taxpayerType: formData.taxpayerType,
    principalPlaceOfBusinessAddress: formData.principalPlaceOfBusiness,
    natureOfCoreBusinessActivity: formData.natureOfCoreBusinessActivity
  });
}

// Store additional business data
await updateCustomerBusinessPreferences(customerId, {
  businessCategory: "Chemical Trading",
  businessType: "B2B",
  industryType: "Chemicals"
});
```

## Error Handling

```typescript
const result = await setCustomerMetafields(customerId, metafields);

if (!result.success) {
  console.error('Failed to update metafields:', result.error);
  // Handle error appropriately
  return { success: false, message: 'Failed to save customer information' };
}

console.log('Metafields updated successfully:', result.metafields);
```

## Best Practices

### 1. Namespace Organization
- Use consistent namespaces for related data
- Keep namespace names short and descriptive
- Group related metafields under the same namespace

### 2. Data Types
- Choose appropriate metafield types for your data
- Use `rich_text_field` for formatted content
- Use `number_decimal` for monetary values

### 3. Error Handling
- Always check the `success` property in responses
- Log errors for debugging
- Provide user-friendly error messages

### 4. Performance
- Batch metafield updates when possible
- Use specific namespace queries to reduce data transfer
- Cache frequently accessed metafield values

## Common Use Cases

### Customer Verification Status
```typescript
await setCustomerMetafields(customerId, [
  {
    namespace: "verification",
    key: "email_verified",
    value: "true",
    type: "boolean"
  },
  {
    namespace: "verification",
    key: "phone_verified",
    value: "true",
    type: "boolean"
  },
  {
    namespace: "verification",
    key: "gst_verified",
    value: "true",
    type: "boolean"
  }
]);
```

### Customer Preferences
```typescript
await setCustomerMetafields(customerId, [
  {
    namespace: "preferences",
    key: "communication_language",
    value: "English",
    type: "single_line_text_field"
  },
  {
    namespace: "preferences",
    key: "newsletter_subscription",
    value: "true",
    type: "boolean"
  }
]);
```

### Analytics and Tracking
```typescript
await setCustomerMetafields(customerId, [
  {
    namespace: "analytics",
    key: "registration_source",
    value: "website_form",
    type: "single_line_text_field"
  },
  {
    namespace: "analytics",
    key: "registration_date",
    value: new Date().toISOString(),
    type: "date_time"
  }
]);
```

## Troubleshooting

### Common Issues

1. **Invalid metafield type**: Ensure the type matches the data format
2. **Namespace restrictions**: Some namespaces are reserved by Shopify
3. **Value size limits**: Check character limits for different field types
4. **Permission errors**: Ensure proper API permissions for metafield operations

### Debugging Tips

1. Check console logs for detailed error messages
2. Verify customer ID format (should include `gid://shopify/Customer/`)
3. Test with simple metafields first
4. Use Shopify Admin to verify metafield creation
