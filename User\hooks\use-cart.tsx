"use client"

import type React from "react"
import { toast } from "sonner"
import { createContext, useContext, useState, useEffect } from "react"
import { getOrCreateCart, addToCart, updateCartItem, removeFromCart } from "@/lib/cart-service"
import { useAuth } from "@/contexts/shopify-auth-context"

interface CartItem {
  id: string // This is the cart line item ID from Shopify
  name: string
  price: number
  quantity: number
  image: string
  category: string
  variantId: string // This is the product variant ID from Shopify
  packageSize?: string // Added package size field
}

interface CartContextType {
  items: CartItem[]
  addItem: (item: Omit<CartItem, "id" | "category" | "image" | "name" | "price" | "packageSize"> & Partial<Pick<CartItem, "category" | "image" | "name" | "price" | "packageSize">>) => Promise<void>
  removeItem: (lineId: string) => Promise<void>
  updateQuantity: (lineId: string, quantity: number) => Promise<void>
  clearCart: () => Promise<void>
  itemCount: number
  subtotal: number
  isLoading: boolean
  cartId: string | null
  checkoutUrl: string | null
}

const CartContext = createContext<CartContextType | null>(null)

export function CartProvider({ children }: { children: React.ReactNode }) {
  const [items, setItems] = useState<CartItem[]>([])
  const [itemCount, setItemCount] = useState(0)
  const [subtotal, setSubtotal] = useState(0)
  const [isLoading, setIsLoading] = useState(true)
  const [cartId, setCartId] = useState<string | null>(null)
  const [checkoutUrl, setCheckoutUrl] = useState<string | null>(null)
  const { user } = useAuth() // Get authentication state

  const transformShopifyCart = (shopifyCart: any): CartItem[] => {
    if (!shopifyCart || !shopifyCart.lines || !shopifyCart.lines.edges) {
      return []
    }
    return shopifyCart.lines.edges.map((edge: any) => {
      const { node } = edge
      const { merchandise } = node
      const product = merchandise.product
      const image =
        product.images?.edges?.length > 0 ? product.images.edges[0].node.url : "/images/product-placeholder.png"

      // Get package size from attributes if available
      const packageSizeAttribute = node.attributes?.find((attr: any) => attr.key === "packageSize")
      const packageSize = packageSizeAttribute ? packageSizeAttribute.value : undefined

      return {
        id: node.id, // Cart line item ID
        variantId: merchandise.id, // Product variant ID
        name: product.title,
        price: Number.parseFloat(merchandise.priceV2.amount), // Use priceV2 for consistency
        quantity: node.quantity,
        image: image,
        category: product.collections?.edges?.[0]?.node?.title?.toLowerCase() || "unknown",
        packageSize: packageSize,
      }
    })
  }

  // Initialize cart when user is authenticated
  useEffect(() => {
    async function initCart() {
      try {
        setIsLoading(true)

        // Only initialize cart if user is authenticated
        if (user) {
          const cart = await getOrCreateCart()

          if (cart) {
            setCartId(cart.id)
            setCheckoutUrl(cart.checkoutUrl)
            const cartItems = transformShopifyCart(cart)
            setItems(cartItems)
          }
        } else {
          // Clear cart data if user is not authenticated
          setCartId(null)
          setCheckoutUrl(null)
          setItems([])
        }
      } catch (error) {
        console.error("Error initializing cart:", error)
        // Clear cart data on error
        setCartId(null)
        setCheckoutUrl(null)
        setItems([])
      } finally {
        setIsLoading(false)
      }
    }

    initCart()
  }, [user]) // Re-run when user authentication state changes

  // No longer needed since we only create authenticated carts
  // The cart initialization effect above handles authentication state changes

  useEffect(() => {
    // Calculate item count and subtotal whenever items change
    const count = items.reduce((total, item) => total + item.quantity, 0)
    const total = items.reduce((total, item) => total + item.price * item.quantity, 0)

    setItemCount(count)
    setSubtotal(total)
  }, [items])

  const addItem = async (item: Omit<CartItem, "id" | "category" | "image" | "name" | "price" | "packageSize"> & Partial<Pick<CartItem, "category" | "image" | "name" | "price" | "packageSize">>) => {
    // Check if user is authenticated
    if (!user) {
      toast.error("Please log in to add items to cart")
      return
    }

    setIsLoading(true)
    try {
      let currentCartId = cartId
      if (!currentCartId) {
        const newCart = await getOrCreateCart()
        if (newCart) {
          currentCartId = newCart.id
          setCartId(newCart.id)
          setCheckoutUrl(newCart.checkoutUrl)
          // If newCart contains lines (e.g. from a previous session recovered by Shopify), update items
          if (newCart.lines?.edges?.length > 0) {
            const cartItems = transformShopifyCart(newCart)
            setItems(cartItems)
          }
        } else {
          throw new Error("Failed to create or retrieve cart. Please ensure you are logged in.")
        }
      }

      if (!currentCartId) {
        throw new Error("Cart ID is still null after attempting to create/retrieve cart.")
      }

      // Prepare attributes array if packageSize is provided
      const attributes = []
      if (item.packageSize) {
        attributes.push({
          key: "packageSize",
          value: item.packageSize
        })
      }

      const updatedCart = await addToCart(currentCartId, [
        {
          merchandiseId: item.variantId,
          quantity: item.quantity,
          attributes: attributes
        },
      ])

      if (updatedCart) {
        setCheckoutUrl(updatedCart.checkoutUrl)
        const cartItems = transformShopifyCart(updatedCart)
        setItems(cartItems)

        // Show success notification
        toast.success(
          <div className="flex flex-col">
            <span className="font-medium">{item.name || "Product"}</span>
            <span className="text-sm text-gray-600">added to your cart</span>
          </div>,
          {
            duration: 3000,
            icon: <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
              <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
            </svg>
          }
        )
      }
    } catch (error) {
      toast.error("Failed to add item to cart. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  const removeItem = async (lineId: string) => {
    setIsLoading(true)
    if (!cartId) {
      setIsLoading(false)
      return
    }
    try {
      const updatedCart = await removeFromCart(cartId, [lineId])
      if (updatedCart) {
        setCheckoutUrl(updatedCart.checkoutUrl)
        const cartItems = transformShopifyCart(updatedCart)
        setItems(cartItems)
      } else {
        // This case might occur if the API returns null or an unexpected structure
        // If all items were removed, the cart might still exist but be empty.
        // Re-fetch cart to be sure or assume empty if lines are gone.
        const freshCart = await getOrCreateCart(cartId) // Or just pass cartId
        if (freshCart) {
          setCheckoutUrl(freshCart.checkoutUrl)
          const cartItems = transformShopifyCart(freshCart)
          setItems(cartItems)
        } else {
          setItems([]) // Fallback
          setCheckoutUrl(null) // No checkout if cart is gone
        }
      }
    } catch (error) {
      // Add user-facing error handling
    } finally {
      setIsLoading(false)
    }
  }

  const updateQuantity = async (lineId: string, quantity: number) => {
    if (quantity < 1) {
      await removeItem(lineId) // Shopify handles 0 as remove for some mutations, but explicit is better.
      return
    }
    setIsLoading(true)
    if (!cartId) {
      setIsLoading(false)
      return
    }
    try {
      const updatedCart = await updateCartItem(cartId, lineId, quantity)
      if (updatedCart) {
        setCheckoutUrl(updatedCart.checkoutUrl)
        const cartItems = transformShopifyCart(updatedCart)
        setItems(cartItems)
      }
    } catch (error) {
      // Add user-facing error handling
    } finally {
      setIsLoading(false)
    }
  }

  const clearCart = async () => {
    setIsLoading(true)
    if (!cartId || items.length === 0) {
      setIsLoading(false)
      setItems([]) // Ensure items are cleared if no cartId or items were present
      return
    }
    try {
      const lineIds = items.map((item) => item.id)
      if (lineIds.length > 0) {
        // Shopify's cartLinesRemove mutation is used. It returns the updated cart.
        const updatedCart = await removeFromCart(cartId, lineIds)
        if (updatedCart) {
          setCheckoutUrl(updatedCart.checkoutUrl)
          const cartItems = transformShopifyCart(updatedCart)
          setItems(cartItems)
        } else {
          // Fallback if API response is not as expected after removing all lines
          setItems([])
          // Optionally, re-fetch the cart to get its current state (likely empty with a new checkoutUrl)
          const freshCart = await getOrCreateCart(cartId)
          if (freshCart) {
            setCartId(freshCart.id)
            setCheckoutUrl(freshCart.checkoutUrl)
          } else {
            setCheckoutUrl(null) // If cart truly gone
          }
        }
      } else {
        setItems([]) // No line IDs means items array was already empty or inconsistent
      }
    } catch (error) {
      // Add user-facing error handling
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <CartContext.Provider
      value={{
        items,
        addItem,
        removeItem,
        updateQuantity,
        clearCart,
        itemCount,
        subtotal,
        isLoading,
        cartId,
        checkoutUrl,
      }}
    >
      {children}
    </CartContext.Provider>
  )
}

export function useCart() {
  const context = useContext(CartContext)
  if (!context) {
    throw new Error("useCart must be used within a CartProvider")
  }
  return context
}
