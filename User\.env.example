# Benzochem Industries - Environment Variables Template
# Copy this file to .env.local and fill in your actual API keys
# NEVER commit .env.local to version control!

# =============================================================================
# SHOPIFY CONFIGURATION
# =============================================================================
# Get these from your Shopify Partner Dashboard or Store Admin
# Guide: https://shopify.dev/docs/api/usage/authentication

# Public Shopify Configuration (safe for client-side)
NEXT_PUBLIC_SHOPIFY_STORE_DOMAIN=your-store.myshopify.com
NEXT_PUBLIC_SHOPIFY_STOREFRONT_ACCESS_TOKEN=your_storefront_access_token_here

# Private Shopify Configuration (server-side only)
SHOPIFY_STORE_DOMAIN=your-store.myshopify.com
SHOPIFY_ADMIN_API_ACCESS_TOKEN=shpat_your_admin_access_token_here
SHOPIFY_API_VERSION=2025-04
SHOPIFY_WEBHOOK_SECRET=your_webhook_secret_here

# Authentication URLs (Shopify-based)
NEXT_PUBLIC_SIGN_IN_URL=/login
NEXT_PUBLIC_SIGN_UP_URL=/register
NEXT_PUBLIC_AFTER_SIGN_IN_URL=/account
NEXT_PUBLIC_AFTER_SIGN_UP_URL=/account/verify-gst

# =============================================================================
# RAPIDAPI GST VERIFICATION SERVICE
# =============================================================================
# Get your RapidAPI key from: https://rapidapi.com/
# Subscribe to: GST Return Status API
# Guide: https://rapidapi.com/hub

RAPIDAPI_KEY=your_rapidapi_key_here
RAPIDAPI_GST_HOST=gst-return-status.p.rapidapi.com
RAPIDAPI_GST_BASE_URL=https://gst-return-status.p.rapidapi.com/free

# =============================================================================
# GOOGLE PLACES API CONFIGURATION
# =============================================================================
# Get your API key from: https://console.cloud.google.com/
# Enable: Places API, Places API (New)
# Guide: See GOOGLE_PLACES_SETUP.md for detailed instructions

GOOGLE_PLACES_API_KEY=AIzaSy_your_google_places_api_key_here

# =============================================================================
# SECURITY NOTES
# =============================================================================
# 1. Replace ALL placeholder values with your actual API keys
# 2. NEVER commit .env.local to version control
# 3. Use different API keys for development and production
# 4. Set up API key restrictions in each service's console
# 5. Monitor API usage and set up billing alerts
# 6. Rotate API keys regularly for security
