"use client"

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'

export default function MetafieldsTestPage() {
  const [customerId, setCustomerId] = useState('gid://shopify/Customer/')
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<any>(null)

  // GST form data
  const [gstData, setGstData] = useState({
    gstNumber: '',
    legalNameOfBusiness: '',
    tradeName: '',
    dateOfRegistration: '',
    constitutionOfBusiness: '',
    taxpayerType: '',
    gstStatus: '',
    principalPlaceOfBusinessAddress: '',
    principalPlaceOfBusinessEmail: '',
    principalPlaceOfBusinessMobile: '',
    natureOfCoreBusinessActivity: ''
  })

  // Business preferences data
  const [preferences, setPreferences] = useState({
    preferredPaymentMethod: '',
    creditLimit: '',
    businessCategory: '',
    annualTurnover: '',
    numberOfEmployees: '',
    businessType: '',
    industryType: '',
    notes: ''
  })

  const handleApiCall = async (action: string, additionalData: any = {}) => {
    if (!customerId.trim()) {
      setResult({
        success: false,
        message: 'Please enter a customer ID'
      })
      return
    }

    setLoading(true)
    setResult(null)

    try {
      const response = await fetch('/api/test-metafields', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action,
          customerId: customerId.trim(),
          ...additionalData
        })
      })

      const data = await response.json()
      setResult(data)
    } catch (error) {
      setResult({
        success: false,
        message: 'Network error occurred'
      })
    } finally {
      setLoading(false)
    }
  }

  const handleCreateBasicMetafields = () => {
    handleApiCall('create', {
      metafields: [
        {
          namespace: "custom",
          key: "business_type",
          value: "Manufacturing",
          type: "single_line_text_field"
        },
        {
          namespace: "custom",
          key: "annual_revenue",
          value: "5000000",
          type: "number_decimal"
        },
        {
          namespace: "preferences",
          key: "newsletter_subscription",
          value: "true",
          type: "boolean"
        }
      ]
    })
  }

  const handleUpdateGST = () => {
    const filteredGstData = Object.fromEntries(
      Object.entries(gstData).filter(([_, value]) => value.trim() !== '')
    )
    
    if (Object.keys(filteredGstData).length === 0) {
      setResult({
        success: false,
        message: 'Please fill in at least one GST field'
      })
      return
    }

    handleApiCall('updateGST', { gstData: filteredGstData })
  }

  const handleUpdatePreferences = () => {
    const filteredPreferences = Object.fromEntries(
      Object.entries(preferences).filter(([_, value]) => value.trim() !== '')
    )
    
    if (Object.keys(filteredPreferences).length === 0) {
      setResult({
        success: false,
        message: 'Please fill in at least one preference field'
      })
      return
    }

    // Convert creditLimit to number if provided
    if (filteredPreferences.creditLimit) {
      filteredPreferences.creditLimit = parseFloat(filteredPreferences.creditLimit)
    }

    handleApiCall('updatePreferences', { preferences: filteredPreferences })
  }

  const fillSampleGSTData = () => {
    setGstData({
      gstNumber: '27AABCU9603R1ZM',
      legalNameOfBusiness: 'Benzochem Industries Pvt Ltd',
      tradeName: 'Benzochem',
      dateOfRegistration: '2020-01-15',
      constitutionOfBusiness: 'Private Limited Company',
      taxpayerType: 'Regular',
      gstStatus: 'Active',
      principalPlaceOfBusinessAddress: '123 Industrial Area, Mumbai, Maharashtra 400001',
      principalPlaceOfBusinessEmail: '<EMAIL>',
      principalPlaceOfBusinessMobile: '+91-9876543210',
      natureOfCoreBusinessActivity: 'Chemical Manufacturing and Trading'
    })
  }

  const fillSamplePreferences = () => {
    setPreferences({
      preferredPaymentMethod: 'Net 30',
      creditLimit: '100000',
      businessCategory: 'Chemical Manufacturing',
      annualTurnover: '10-50 Crores',
      numberOfEmployees: '50-100',
      businessType: 'B2B',
      industryType: 'Chemicals',
      notes: 'Preferred supplier for specialty chemicals. Bulk order discounts applicable.'
    })
  }

  return (
    <div className="container mx-auto py-8 max-w-4xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Shopify Metafields Test</h1>
        <p className="text-muted-foreground">
          Test metafield operations for customer data storage and retrieval.
        </p>
      </div>

      {/* Customer ID Input */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Customer ID</CardTitle>
          <CardDescription>
            Enter the Shopify customer ID (format: gid://shopify/Customer/123456789)
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Input
            value={customerId}
            onChange={(e) => setCustomerId(e.target.value)}
            placeholder="gid://shopify/Customer/123456789"
            className="mb-4"
          />
          <div className="flex gap-2">
            <Button 
              onClick={() => handleApiCall('get')} 
              disabled={loading}
              variant="outline"
            >
              {loading ? 'Loading...' : 'Get All Metafields'}
            </Button>
            <Button 
              onClick={() => handleApiCall('getGST')} 
              disabled={loading}
              variant="outline"
            >
              {loading ? 'Loading...' : 'Get GST Info'}
            </Button>
            <Button 
              onClick={handleCreateBasicMetafields} 
              disabled={loading}
              variant="outline"
            >
              {loading ? 'Creating...' : 'Create Sample Metafields'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Tabs for different operations */}
      <Tabs defaultValue="gst" className="mb-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="gst">GST Information</TabsTrigger>
          <TabsTrigger value="preferences">Business Preferences</TabsTrigger>
        </TabsList>

        <TabsContent value="gst">
          <Card>
            <CardHeader>
              <CardTitle>GST Information</CardTitle>
              <CardDescription>
                Update customer GST and business registration details
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">GST Number</label>
                  <Input
                    value={gstData.gstNumber}
                    onChange={(e) => setGstData({...gstData, gstNumber: e.target.value})}
                    placeholder="27AABCU9603R1ZM"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Legal Name of Business</label>
                  <Input
                    value={gstData.legalNameOfBusiness}
                    onChange={(e) => setGstData({...gstData, legalNameOfBusiness: e.target.value})}
                    placeholder="Company Pvt Ltd"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Trade Name</label>
                  <Input
                    value={gstData.tradeName}
                    onChange={(e) => setGstData({...gstData, tradeName: e.target.value})}
                    placeholder="Trade Name"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Date of Registration</label>
                  <Input
                    type="date"
                    value={gstData.dateOfRegistration}
                    onChange={(e) => setGstData({...gstData, dateOfRegistration: e.target.value})}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Constitution of Business</label>
                  <Input
                    value={gstData.constitutionOfBusiness}
                    onChange={(e) => setGstData({...gstData, constitutionOfBusiness: e.target.value})}
                    placeholder="Private Limited Company"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Taxpayer Type</label>
                  <Input
                    value={gstData.taxpayerType}
                    onChange={(e) => setGstData({...gstData, taxpayerType: e.target.value})}
                    placeholder="Regular"
                  />
                </div>
              </div>
              
              <div>
                <label className="text-sm font-medium">Principal Place of Business Address</label>
                <Textarea
                  value={gstData.principalPlaceOfBusinessAddress}
                  onChange={(e) => setGstData({...gstData, principalPlaceOfBusinessAddress: e.target.value})}
                  placeholder="Complete business address"
                />
              </div>

              <div className="flex gap-2">
                <Button onClick={handleUpdateGST} disabled={loading}>
                  {loading ? 'Updating...' : 'Update GST Info'}
                </Button>
                <Button onClick={fillSampleGSTData} variant="outline">
                  Fill Sample Data
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="preferences">
          <Card>
            <CardHeader>
              <CardTitle>Business Preferences</CardTitle>
              <CardDescription>
                Update customer business preferences and settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">Preferred Payment Method</label>
                  <Input
                    value={preferences.preferredPaymentMethod}
                    onChange={(e) => setPreferences({...preferences, preferredPaymentMethod: e.target.value})}
                    placeholder="Net 30"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Credit Limit</label>
                  <Input
                    type="number"
                    value={preferences.creditLimit}
                    onChange={(e) => setPreferences({...preferences, creditLimit: e.target.value})}
                    placeholder="100000"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Business Category</label>
                  <Input
                    value={preferences.businessCategory}
                    onChange={(e) => setPreferences({...preferences, businessCategory: e.target.value})}
                    placeholder="Manufacturing"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Annual Turnover</label>
                  <Input
                    value={preferences.annualTurnover}
                    onChange={(e) => setPreferences({...preferences, annualTurnover: e.target.value})}
                    placeholder="10-50 Crores"
                  />
                </div>
              </div>
              
              <div>
                <label className="text-sm font-medium">Notes</label>
                <Textarea
                  value={preferences.notes}
                  onChange={(e) => setPreferences({...preferences, notes: e.target.value})}
                  placeholder="Additional notes about the customer"
                />
              </div>

              <div className="flex gap-2">
                <Button onClick={handleUpdatePreferences} disabled={loading}>
                  {loading ? 'Updating...' : 'Update Preferences'}
                </Button>
                <Button onClick={fillSamplePreferences} variant="outline">
                  Fill Sample Data
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Results */}
      {result && (
        <Card>
          <CardHeader>
            <CardTitle>Result</CardTitle>
          </CardHeader>
          <CardContent>
            <Alert className={result.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
              <AlertDescription>
                <div className="space-y-2">
                  <div className="font-medium">
                    {result.success ? '✅ Success' : '❌ Error'}
                  </div>
                  {result.message && <div>{result.message}</div>}
                  {result.error && <div>Error: {result.error}</div>}
                  {result.metafields && (
                    <div>
                      <div className="font-medium">Metafields:</div>
                      <pre className="text-xs bg-gray-100 p-2 rounded mt-1 overflow-auto">
                        {JSON.stringify(result.metafields, null, 2)}
                      </pre>
                    </div>
                  )}
                  {result.gstInfo && (
                    <div>
                      <div className="font-medium">GST Information:</div>
                      <pre className="text-xs bg-gray-100 p-2 rounded mt-1 overflow-auto">
                        {JSON.stringify(result.gstInfo, null, 2)}
                      </pre>
                    </div>
                  )}
                </div>
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
