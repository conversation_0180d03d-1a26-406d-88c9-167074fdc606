import { storefrontFetch } from "./shopify"

// GraphQL mutation for creating a customer
const CUSTOMER_CREATE_MUTATION = `
  mutation customerCreate($input: CustomerCreateInput!) {
    customerCreate(input: $input) {
      customer {
        id
        firstName
        lastName
        email
        phone
        metafields(first: 10) {
          edges {
            node {
              namespace
              key
              value
            }
          }
        }
      }
      customerUserErrors {
        code
        field
        message
      }
    }
  }
`

// Create a customer in Shopify after successful Clerk registration
export async function createShopifyCustomer({
  firstName,
  lastName,
  email,
  phone,
  businessName,
  gstNumber,
}: {
  firstName: string
  lastName: string
  email: string
  phone: string
  businessName: string
  gstNumber: string
}) {
  try {
    // Generate a random password for Shopify (since auth is handled by Clerk)
    const password = Math.random().toString(36).slice(-12)

    const { data } = await storefrontFetch<any>({
      query: CUSTOMER_CREATE_MUTATION,
      variables: {
        input: {
          firstName,
          lastName,
          email,
          phone,
          password,
          acceptsMarketing: false,
          metafields: [
            {
              namespace: "customer",
              key: "business_name",
              value: businessName,
              type: "single_line_text_field"
            },
            {
              namespace: "customer",
              key: "gst_number",
              value: gstNumber,
              type: "single_line_text_field"
            }
          ]
        },
      },
    })

    const { customer, customerUserErrors } = data?.customerCreate || {}

    if (customerUserErrors?.length) {
      throw new Error(customerUserErrors[0].message)
    }

    return customer
  } catch (error) {
    console.error("Error creating Shopify customer:", error)
    throw error
  }
}