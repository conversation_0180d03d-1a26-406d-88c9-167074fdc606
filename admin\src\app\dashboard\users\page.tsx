"use client";

import { useState } from "react";
import { ProtectedRoute } from "@/components/auth/protected-route";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DetailDialog,
  DetailSection,
  DetailField,
} from "@/components/ui/enhanced-dialog";
import {
  Search,
  Filter,
  MoreHorizontal,
  UserCheck,
  UserX,
  Eye,
  Download,
  RefreshCw,
  Building,
  Mail,
  Phone,
  MapPin,
  Globe,
  Calendar
} from "lucide-react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../../../convex/_generated/api";
import { useAuth } from "@/contexts/auth-context";
import { toast } from "sonner";

export default function UsersPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [currentPage, setCurrentPage] = useState(0);
  const [selectedUser, setSelectedUser] = useState<any>(null);
  const [showDetailsDialog, setShowDetailsDialog] = useState(false);
  const pageSize = 20;

  const { admin } = useAuth();

  // Queries
  const users = useQuery(api.users.getUsers, {
    search: searchTerm || undefined,
    status: statusFilter === "all" ? undefined : statusFilter as any,
    limit: pageSize,
    offset: currentPage * pageSize,
  });

  const userStats = useQuery(api.users.getUserStats);

  // Mutations
  const approveUser = useMutation(api.users.approveUser);
  const rejectUser = useMutation(api.users.rejectUser);
  const getOrCreateDemoAdmin = useMutation(api.admins.getOrCreateDemoAdmin);

  const handleApproveUser = async (userId: string) => {
    if (!admin) return;

    try {
      // Get or create the demo admin in Convex
      const adminId = await getOrCreateDemoAdmin({ email: admin.email });

      await approveUser({
        userId: userId as any,
        adminId: adminId,
      });
      toast.success("User approved successfully");
    } catch (error) {
      toast.error("Failed to approve user");
      console.error(error);
    }
  };

  const handleRejectUser = async (userId: string) => {
    if (!admin) return;

    const reason = prompt("Please provide a reason for rejection:");
    if (!reason) return;

    try {
      // Get or create the demo admin in Convex
      const adminId = await getOrCreateDemoAdmin({ email: admin.email });

      await rejectUser({
        userId: userId as any,
        adminId: adminId,
        reason,
      });
      toast.success("User rejected successfully");
    } catch (error) {
      toast.error("Failed to reject user");
      console.error(error);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "approved":
        return <Badge variant="default" className="bg-green-500">Approved</Badge>;
      case "pending":
        return <Badge variant="secondary">Pending</Badge>;
      case "rejected":
        return <Badge variant="destructive">Rejected</Badge>;
      case "suspended":
        return <Badge variant="outline" className="border-orange-500 text-orange-500">Suspended</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const openDetailsDialog = (user: any) => {
    setSelectedUser(user);
    setShowDetailsDialog(true);
  };

  return (
    <ProtectedRoute requiredPermission="users.read">
      <DashboardLayout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">User Management</h1>
              <p className="text-muted-foreground">
                Manage user registrations, approvals, and account status
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
              <Button variant="outline" size="sm">
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </div>
          </div>

          {/* Stats Cards */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total Users</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{userStats?.total || 0}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Pending</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-yellow-600">{userStats?.pending || 0}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Approved</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{userStats?.approved || 0}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Rejected</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">{userStats?.rejected || 0}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">GST Verified</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">{userStats?.gstVerified || 0}</div>
              </CardContent>
            </Card>
          </div>

          {/* Filters */}
          <Card>
            <CardHeader>
              <CardTitle>Users</CardTitle>
              <CardDescription>
                Manage and monitor user accounts
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-4 mb-6">
                <div className="relative flex-1 max-w-sm">
                  <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                  <Input
                    placeholder="Search users..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="approved">Approved</SelectItem>
                    <SelectItem value="rejected">Rejected</SelectItem>
                    <SelectItem value="suspended">Suspended</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Users Table */}
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>User</TableHead>
                      <TableHead>Business</TableHead>
                      <TableHead>GST</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Registered</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {users?.map((user) => (
                      <TableRow key={user._id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">
                              {user.firstName} {user.lastName}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {user.email}
                            </div>
                            {user.phone && (
                              <div className="text-sm text-muted-foreground">
                                {user.phone}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            {user.businessName && (
                              <div className="font-medium">{user.businessName}</div>
                            )}
                            {user.legalNameOfBusiness && (
                              <div className="text-sm text-muted-foreground">
                                {user.legalNameOfBusiness}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            {user.gstNumber && (
                              <div className="font-mono text-sm">{user.gstNumber}</div>
                            )}
                            {user.isGstVerified && (
                              <Badge variant="outline" className="text-green-600 border-green-600">
                                Verified
                              </Badge>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          {getStatusBadge(user.status)}
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            {formatDate(user.createdAt)}
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => openDetailsDialog(user)}>
                                <Eye className="mr-2 h-4 w-4" />
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              {user.status === "pending" && (
                                <>
                                  <DropdownMenuItem
                                    onClick={() => handleApproveUser(user._id)}
                                    className="text-green-600"
                                  >
                                    <UserCheck className="mr-2 h-4 w-4" />
                                    Approve User
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    onClick={() => handleRejectUser(user._id)}
                                    className="text-red-600"
                                  >
                                    <UserX className="mr-2 h-4 w-4" />
                                    Reject User
                                  </DropdownMenuItem>
                                </>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {/* Pagination */}
              <div className="flex items-center justify-between mt-4">
                <div className="text-sm text-muted-foreground">
                  Showing {currentPage * pageSize + 1} to {Math.min((currentPage + 1) * pageSize, users?.length || 0)} of {users?.length || 0} users
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(Math.max(0, currentPage - 1))}
                    disabled={currentPage === 0}
                  >
                    Previous
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(currentPage + 1)}
                    disabled={!users || users.length < pageSize}
                  >
                    Next
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* User Details Dialog */}
          <DetailDialog
            open={showDetailsDialog}
            onOpenChange={setShowDetailsDialog}
            title="User Details"
            subtitle={`Complete information for ${selectedUser?.firstName} ${selectedUser?.lastName}`}
            size="5xl"
            actions={
              <>
                <Button variant="outline" onClick={() => setShowDetailsDialog(false)}>
                  Close
                </Button>
                {selectedUser?.status === "pending" && (
                  <div className="flex gap-2">
                    <Button
                      onClick={() => {
                        handleApproveUser(selectedUser._id);
                        setShowDetailsDialog(false);
                      }}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      <UserCheck className="h-4 w-4 mr-2" />
                      Approve User
                    </Button>
                    <Button
                      variant="destructive"
                      onClick={() => {
                        handleRejectUser(selectedUser._id);
                        setShowDetailsDialog(false);
                      }}
                    >
                      <UserX className="h-4 w-4 mr-2" />
                      Reject User
                    </Button>
                  </div>
                )}
              </>
            }
          >
            <div className="space-y-8">
                {/* Personal Information */}
                <DetailSection title="Personal Information" columns={2}>
                  <DetailField
                    label="Full Name"
                    value={`${selectedUser?.firstName} ${selectedUser?.lastName}`}
                  />
                  <DetailField
                    label="Email"
                    value={selectedUser?.email}
                  />
                  <DetailField
                    label="Phone"
                    value={selectedUser?.phone}
                  />
                  <DetailField
                    label="Date of Birth"
                    value={selectedUser?.dateOfBirth ? formatDate(selectedUser?.dateOfBirth) : undefined}
                  />
                  <DetailField
                    label="Account Status"
                    value={selectedUser && getStatusBadge(selectedUser.status)}
                  />
                  <DetailField
                    label="Email Verified"
                    value={
                      selectedUser?.emailVerified ? (
                        <Badge variant="outline" className="text-green-600 border-green-600">
                          Verified
                        </Badge>
                      ) : (
                        <Badge variant="outline" className="text-orange-600 border-orange-600">
                          Not Verified
                        </Badge>
                      )
                    }
                  />
                </DetailSection>

                {/* Business Information */}
                <DetailSection title="Business Information" columns={2}>
                  <DetailField
                    label="Business Name"
                    value={selectedUser?.businessName}
                  />
                  <DetailField
                    label="Legal Name of Business"
                    value={selectedUser?.legalNameOfBusiness}
                  />
                  <DetailField
                    label="Trade Name"
                    value={selectedUser?.tradeName}
                  />
                  <DetailField
                    label="Business Type"
                    value={selectedUser?.businessType}
                  />
                  <DetailField
                    label="Industry Type"
                    value={selectedUser?.industryType}
                  />
                </DetailSection>



                {/* Address Information */}
                <DetailSection title="Address Information" columns={2}>
                  <DetailField
                    label="Address"
                    value={selectedUser?.address}
                  />
                  <DetailField
                    label="City"
                    value={selectedUser?.city}
                  />
                  <DetailField
                    label="State"
                    value={selectedUser?.state}
                  />
                  <DetailField
                    label="Pincode"
                    value={selectedUser?.pincode}
                  />
                  <DetailField
                    label="Country"
                    value={selectedUser?.country}
                  />
                </DetailSection>

                {/* Account Information */}
                <DetailSection title="Account Information" columns={3}>
                  <DetailField
                    label="Registration Date"
                    value={selectedUser?.createdAt ? formatDate(selectedUser?.createdAt) : 'N/A'}
                  />
                  <DetailField
                    label="Last Updated"
                    value={selectedUser?.updatedAt ? formatDate(selectedUser?.updatedAt) : 'N/A'}
                  />
                  <DetailField
                    label="Email Verified"
                    value={
                      selectedUser?.emailVerified ? (
                        <Badge variant="outline" className="text-green-600 border-green-600">
                          Verified
                        </Badge>
                      ) : (
                        <Badge variant="outline" className="text-orange-600 border-orange-600">
                          Not Verified
                        </Badge>
                      )
                    }
                  />
                </DetailSection>

                {/* Additional Information */}
                {(selectedUser?.website || selectedUser?.description || selectedUser?.socialMediaLinks) && (
                  <DetailSection title="Additional Information" columns={1}>
                    <DetailField
                      label="Website"
                      value={selectedUser?.website ? (
                        <a
                          href={selectedUser?.website}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:underline transition-colors"
                        >
                          {selectedUser?.website}
                        </a>
                      ) : undefined}
                    />
                    <DetailField
                      label="Description"
                      value={selectedUser?.description}
                    />
                    <DetailField
                      label="Social Media"
                      value={selectedUser?.socialMediaLinks && Object.keys(selectedUser?.socialMediaLinks).length > 0 ? (
                        <div className="flex gap-3 flex-wrap">
                          {Object.entries(selectedUser?.socialMediaLinks).map(([platform, url]) => (
                            <a
                              key={platform}
                              href={url as string}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-blue-600 hover:underline text-sm transition-colors capitalize"
                            >
                              {platform}
                            </a>
                          ))}
                        </div>
                      ) : undefined}
                    />
                  </DetailSection>
                )}
            </div>
          </DetailDialog>
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  );
}
