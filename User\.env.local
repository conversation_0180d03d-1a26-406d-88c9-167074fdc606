# Shopify Configuration
NEXT_PUBLIC_SHOPIFY_STORE_DOMAIN=benzochem.myshopify.com
NEXT_PUBLIC_SHOPIFY_STOREFRONT_ACCESS_TOKEN=95029391ce9ba8bad8a604e7faf7eb87
SHOPIFY_STORE_DOMAIN=benzochem.myshopify.com
SHOPIFY_ADMIN_API_ACCESS_TOKEN=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
SHOPIFY_API_VERSION=2025-04
SHOPIFY_WEBHOOK_SECRET=4db46df9e2ec1150d570f2963416714f93aabf91185556b0686c8b507eee5573

# Authentication URLs (Shopify-based)
NEXT_PUBLIC_SIGN_IN_URL=/login
NEXT_PUBLIC_SIGN_UP_URL=/register
NEXT_PUBLIC_AFTER_SIGN_IN_URL=/account
NEXT_PUBLIC_AFTER_SIGN_UP_URL=/account/verify-gst

# RapidAPI GST Verification API Credentials
RAPIDAPI_KEY=************************************************** # Replace with your actual RapidAPI Key
RAPIDAPI_GST_HOST=gst-return-status.p.rapidapi.com # As per the screenshot
RAPIDAPI_GST_BASE_URL=https://gst-return-status.p.rapidapi.com/free # As per the screenshot, endpoint is /gstin/{gstin_number}

# Google Places API Configuration
# IMPORTANT: Replace with your actual Google Places API Key from Google Cloud Console
# See GOOGLE_PLACES_SETUP.md for detailed setup instructions
GOOGLE_PLACES_API_KEY=AIzaSyAVipoBOjQ5fefw-V92HOhBBn7Zn9quoYk

# Other environment variables (Keep existing ones)