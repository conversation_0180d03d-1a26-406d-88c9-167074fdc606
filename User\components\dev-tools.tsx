"use client"

import { useEffect } from 'react'

export default function DevTools() {
  useEffect(() => {
    // Only load dev tools in development mode
    if (process.env.NODE_ENV === 'development') {
      // Create browser test helpers for customer account functionality
      if (typeof window !== 'undefined') {
        (window as any).testPasswordReset = async (email: string) => {
          console.log(`Testing password reset email for: ${email}`)

          const response = await fetch('/api/shopify/resend-invitation', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ email })
          })

          const result = await response.json()

          if (result.success) {
            console.log('✅ Password reset email sent successfully!')
            console.log('Message:', result.message)
          } else {
            console.log('❌ Failed to send password reset email')
            console.log('Error:', result.error)
            console.log('Message:', result.message)
          }

          return result
        }

        ;(window as any).createTestCustomer = async () => {
          const testData = {
            email: `test-${Date.now()}@example.com`,
            firstName: 'Test',
            lastName: 'Customer',
            password: 'TestPassword123!',
            phone: '+1234567890'
          }

          console.log('Creating test customer:', testData.email)

          const response = await fetch('/api/shopify/send-customer-invite', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(testData)
          })

          const result = await response.json()

          if (result.success) {
            console.log('✅ Test customer created successfully!')
            console.log('Email:', testData.email)
            console.log('Account is now active! Check your email for the welcome message and you can now log in.')
          } else {
            console.log('❌ Failed to create test customer')
            console.log('Error:', result.error)
            console.log('Message:', result.message)
          }

          return result
        }

        console.log('🧪 Customer test helpers loaded!')
        console.log('Available functions:')
        console.log('- window.testPasswordReset(email) - Test sending password reset email to existing customer')
        console.log('- window.createTestCustomer() - Create a new test customer with welcome email')
      }
    }
  }, [])

  // This component doesn't render anything
  return null
}
