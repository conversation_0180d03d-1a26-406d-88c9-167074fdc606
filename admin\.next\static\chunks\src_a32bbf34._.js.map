{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/providers/convex-provider.tsx"], "sourcesContent": ["\"use client\";\n\nimport { ReactNode } from \"react\";\nimport { ConvexProvider, ConvexReactClient } from \"convex/react\";\n\nconst convex = new ConvexReactClient(process.env.NEXT_PUBLIC_CONVEX_URL!);\n\nexport function ConvexClientProvider({ children }: { children: ReactNode }) {\n  return <ConvexProvider client={convex}>{children}</ConvexProvider>;\n}\n"], "names": [], "mappings": ";;;AAKqC;;AAFrC;AAAA;AAHA;;;AAKA,MAAM,SAAS,IAAI,2JAAA,CAAA,oBAAiB;AAE7B,SAAS,qBAAqB,EAAE,QAAQ,EAA2B;IACxE,qBAAO,6LAAC,2JAAA,CAAA,iBAAc;QAAC,QAAQ;kBAAS;;;;;;AAC1C;KAFgB", "debugId": null}}, {"offset": {"line": 40, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/providers/theme-provider.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport { ThemeProvider as NextThemesProvider } from \"next-themes\";\nimport { type ThemeProviderProps } from \"next-themes/dist/types\";\n\nexport function ThemeProvider({ children, ...props }: ThemeProviderProps) {\n  return <NextThemesProvider {...props}>{children}</NextThemesProvider>;\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAMO,SAAS,cAAc,EAAE,QAAQ,EAAE,GAAG,OAA2B;IACtE,qBAAO,6LAAC,mJAAA,CAAA,gBAAkB;QAAE,GAAG,KAAK;kBAAG;;;;;;AACzC;KAFgB", "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/contexts/auth-context.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { createContext, useContext, useEffect, useState } from \"react\";\n\nexport interface Admin {\n  _id: string;\n  email: string;\n  firstName: string;\n  lastName: string;\n  role?: string;\n  permissions?: string[];\n}\n\ninterface AuthContextType {\n  admin: Admin | null;\n  isLoading: boolean;\n  isAuthenticated: boolean;\n  csrfToken: string | null;\n  login: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;\n  logout: () => Promise<{ success: boolean; error?: string }>;\n  refreshSession: () => Promise<void>;\n  hasPermission: (permission: string) => boolean;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport function useAuth() {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error(\"useAuth must be used within an AuthProvider\");\n  }\n  return context;\n}\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [admin, setAdmin] = useState<Admin | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [csrfToken, setCsrfToken] = useState<string | null>(null);\n\n  // Get CSRF token from cookie\n  const getCSRFToken = (): string | null => {\n    if (typeof document === 'undefined') return null;\n    const cookies = document.cookie.split(';');\n    const csrfCookie = cookies.find(cookie => cookie.trim().startsWith('benzochem-csrf-token='));\n    return csrfCookie ? csrfCookie.split('=')[1] : null;\n  };\n\n  // Check session on mount and periodically\n  useEffect(() => {\n    const checkSession = async () => {\n      try {\n        const response = await fetch('/api/auth/session', {\n          method: 'GET',\n          credentials: 'include',\n        });\n\n        if (response.ok) {\n          const data = await response.json();\n          if (data.success && data.admin) {\n            setAdmin(data.admin);\n            setCsrfToken(getCSRFToken());\n          } else {\n            setAdmin(null);\n            setCsrfToken(null);\n          }\n        } else {\n          setAdmin(null);\n          setCsrfToken(null);\n        }\n      } catch (error) {\n        console.error('Session check error:', error);\n        setAdmin(null);\n        setCsrfToken(null);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    checkSession();\n\n    // Set up periodic session refresh (every 30 minutes)\n    const interval = setInterval(checkSession, 30 * 60 * 1000);\n    return () => clearInterval(interval);\n  }, []);\n\n  const login = async (email: string, password: string): Promise<{ success: boolean; error?: string }> => {\n    try {\n      setIsLoading(true);\n\n      const response = await fetch('/api/auth/login', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          ...(csrfToken && { 'X-CSRF-Token': csrfToken }),\n        },\n        credentials: 'include',\n        body: JSON.stringify({ email, password, csrfToken }),\n      });\n\n      const data = await response.json();\n\n      if (response.ok && data.success) {\n        setAdmin(data.admin);\n        setCsrfToken(data.csrfToken);\n        setIsLoading(false);\n        return { success: true };\n      } else {\n        setIsLoading(false);\n        return { success: false, error: data.error || \"Login failed\" };\n      }\n    } catch (error) {\n      console.error(\"Login error:\", error);\n      setIsLoading(false);\n      return { success: false, error: \"Login failed. Please try again.\" };\n    }\n  };\n\n  const logout = async (): Promise<{ success: boolean; error?: string }> => {\n    try {\n      const response = await fetch('/api/auth/logout', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          ...(csrfToken && { 'X-CSRF-Token': csrfToken }),\n        },\n        credentials: 'include',\n        body: JSON.stringify({ csrfToken }),\n      });\n\n      // Clear local state regardless of API response\n      setAdmin(null);\n      setCsrfToken(null);\n\n      // Force redirect to login page after logout\n      if (typeof window !== 'undefined') {\n        window.location.href = '/login';\n      }\n\n      if (response.ok) {\n        return { success: true };\n      } else {\n        const data = await response.json();\n        return { success: false, error: data.error || \"Logout failed\" };\n      }\n    } catch (error) {\n      console.error(\"Logout error:\", error);\n      // Clear local state even on error\n      setAdmin(null);\n      setCsrfToken(null);\n\n      // Force redirect to login page even on error\n      if (typeof window !== 'undefined') {\n        window.location.href = '/login';\n      }\n\n      return { success: false, error: \"Logout failed\" };\n    }\n  };\n\n  const refreshSession = async (): Promise<void> => {\n    try {\n      const response = await fetch('/api/auth/session', {\n        method: 'GET',\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success && data.admin) {\n          setAdmin(data.admin);\n          setCsrfToken(getCSRFToken());\n        } else {\n          setAdmin(null);\n          setCsrfToken(null);\n        }\n      } else {\n        setAdmin(null);\n        setCsrfToken(null);\n      }\n    } catch (error) {\n      console.error('Session refresh error:', error);\n      setAdmin(null);\n      setCsrfToken(null);\n    }\n  };\n\n  const hasPermission = (permission: string): boolean => {\n    if (!admin) return false;\n    if (admin.role === \"super_admin\") return true; // Super admins have all permissions\n    return admin.permissions?.includes(permission) || false;\n  };\n\n  const isAuthenticated = !!admin;\n\n  const value: AuthContextType = {\n    admin,\n    isLoading,\n    isAuthenticated,\n    csrfToken,\n    login,\n    logout,\n    refreshSession,\n    hasPermission,\n  };\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\n}\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAwBA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANgB;AAQT,SAAS,aAAa,EAAE,QAAQ,EAAiC;;IACtE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAE1D,6BAA6B;IAC7B,MAAM,eAAe;QACnB,IAAI,OAAO,aAAa,aAAa,OAAO;QAC5C,MAAM,UAAU,SAAS,MAAM,CAAC,KAAK,CAAC;QACtC,MAAM,aAAa,QAAQ,IAAI,CAAC,CAAA,SAAU,OAAO,IAAI,GAAG,UAAU,CAAC;QACnE,OAAO,aAAa,WAAW,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG;IACjD;IAEA,0CAA0C;IAC1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM;uDAAe;oBACnB,IAAI;wBACF,MAAM,WAAW,MAAM,MAAM,qBAAqB;4BAChD,QAAQ;4BACR,aAAa;wBACf;wBAEA,IAAI,SAAS,EAAE,EAAE;4BACf,MAAM,OAAO,MAAM,SAAS,IAAI;4BAChC,IAAI,KAAK,OAAO,IAAI,KAAK,KAAK,EAAE;gCAC9B,SAAS,KAAK,KAAK;gCACnB,aAAa;4BACf,OAAO;gCACL,SAAS;gCACT,aAAa;4BACf;wBACF,OAAO;4BACL,SAAS;4BACT,aAAa;wBACf;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,wBAAwB;wBACtC,SAAS;wBACT,aAAa;oBACf,SAAU;wBACR,aAAa;oBACf;gBACF;;YAEA;YAEA,qDAAqD;YACrD,MAAM,WAAW,YAAY,cAAc,KAAK,KAAK;YACrD;0CAAO,IAAM,cAAc;;QAC7B;iCAAG,EAAE;IAEL,MAAM,QAAQ,OAAO,OAAe;QAClC,IAAI;YACF,aAAa;YAEb,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,GAAI,aAAa;wBAAE,gBAAgB;oBAAU,CAAC;gBAChD;gBACA,aAAa;gBACb,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAO;oBAAU;gBAAU;YACpD;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,IAAI,KAAK,OAAO,EAAE;gBAC/B,SAAS,KAAK,KAAK;gBACnB,aAAa,KAAK,SAAS;gBAC3B,aAAa;gBACb,OAAO;oBAAE,SAAS;gBAAK;YACzB,OAAO;gBACL,aAAa;gBACb,OAAO;oBAAE,SAAS;oBAAO,OAAO,KAAK,KAAK,IAAI;gBAAe;YAC/D;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,aAAa;YACb,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAkC;QACpE;IACF;IAEA,MAAM,SAAS;QACb,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,oBAAoB;gBAC/C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,GAAI,aAAa;wBAAE,gBAAgB;oBAAU,CAAC;gBAChD;gBACA,aAAa;gBACb,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAU;YACnC;YAEA,+CAA+C;YAC/C,SAAS;YACT,aAAa;YAEb,4CAA4C;YAC5C,wCAAmC;gBACjC,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,OAAO;oBAAE,SAAS;gBAAK;YACzB,OAAO;gBACL,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,OAAO;oBAAE,SAAS;oBAAO,OAAO,KAAK,KAAK,IAAI;gBAAgB;YAChE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,kCAAkC;YAClC,SAAS;YACT,aAAa;YAEb,6CAA6C;YAC7C,wCAAmC;gBACjC,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB;YAEA,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAgB;QAClD;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,qBAAqB;gBAChD,QAAQ;gBACR,aAAa;YACf;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,IAAI,KAAK,KAAK,EAAE;oBAC9B,SAAS,KAAK,KAAK;oBACnB,aAAa;gBACf,OAAO;oBACL,SAAS;oBACT,aAAa;gBACf;YACF,OAAO;gBACL,SAAS;gBACT,aAAa;YACf;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,SAAS;YACT,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,CAAC,OAAO,OAAO;QACnB,IAAI,MAAM,IAAI,KAAK,eAAe,OAAO,MAAM,oCAAoC;QACnF,OAAO,MAAM,WAAW,EAAE,SAAS,eAAe;IACpD;IAEA,MAAM,kBAAkB,CAAC,CAAC;IAE1B,MAAM,QAAyB;QAC7B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBAAO,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAC9C;IA5KgB;KAAA", "debugId": null}}, {"offset": {"line": 295, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/components/ui/sonner.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useTheme } from \"next-themes\"\nimport { Toaster as Son<PERSON>, ToasterP<PERSON> } from \"sonner\"\n\nconst Toaster = ({ ...props }: ToasterProps) => {\n  const { theme = \"system\" } = useTheme()\n\n  return (\n    <Sonner\n      theme={theme as ToasterProps[\"theme\"]}\n      className=\"toaster group\"\n      style={\n        {\n          \"--normal-bg\": \"var(--popover)\",\n          \"--normal-text\": \"var(--popover-foreground)\",\n          \"--normal-border\": \"var(--border)\",\n        } as React.CSSProperties\n      }\n      {...props}\n    />\n  )\n}\n\nexport { Toaster }\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKA,MAAM,UAAU,CAAC,EAAE,GAAG,OAAqB;;IACzC,MAAM,EAAE,QAAQ,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IAEpC,qBACE,6LAAC,2IAAA,CAAA,UAAM;QACL,OAAO;QACP,WAAU;QACV,OACE;YACE,eAAe;YACf,iBAAiB;YACjB,mBAAmB;QACrB;QAED,GAAG,KAAK;;;;;;AAGf;GAjBM;;QACyB,mJAAA,CAAA,WAAQ;;;KADjC", "debugId": null}}]}