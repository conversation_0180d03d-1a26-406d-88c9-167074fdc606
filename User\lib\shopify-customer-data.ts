import { storefrontFetch, adminFetch } from "./shopify"

// GraphQL query to get customer data by access token
const CUSTOMER_QUERY = `
  query getCustomerData($customerAccessToken: String!) {
    customer(customerAccessToken: $customerAccessToken) {
      id
      firstName
      lastName
      email
      phone
      displayName
      defaultAddress {
        id
        address1
        address2
        city
        province
        country
        zip
        phone
      }
      addresses(first: 10) {
        edges {
          node {
            id
            address1
            address2
            city
            province
            country
            zip
            phone
          }
        }
      }
      orders(first: 5) {
        edges {
          node {
            id
            orderNumber
            processedAt
            financialStatus
            fulfillmentStatus
            totalPrice {
              amount
              currencyCode
            }
            lineItems(first: 5) {
              edges {
                node {
                  title
                  quantity
                  variant {
                    price {
                      amount
                      currencyCode
                    }
                    image {
                      url
                      altText
                    }
                  }
                }
              }
            }
          }
        }
      }

    }
  }
`

// Get customer access token from localStorage
export async function getCustomerAccessToken(): Promise<string | null> {
  
  if (typeof window === 'undefined') return null;
  
  try {
    // First try the key from customer-service.ts
    const CUSTOMER_ACCESS_TOKEN_KEY = "shopify_customer_access_token";
    let token = localStorage.getItem(CUSTOMER_ACCESS_TOKEN_KEY);
    
    // If not found, try the old key for backward compatibility
    if (!token) {
      const oldToken = localStorage.getItem('shopifyCustomerAccessToken');
      if (oldToken) {
        // If found in old format, migrate it to the new format
        try {
          const tokenData = JSON.parse(oldToken);
          localStorage.setItem(CUSTOMER_ACCESS_TOKEN_KEY, tokenData.accessToken);
          localStorage.setItem(`${CUSTOMER_ACCESS_TOKEN_KEY}_expires`, tokenData.expiresAt);
          // Clean up old token
          localStorage.removeItem('shopifyCustomerAccessToken');
          return tokenData.accessToken;
        } catch (parseError) {
          console.error('Error parsing old token format:', parseError);
        }
      }
      return null;
    }
    
    // Check expiration for the new format
    const expiresAt = localStorage.getItem(`${CUSTOMER_ACCESS_TOKEN_KEY}_expires`);
    if (!expiresAt) return null;
    
    if (new Date(expiresAt) < new Date()) {
      // Token is expired
      localStorage.removeItem(CUSTOMER_ACCESS_TOKEN_KEY);
      localStorage.removeItem(`${CUSTOMER_ACCESS_TOKEN_KEY}_expires`);
      return null;
    }
    
    return token;
  } catch (error) {
    console.error('Error getting customer access token:', error);
    return null;
  }
}

// GraphQL query to find a customer by Clerk ID metafield
const CUSTOMER_BY_CLERK_ID_QUERY = `
  query getCustomerByClerkId($clerkId: String!) {
    customers(first: 1, query: $clerkId) {
      edges {
        node {
          id
          firstName
          lastName
          email
          phone
          displayName
          defaultAddress {
            id
            address1
            address2
            city
            province
            country
            zip
            phone
          }
          addresses(first: 10) {
            edges {
              node {
                id
                address1
                address2
                city
                province
                country
                zip
                phone
              }
            }
          }
          orders(first: 5) {
            edges {
              node {
                id
                orderNumber
                processedAt
                financialStatus
                fulfillmentStatus
                totalPrice {
                  amount
                  currencyCode
                }
                lineItems(first: 5) {
                  edges {
                    node {
                      title
                      quantity
                      variant {
                        price {
                          amount
                          currencyCode
                        }
                        image {
                          url
                          altText
                        }
                      }
                    }
                  }
                }
              }
            }
          }

        }
      }
    }
  }
`;

// Fetch customer data by Clerk ID
export async function fetchShopifyCustomerByClerkId(clerkId: string) {
  try {
    if (!clerkId) {
      console.log('No Clerk ID provided to fetchShopifyCustomerByClerkId');
      return null;
    }
    
    console.log(`Fetching Shopify customer data for Clerk ID: ${clerkId}`);
    
    // This would need to be implemented with the Shopify Admin API
    // For now, we'll use a server-side API route to handle this securely
    const response = await fetch(`/api/shopify/customer-by-clerk-id?clerkId=${encodeURIComponent(clerkId)}`);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Error response from customer-by-clerk-id API: Status ${response.status}, Body:`, errorText);
      throw new Error(`Failed to fetch customer by Clerk ID: ${response.status} ${response.statusText}`);
    }
    
    const data = await response.json();
    
    if (!data || !data.customer) {
      return null;
    }
    
    const customer = data.customer;
    
    // Extract metafields
    const metafields = customer.metafields?.edges.reduce((acc: any, edge: any) => {
      const { namespace, key, value } = edge.node;
      if (!acc[namespace]) acc[namespace] = {};
      acc[namespace][key] = value;
      return acc;
    }, {});
    
    // Format orders
    const orders = customer.orders?.edges.map((edge: any) => {
      const order = edge.node;
      return {
        id: order.id,
        orderNumber: order.name, // Changed from orderNumber to name
        processedAt: order.processedAt,
        financialStatus: order.displayFinancialStatus, // Changed from financialStatus to displayFinancialStatus
        fulfillmentStatus: order.displayFulfillmentStatus, // Changed from fulfillmentStatus to displayFulfillmentStatus
        totalPrice: order.totalPriceSet?.shopMoney, // Changed to use totalPriceSet.shopMoney
        lineItems: order.lineItems?.edges.map((itemEdge: any) => {
          const item = itemEdge.node;
          return {
            title: item.title,
            quantity: item.quantity,
            variant: item.variant
          };
        })
      };
    });
    
    // Format addresses - addresses are now directly an array, not edges
    const addresses = customer.addresses;
    
    return {
      id: customer.id,
      firstName: customer.firstName,
      lastName: customer.lastName,
      name: customer.displayName || `${customer.firstName} ${customer.lastName}`,
      email: customer.email,
      phone: customer.phone,
      defaultAddress: customer.defaultAddress,
      addresses,
      orders,
      businessName: metafields?.custom?.business_name || metafields?.custom?.trade_name || metafields?.customer?.business_name || '',
      gstNumber: metafields?.custom?.gstin || metafields?.customer?.gst_number || '',
      isGstVerified: metafields?.custom?.gst_status === 'Active' || metafields?.customer?.is_gst_verified === 'true',
      clerkId: metafields?.customer?.clerk_id || '',
    };
  } catch (error) {
    console.error('Error fetching Shopify customer by Clerk ID:', error);
    return null;
  }
}

// Fetch customer data from Shopify
export async function fetchShopifyCustomerData() {
  try {
    // Check if we're on the client side
    if (typeof window === 'undefined') {
      console.log('fetchShopifyCustomerData called on server side - returning null');
      return null;
    }

    console.log('🔍 fetchShopifyCustomerData called on CLIENT side');

    // Get customer access token using the migration-aware function
    let customerAccessToken = localStorage.getItem('shopify_customer_access_token');

    // If not found, check for legacy uppercase key and migrate
    if (!customerAccessToken) {
      const legacyToken = localStorage.getItem('SHOPIFY_CUSTOMER_ACCESS_TOKEN');
      if (legacyToken) {
        console.log('🔄 Found token in legacy uppercase format, migrating...');
        customerAccessToken = legacyToken;
        localStorage.setItem('shopify_customer_access_token', legacyToken);

        // Try to migrate expiration if it exists
        const legacyExpires = localStorage.getItem('SHOPIFY_CUSTOMER_ACCESS_TOKEN_EXPIRES');
        if (legacyExpires) {
          localStorage.setItem('shopify_customer_access_token_expires', legacyExpires);
          localStorage.removeItem('SHOPIFY_CUSTOMER_ACCESS_TOKEN_EXPIRES');
        }

        // Clean up legacy key
        localStorage.removeItem('SHOPIFY_CUSTOMER_ACCESS_TOKEN');
        console.log('✅ Token migration completed in fetchShopifyCustomerData');
      }
    }

    console.log('🔑 Customer access token from localStorage:', customerAccessToken ? `${customerAccessToken.substring(0, 10)}...` : 'NOT FOUND');

    if (!customerAccessToken) {
      console.log('❌ No customer access token found in localStorage');
      return null;
    }

    // Check if token is expired
    const expiresAt = localStorage.getItem('shopify_customer_access_token_expires');
    console.log('⏰ Token expires at:', expiresAt);

    if (expiresAt && new Date(expiresAt) <= new Date()) {
      console.log('❌ Customer access token has expired');
      // Clean up expired token
      localStorage.removeItem('shopify_customer_access_token');
      localStorage.removeItem('shopify_customer_access_token_expires');
      return null;
    }

    console.log('✅ Using valid customer access token from localStorage');

  const { data } = await storefrontFetch<{ customer: any }>({
    query: CUSTOMER_QUERY,
    variables: {
      customerAccessToken
    }
  });

  if (!data || !data.customer) {
    throw new Error('No customer data found');
  }

  // Extract and format customer data
  const customer = data.customer;

  // Fetch metafields separately using the customer access token
  let metafields = {};
  try {
    const metafieldIdentifiers = [
      "customer.business_name",
      "customer.gst_number",
      "customer.is_gst_verified",
      "customer.clerk_id",
      // Custom namespace metafields (from GST verification)
      "custom.business_name",
      "custom.gstin",
      "custom.legal_name_of_business",
      "custom.trade_name",
      "custom.gst_status"
    ];

    const { data: metafieldData } = await storefrontFetch<any>({
      query: `
        query getCustomerMetafields($customerAccessToken: String!, $identifiers: [HasMetafieldsIdentifier!]!) {
          customer(customerAccessToken: $customerAccessToken) {
            id
            metafields(identifiers: $identifiers) {
              namespace
              key
              value
            }
          }
        }
      `,
      variables: {
        customerAccessToken,
        identifiers: metafieldIdentifiers.map(id => {
          const [namespace, key] = id.split('.');
          return { namespace, key };
        })
      }
    });

    if (metafieldData?.customer?.metafields) {
      metafields = metafieldData.customer.metafields.reduce((acc: any, metafield: any) => {
        if (metafield) {
          const { namespace, key, value } = metafield;
          if (!acc[namespace]) acc[namespace] = {};
          acc[namespace][key] = value;
        }
        return acc;
      }, {});
    }
  } catch (metafieldError) {
    console.warn('Error fetching metafields:', metafieldError);
    // Continue without metafields
  }

  // Format orders
  const orders = customer.orders?.edges.map((edge: any) => {
    const order = edge.node;
    return {
      id: order.id,
      orderNumber: order.name, // Changed from orderNumber to name
      processedAt: order.processedAt,
      financialStatus: order.displayFinancialStatus, // Changed from financialStatus to displayFinancialStatus
      fulfillmentStatus: order.displayFulfillmentStatus, // Changed from fulfillmentStatus to displayFulfillmentStatus
      totalPrice: order.totalPriceSet?.shopMoney, // Changed to use totalPriceSet.shopMoney
      lineItems: order.lineItems?.edges.map((itemEdge: any) => {
        const item = itemEdge.node;
        return {
          title: item.title,
          quantity: item.quantity,
          variant: item.variant
        };
      })
    };
  });

  // Format addresses - addresses are now directly an array, not edges
  const addresses = customer.addresses;

  return {
    id: customer.id,
    firstName: customer.firstName,
    lastName: customer.lastName,
    name: customer.displayName || `${customer.firstName} ${customer.lastName}`,
    email: customer.email,
    phone: customer.phone,
    defaultAddress: customer.defaultAddress,
    addresses,
    orders,
    businessName: (metafields as any)?.custom?.business_name || (metafields as any)?.custom?.trade_name || (metafields as any)?.customer?.business_name || '',
    gstNumber: (metafields as any)?.custom?.gstin || (metafields as any)?.customer?.gst_number || '',
    isGstVerified: (metafields as any)?.custom?.gst_status === 'Active' || (metafields as any)?.customer?.is_gst_verified === 'true',
    clerkId: (metafields as any)?.customer?.clerk_id || '',
  };
} catch (error) {
  console.error('Error fetching Shopify customer data:', error);
  return null;
}
}

// Admin API query to get customer by Clerk ID metafield - old approach with full customer search
const ADMIN_CUSTOMER_BY_CLERK_ID_QUERY = `
  query getCustomerByClerkId($query: String!) {
    customers(first: 20, query: $query) {
      edges {
        node {
          id
          firstName
          lastName
          displayName
          email
          phone
          defaultAddress {
            id
            address1
            address2
            city
            province
            country
            zip
            phone
          }
          addresses {
            id
            address1
            address2
            city
            province
            country
            zip
            phone
          }
          orders(first: 5) {
            edges {
              node {
                id
                name
                processedAt
                displayFinancialStatus
                displayFulfillmentStatus
                totalPriceSet {
                  shopMoney {
                    amount
                    currencyCode
                  }
                }
                lineItems(first: 5) {
                  edges {
                    node {
                      title
                      quantity
                      variant {
                        price
                        image {
                          url
                          altText
                        }
                      }
                    }
                  }
                }
              }
            }
          }
          metafields(first: 50) {
            edges {
              node {
                namespace
                key
                value
              }
            }
          }
        }
      }
    }
  }
`;

// Server action to fetch customer data from Shopify Admin API by Clerk ID
// New direct query to get customer by clerk_id using customerByIdentifier
// CustomerIdentifierInput is a OneOf type, so we need to construct the query properly
const CUSTOMER_BY_IDENTIFIER_QUERY = `
  query GetCustomerByClerkId($metafieldValue: String!) {
    customers(first: 1, query: $metafieldValue) {
      edges {
        node {
          id
          firstName
          lastName
          displayName
          email
          phone
          defaultAddress {
            id
            address1
            address2
            city
            province
            country
            zip
          }
          addresses {
            id
            address1
            address2
            city
            province
            country
            zip
          }
          orders(first: 10) {
            edges {
              node {
                id
                name
                processedAt
                displayFinancialStatus
                displayFulfillmentStatus
                totalPriceSet {
                  shopMoney {
                    amount
                    currencyCode
                  }
                }
                lineItems(first: 5) {
                  edges {
                    node {
                      title
                      quantity
                      variant {
                        id
                        title
                        price
                        image {
                          url
                        }
                      }
                    }
                  }
                }
              }
            }
          }
          metafields(first: 20) {
            edges {
              node {
                namespace
                key
                value
              }
            }
          }
        }
      }
    }
  }
`;

export async function fetchCustomerByClerkIdFromAdmin(clerkId: string, email?: string | null) {
  try {
    if (!clerkId) {
      console.log('No Clerk ID provided to fetchCustomerByClerkIdFromAdmin');
      return null;
    }

    console.log(`Fetching Shopify customer data for Clerk ID: ${clerkId}`);
    
    // Build a more specific query that includes email if available
    let searchQuery = `metafield.namespace:customer AND metafield.key:clerk_id AND metafield.value:${clerkId}`;
    
    // Add email verification if provided
    if (email) {
      console.log(`Including email verification in query: ${email}`);
      searchQuery = `${searchQuery} AND email:${email}`;
    }
    
    console.log(`Using enhanced search query: ${searchQuery}`);
    
    const response = await adminFetch<any>({
      query: CUSTOMER_BY_IDENTIFIER_QUERY,
      variables: {
        metafieldValue: searchQuery
      }
    });
    
    console.log('Customer query response:', JSON.stringify(response, null, 2));
    
    const { data } = response;

    if (!data?.customers?.edges?.length) {
      console.log('No customer found with the provided Clerk ID');
      console.log('Full response data:', JSON.stringify(data, null, 2));
      return null;
    }
    
    const customer = data.customers.edges[0].node;
    
    // Verify that the found customer's email matches the provided email if available
    if (email && customer.email !== email) {
      console.log(`Found customer email ${customer.email} does not match provided email ${email}`);
      console.log('This may indicate a mismatch between Clerk user and Shopify customer - checking other results');
      
      // Look for the customer with matching email in the results if any
      const matchingCustomer = data.customers.edges.find((edge: { node: { email: string } }) => edge.node.email === email)?.node;
      if (matchingCustomer) {
        console.log(`Found customer with matching email in results at position other than first`);
        return matchingCustomer;
      }
    }
    console.log('Found customer:', JSON.stringify(customer, null, 2));

    // Extract metafields
    console.log('Raw metafields data:', JSON.stringify(customer.metafields, null, 2));
    
    // Process metafields
    const metafields = customer.metafields?.edges.reduce((acc: any, edge: any) => {
      const { namespace, key, value } = edge.node;
      console.log(`Found metafield: namespace=${namespace}, key=${key}, value=${value}`);
      if (!acc[namespace]) acc[namespace] = {};
      acc[namespace][key] = value;
      return acc;
    }, {});
    
    console.log('Processed metafields:', JSON.stringify(metafields, null, 2));

    // Format orders
    const orders = customer.orders?.edges.map((edge: any) => {
      const order = edge.node;
      return {
        id: order.id,
        orderNumber: order.name,
        processedAt: order.processedAt,
        financialStatus: order.displayFinancialStatus,
        fulfillmentStatus: order.displayFulfillmentStatus,
        totalPrice: order.totalPriceSet?.shopMoney,
        lineItems: order.lineItems?.edges.map((itemEdge: any) => {
          const item = itemEdge.node;
          return {
            title: item.title,
            quantity: item.quantity,
            variant: item.variant
          };
        })
      };
    });

    const formattedCustomer = {
      id: customer.id,
      firstName: customer.firstName,
      lastName: customer.lastName,
      name: customer.displayName || `${customer.firstName} ${customer.lastName}`,
      email: customer.email,
      phone: customer.phone,
      defaultAddress: customer.defaultAddress,
      addresses: customer.addresses,
      orders,
      businessName: metafields?.custom?.business_name || metafields?.custom?.trade_name || metafields?.customer?.business_name || '',
      gstNumber: metafields?.custom?.gstin || metafields?.customer?.gst_number || '',
      isGstVerified: metafields?.custom?.gst_status === 'Active' || metafields?.customer?.is_gst_verified === 'true',
      clerkId: clerkId, // Use the clerkId we searched with
      metafields, // Include the full metafields object for UI display
    };
    
    console.log('Formatted customer data:', JSON.stringify(formattedCustomer, null, 2));
    
    return formattedCustomer;
  } catch (error) {
    console.error('Error fetching customer by Clerk ID from Admin API:', error);
    return null;
  }
}
