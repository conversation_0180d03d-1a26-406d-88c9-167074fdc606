// Environment variables validation utility

export interface ShopifyConfig {
  // Public variables (available on client-side)
  storeDomain: string
  storefrontAccessToken: string
  
  // Server-side only variables
  adminAccessToken?: string
  apiVersion: string
  webhookSecret?: string
}

export interface RapidAPIConfig {
  key: string
  gstHost: string
  gstBaseUrl: string
}

export interface GoogleAPIConfig {
  placesApiKey: string
}

export interface AppConfig {
  shopify: ShopifyConfig
  rapidApi: RapidAPIConfig
  googleApi: GoogleAPIConfig
}

/**
 * Validates and returns Shopify configuration
 * @param serverSide - Whether to include server-side only variables
 */
export function getShopifyConfig(serverSide: boolean = false): ShopifyConfig {
  const config: ShopifyConfig = {
    storeDomain: process.env.NEXT_PUBLIC_SHOPIFY_STORE_DOMAIN || '',
    storefrontAccessToken: process.env.NEXT_PUBLIC_SHOPIFY_STOREFRONT_ACCESS_TOKEN || '',
    apiVersion: process.env.SHOPIFY_API_VERSION || '2025-04',
  }

  // Add server-side variables if requested
  if (serverSide) {
    config.adminAccessToken = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN
    config.webhookSecret = process.env.SHOPIFY_WEBHOOK_SECRET
  }

  // Validate required public variables
  const missingVars: string[] = []
  
  if (!config.storeDomain) {
    missingVars.push('NEXT_PUBLIC_SHOPIFY_STORE_DOMAIN')
  }
  
  if (!config.storefrontAccessToken) {
    missingVars.push('NEXT_PUBLIC_SHOPIFY_STOREFRONT_ACCESS_TOKEN')
  }

  // Validate server-side variables if requested
  if (serverSide) {
    if (!config.adminAccessToken) {
      missingVars.push('SHOPIFY_ADMIN_API_ACCESS_TOKEN')
    }
  }

  if (missingVars.length > 0) {
    throw new Error(`Missing required Shopify environment variables: ${missingVars.join(', ')}`)
  }

  return config
}

/**
 * Validates and returns RapidAPI configuration
 */
export function getRapidAPIConfig(): RapidAPIConfig {
  const config: RapidAPIConfig = {
    key: process.env.RAPIDAPI_KEY || '',
    gstHost: process.env.RAPIDAPI_GST_HOST || '',
    gstBaseUrl: process.env.RAPIDAPI_GST_BASE_URL || '',
  }

  const missingVars: string[] = []

  if (!config.key) {
    missingVars.push('RAPIDAPI_KEY')
  }

  if (!config.gstHost) {
    missingVars.push('RAPIDAPI_GST_HOST')
  }

  if (!config.gstBaseUrl) {
    missingVars.push('RAPIDAPI_GST_BASE_URL')
  }

  if (missingVars.length > 0) {
    throw new Error(`Missing required RapidAPI environment variables: ${missingVars.join(', ')}`)
  }

  return config
}

/**
 * Validates and returns Google API configuration
 */
export function getGoogleAPIConfig(): GoogleAPIConfig {
  const config: GoogleAPIConfig = {
    placesApiKey: process.env.GOOGLE_PLACES_API_KEY || '',
  }

  const missingVars: string[] = []

  if (!config.placesApiKey) {
    missingVars.push('GOOGLE_PLACES_API_KEY')
  }

  if (missingVars.length > 0) {
    throw new Error(`Missing required Google API environment variables: ${missingVars.join(', ')}`)
  }

  return config
}

/**
 * Validates and returns complete application configuration
 */
export function getAppConfig(serverSide: boolean = false): AppConfig {
  return {
    shopify: getShopifyConfig(serverSide),
    rapidApi: getRapidAPIConfig(),
    googleApi: getGoogleAPIConfig(),
  }
}

/**
 * Logs environment variables status (without exposing sensitive values)
 */
export function logEnvStatus(): void {
  console.log('🔧 Environment Variables Status:')
  console.log('================================')
  
  // Public Shopify variables
  console.log('📦 Shopify Public Variables:')
  console.log(`  NEXT_PUBLIC_SHOPIFY_STORE_DOMAIN: ${process.env.NEXT_PUBLIC_SHOPIFY_STORE_DOMAIN ? '✅ SET' : '❌ MISSING'}`)
  console.log(`  NEXT_PUBLIC_SHOPIFY_STOREFRONT_ACCESS_TOKEN: ${process.env.NEXT_PUBLIC_SHOPIFY_STOREFRONT_ACCESS_TOKEN ? '✅ SET' : '❌ MISSING'}`)
  
  // Server-side Shopify variables
  console.log('🔒 Shopify Server Variables:')
  console.log(`  SHOPIFY_STORE_DOMAIN: ${process.env.SHOPIFY_STORE_DOMAIN ? '✅ SET' : '❌ MISSING'}`)
  console.log(`  SHOPIFY_ADMIN_API_ACCESS_TOKEN: ${process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN ? '✅ SET' : '❌ MISSING'}`)
  console.log(`  SHOPIFY_API_VERSION: ${process.env.SHOPIFY_API_VERSION || 'DEFAULT (2025-04)'}`)
  console.log(`  SHOPIFY_WEBHOOK_SECRET: ${process.env.SHOPIFY_WEBHOOK_SECRET ? '✅ SET' : '❌ MISSING'}`)
  
  // RapidAPI variables
  console.log('🚀 RapidAPI Variables:')
  console.log(`  RAPIDAPI_KEY: ${process.env.RAPIDAPI_KEY ? '✅ SET' : '❌ MISSING'}`)
  console.log(`  RAPIDAPI_GST_HOST: ${process.env.RAPIDAPI_GST_HOST ? '✅ SET' : '❌ MISSING'}`)
  console.log(`  RAPIDAPI_GST_BASE_URL: ${process.env.RAPIDAPI_GST_BASE_URL ? '✅ SET' : '❌ MISSING'}`)

  // Google API variables
  console.log('🗺️ Google API Variables:')
  console.log(`  GOOGLE_PLACES_API_KEY: ${process.env.GOOGLE_PLACES_API_KEY ? '✅ SET' : '❌ MISSING'}`)

  console.log('================================')
}

/**
 * Validates environment variables and throws if any are missing
 */
export function validateEnvironment(serverSide: boolean = false): void {
  try {
    getAppConfig(serverSide)
    console.log('✅ All required environment variables are properly configured')
  } catch (error) {
    console.error('❌ Environment validation failed:', error)
    throw error
  }
}
