"use server";

import { adminFetch } from "@/lib/shopify";

// Metafield types
interface MetafieldInput {
  namespace: string;
  key: string;
  value: string;
  type: string;
}

interface MetafieldResult {
  id: string;
  namespace: string;
  key: string;
  value: string;
  type: string;
  createdAt: string;
  updatedAt: string;
}

interface CustomerData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string; // Combined country code + phone
  // businessName?: string; // Removed - will use tradeName or legalNameOfBusiness
  gstNumber: string;
  agreedToEmailMarketing?: boolean;
  agreedToSmsMarketing?: boolean;
  // Fields from GST Verification
  legalNameOfBusiness?: string;
  tradeName?: string;
  dateOfRegistration?: string; // Expected YYYY-MM-DD
  constitutionOfBusiness?: string;
  taxpayerType?: string;
  gstStatus?: string; // 'Active', 'Inactive', etc.
  principalPlaceOfBusinessAddress?: string;
  principalPlaceOfBusinessEmail?: string;
  principalPlaceOfBusinessMobile?: string;
  natureOfCoreBusinessActivity?: string;
  clerkId?: string; // Clerk user ID for linking accounts
}

export async function createShopifyCustomerAction(customerData: CustomerData): Promise<{ success: boolean; error?: string; shopifyCustomerId?: string }> {
  console.log("Server Action: Attempting to create Shopify customer for", customerData.email, "with GST data:", JSON.stringify(customerData, null, 2));

  const mutation = `
    mutation customerCreate($input: CustomerInput!) {
      customerCreate(input: $input) {
        customer {
          id
          firstName
          lastName
          email
          phone
          emailMarketingConsent {
            marketingState
            consentUpdatedAt
          }
          smsMarketingConsent {
            marketingState
            consentUpdatedAt
          }
        }
        userErrors {
          field
          message
        }
      }
    }
  `;

  const now = new Date().toISOString();

  const metafields = [
    {
      namespace: "custom",
      key: "gstin",
      value: customerData.gstNumber,
      type: "single_line_text_field"
    }
  ];
  
  // Add Clerk ID to metafields if provided
  if (customerData.clerkId) {
    metafields.push({
      namespace: "customer",
      key: "clerk_id",
      value: customerData.clerkId,
      type: "single_line_text_field"
    });
  }

  // Conditionally add metafields if data exists
  const shopifyBusinessName = customerData.tradeName || customerData.legalNameOfBusiness;
  if (shopifyBusinessName) {
    metafields.push({
      namespace: "custom",
      key: "business_name",
      value: shopifyBusinessName,
      type: "single_line_text_field"
    });
  }
  if (customerData.legalNameOfBusiness) {
    metafields.push({
      namespace: "custom",
      key: "legal_name_of_business",
      value: customerData.legalNameOfBusiness,
      type: "single_line_text_field"
    });
  }
  if (customerData.tradeName) {
    metafields.push({
      namespace: "custom",
      key: "trade_name",
      value: customerData.tradeName,
      type: "single_line_text_field"
    });
  }
  if (customerData.dateOfRegistration) {
    metafields.push({
      namespace: "custom",
      key: "date_of_registration",
      value: customerData.dateOfRegistration,
      type: "date" // Updated to match Shopify metafield definition
    });
  }
  if (customerData.constitutionOfBusiness) {
    metafields.push({
      namespace: "custom",
      key: "constitution_of_business",
      value: customerData.constitutionOfBusiness,
      type: "single_line_text_field"
    });
  }
  if (customerData.taxpayerType) {
    metafields.push({
      namespace: "custom",
      key: "taxpayer_type",
      value: customerData.taxpayerType,
      type: "single_line_text_field"
    });
  }
  if (customerData.gstStatus) {
    metafields.push({
      namespace: "custom",
      key: "gst_status",
      value: customerData.gstStatus,
      type: "single_line_text_field"
    });
  }
  if (customerData.principalPlaceOfBusinessAddress) {
    // Format for Shopify rich_text_field requires a specific JSON structure with root and children
    const richTextValue = JSON.stringify({
      "type": "root",
      "children": [
        {
          "type": "paragraph",
          "children": [
            {
              "type": "text",
              "value": customerData.principalPlaceOfBusinessAddress
            }
          ]
        }
      ]
    });
    
    metafields.push({
      namespace: "custom",
      key: "principal_place_of_business_address",
      value: richTextValue,
      type: "rich_text_field"
    });
  }
  if (customerData.principalPlaceOfBusinessEmail) {
    metafields.push({
      namespace: "custom",
      key: "principal_place_of_business_email",
      value: customerData.principalPlaceOfBusinessEmail,
      type: "single_line_text_field"
    });
  }
  if (customerData.principalPlaceOfBusinessMobile) {
    metafields.push({
      namespace: "custom",
      key: "principal_place_of_business_mobile",
      value: customerData.principalPlaceOfBusinessMobile,
      type: "single_line_text_field"
    });
  }
  if (customerData.natureOfCoreBusinessActivity) {
    metafields.push({
      namespace: "custom",
      key: "nature_of_core_business_activity",
      value: customerData.natureOfCoreBusinessActivity,
      type: "single_line_text_field" // Updated to match Shopify metafield definition
    });
  }


  const variables = {
    input: {
      email: customerData.email,
      phone: customerData.phone,
      firstName: customerData.firstName,
      lastName: customerData.lastName,
      emailMarketingConsent: customerData.agreedToEmailMarketing ? {
        marketingState: "SUBSCRIBED",
        consentUpdatedAt: now,
        marketingOptInLevel: "SINGLE_OPT_IN"
      } : {
        marketingState: "UNSUBSCRIBED",
        consentUpdatedAt: now
      },
      smsMarketingConsent: customerData.agreedToSmsMarketing ? {
        marketingState: "SUBSCRIBED",
        consentUpdatedAt: now,
        marketingOptInLevel: "SINGLE_OPT_IN"
      } : {
        marketingState: "UNSUBSCRIBED",
        consentUpdatedAt: now
      },
      metafields: metafields
    }
  };

  try {
    const shopifyResult = await adminFetch<any>({ query: mutation, variables });
    console.log("Server Action: Shopify customerCreate result:", JSON.stringify(shopifyResult, null, 2));

    if (shopifyResult.errors || shopifyResult.data?.customerCreate?.userErrors?.length > 0) {
      const errors = shopifyResult.errors || shopifyResult.data?.customerCreate?.userErrors;
      const errorMessage = `Failed to create Shopify customer: ${JSON.stringify(errors)}`;
      console.error("Server Action: " + errorMessage);
      return { success: false, error: "Failed to sync account with Shopify. Please contact support." };
    }

    if (shopifyResult.data?.customerCreate?.customer?.id) {
      const customerId = shopifyResult.data.customerCreate.customer.id;
      console.log("Server Action: Shopify customer created successfully:", customerId);
      return { success: true, shopifyCustomerId: customerId };
    } else {
      console.error("Server Action: Shopify customer creation did not return customer ID.");
      return { success: false, error: "Failed to confirm Shopify sync. Please contact support." };
    }
  } catch (err: any) {
    console.error("Server Action: Error calling Shopify Admin API:", err);
    if (err.message?.includes("Missing Shopify environment variables")) {
        return { success: false, error: "Shopify connection error (Admin). Please contact support." };
    }
    return { success: false, error: "Error syncing account with Shopify. Please contact support." };
  }
}

// ===== METAFIELD MANAGEMENT FUNCTIONS =====

/**
 * Create or update customer metafields
 */
export async function setCustomerMetafields(
  customerId: string,
  metafields: MetafieldInput[]
): Promise<{ success: boolean; error?: string; metafields?: MetafieldResult[] }> {
  console.log("Server Action: Setting metafields for customer", customerId, "with data:", JSON.stringify(metafields, null, 2));

  const mutation = `
    mutation customerUpdate($input: CustomerInput!) {
      customerUpdate(input: $input) {
        customer {
          id
          metafields(first: 50) {
            edges {
              node {
                id
                namespace
                key
                value
                type
                createdAt
                updatedAt
              }
            }
          }
        }
        userErrors {
          field
          message
        }
      }
    }
  `;

  const variables = {
    input: {
      id: customerId,
      metafields: metafields
    }
  };

  try {
    const result = await adminFetch<any>({ query: mutation, variables });
    console.log("Server Action: Customer metafields update result:", JSON.stringify(result, null, 2));

    // Check for network/API errors first
    if (result.errors) {
      const errorMessage = `Shopify API errors: ${JSON.stringify(result.errors)}`;
      console.error("Server Action: " + errorMessage);
      return { success: false, error: `Failed to update customer information: ${errorMessage}` };
    }

    // Check if we got no data back (API call failed)
    if (!result.data) {
      console.error("Server Action: No data returned from Shopify API");
      return { success: false, error: "Failed to update customer information: No response from Shopify API" };
    }

    // Check for GraphQL user errors
    if (result.data?.customerUpdate?.userErrors?.length > 0) {
      const userErrors = result.data.customerUpdate.userErrors;
      const errorMessage = `Shopify user errors: ${JSON.stringify(userErrors)}`;
      console.error("Server Action: " + errorMessage);
      return { success: false, error: `Failed to update customer information: ${errorMessage}` };
    }

    // Check if the mutation was successful
    if (result.data?.customerUpdate?.customer?.metafields) {
      const metafieldResults = result.data.customerUpdate.customer.metafields.edges.map((edge: any) => edge.node);
      console.log("Server Action: Customer metafields updated successfully");
      return { success: true, metafields: metafieldResults };
    } else {
      console.error("Server Action: Customer metafields update did not return expected data.");
      console.error("Server Action: Full result data:", JSON.stringify(result.data, null, 2));
      return { success: false, error: "Failed to confirm metafields update - no metafields returned." };
    }
  } catch (err: any) {
    console.error("Server Action: Error updating customer metafields:", err);
    return { success: false, error: `Error updating customer information: ${err.message || 'Unknown error'}` };
  }
}

/**
 * Get customer metafields
 */
export async function getCustomerMetafields(
  customerId: string,
  namespace?: string
): Promise<{ success: boolean; error?: string; metafields?: MetafieldResult[] }> {
  console.log("Server Action: Getting metafields for customer", customerId, "namespace:", namespace);

  const query = `
    query getCustomer($id: ID!, $namespace: String) {
      customer(id: $id) {
        id
        metafields(first: 50, namespace: $namespace) {
          edges {
            node {
              id
              namespace
              key
              value
              type
              createdAt
              updatedAt
            }
          }
        }
      }
    }
  `;

  const variables = {
    id: customerId,
    ...(namespace && { namespace })
  };

  try {
    const result = await adminFetch<any>({ query, variables });
    console.log("Server Action: Customer metafields query result:", JSON.stringify(result, null, 2));

    // Check for network/API errors first
    if (result.errors) {
      const errorMessage = `Shopify API errors: ${JSON.stringify(result.errors)}`;
      console.error("Server Action: " + errorMessage);
      return { success: false, error: `Failed to retrieve customer information: ${errorMessage}` };
    }

    // Check if we got no data back (API call failed)
    if (!result.data) {
      console.error("Server Action: No data returned from Shopify API");
      return { success: false, error: "Failed to retrieve customer information: No response from Shopify API" };
    }

    if (result.data?.customer?.metafields) {
      const metafields = result.data.customer.metafields.edges.map((edge: any) => edge.node);
      console.log("Server Action: Customer metafields retrieved successfully");
      return { success: true, metafields };
    } else {
      console.log("Server Action: No metafields found for customer");
      return { success: true, metafields: [] };
    }
  } catch (err: any) {
    console.error("Server Action: Error getting customer metafields:", err);
    return { success: false, error: `Error retrieving customer information: ${err.message || 'Unknown error'}` };
  }
}

/**
 * Get a specific customer metafield value
 */
export async function getCustomerMetafieldValue(
  customerId: string,
  namespace: string,
  key: string
): Promise<{ success: boolean; error?: string; value?: string }> {
  console.log("Server Action: Getting metafield value for customer", customerId, "namespace:", namespace, "key:", key);

  const result = await getCustomerMetafields(customerId, namespace);

  if (!result.success) {
    return result;
  }

  const metafield = result.metafields?.find(m => m.key === key);

  if (metafield) {
    console.log("Server Action: Metafield value found:", metafield.value);
    return { success: true, value: metafield.value };
  } else {
    console.log("Server Action: Metafield not found");
    return { success: true, value: undefined };
  }
}

/**
 * Delete customer metafields
 */
export async function deleteCustomerMetafields(
  metafieldIds: string[]
): Promise<{ success: boolean; error?: string }> {
  console.log("Server Action: Deleting metafields:", metafieldIds);

  const mutation = `
    mutation metafieldsDelete($metafields: [MetafieldIdentifierInput!]!) {
      metafieldsDelete(metafields: $metafields) {
        deletedMetafields {
          ownerId
          namespace
          key
        }
        userErrors {
          field
          message
        }
      }
    }
  `;

  const variables = {
    metafields: metafieldIds.map(id => ({ id }))
  };

  try {
    const result = await adminFetch<any>({ query: mutation, variables });
    console.log("Server Action: Metafields delete result:", JSON.stringify(result, null, 2));

    if (result.errors || result.data?.metafieldsDelete?.userErrors?.length > 0) {
      const errors = result.errors || result.data?.metafieldsDelete?.userErrors;
      const errorMessage = `Failed to delete metafields: ${JSON.stringify(errors)}`;
      console.error("Server Action: " + errorMessage);
      return { success: false, error: "Failed to delete customer information." };
    }

    console.log("Server Action: Metafields deleted successfully");
    return { success: true };
  } catch (err: any) {
    console.error("Server Action: Error deleting metafields:", err);
    return { success: false, error: "Error deleting customer information." };
  }
}

// ===== UTILITY FUNCTIONS FOR COMMON METAFIELD OPERATIONS =====

/**
 * Update customer GST information
 */
export async function updateCustomerGSTInfo(
  customerId: string,
  gstData: {
    gstNumber?: string;
    legalNameOfBusiness?: string;
    tradeName?: string;
    dateOfRegistration?: string;
    constitutionOfBusiness?: string;
    taxpayerType?: string;
    gstStatus?: string;
    principalPlaceOfBusinessAddress?: string;
    principalPlaceOfBusinessEmail?: string;
    principalPlaceOfBusinessMobile?: string;
    natureOfCoreBusinessActivity?: string;
  }
): Promise<{ success: boolean; error?: string }> {
  console.log("Server Action: Updating GST info for customer", customerId, "with data:", JSON.stringify(gstData, null, 2));

  const metafields: MetafieldInput[] = [];

  // Add metafields for each provided GST data field
  if (gstData.gstNumber) {
    metafields.push({
      namespace: "custom",
      key: "gstin",
      value: gstData.gstNumber,
      type: "single_line_text_field"
    });
  }

  if (gstData.legalNameOfBusiness) {
    metafields.push({
      namespace: "custom",
      key: "legal_name_of_business",
      value: gstData.legalNameOfBusiness,
      type: "single_line_text_field"
    });
  }

  if (gstData.tradeName) {
    metafields.push({
      namespace: "custom",
      key: "business_name",
      value: gstData.tradeName,
      type: "single_line_text_field"
    });
  }

  if (gstData.dateOfRegistration) {
    metafields.push({
      namespace: "custom",
      key: "date_of_registration",
      value: gstData.dateOfRegistration,
      type: "date"
    });
  }

  if (gstData.constitutionOfBusiness) {
    metafields.push({
      namespace: "custom",
      key: "constitution_of_business",
      value: gstData.constitutionOfBusiness,
      type: "single_line_text_field"
    });
  }

  if (gstData.taxpayerType) {
    metafields.push({
      namespace: "custom",
      key: "taxpayer_type",
      value: gstData.taxpayerType,
      type: "single_line_text_field"
    });
  }

  if (gstData.gstStatus) {
    metafields.push({
      namespace: "custom",
      key: "gst_status",
      value: gstData.gstStatus,
      type: "single_line_text_field"
    });
  }

  if (gstData.principalPlaceOfBusinessAddress) {
    // Format for rich text field
    const richTextValue = JSON.stringify({
      "type": "root",
      "children": [
        {
          "type": "paragraph",
          "children": [
            {
              "type": "text",
              "value": gstData.principalPlaceOfBusinessAddress
            }
          ]
        }
      ]
    });

    metafields.push({
      namespace: "custom",
      key: "principal_place_of_business_address",
      value: richTextValue,
      type: "rich_text_field"
    });
  }

  if (gstData.principalPlaceOfBusinessEmail) {
    metafields.push({
      namespace: "custom",
      key: "principal_place_of_business_email",
      value: gstData.principalPlaceOfBusinessEmail,
      type: "single_line_text_field"
    });
  }

  if (gstData.principalPlaceOfBusinessMobile) {
    metafields.push({
      namespace: "custom",
      key: "principal_place_of_business_mobile",
      value: gstData.principalPlaceOfBusinessMobile,
      type: "single_line_text_field"
    });
  }

  if (gstData.natureOfCoreBusinessActivity) {
    metafields.push({
      namespace: "custom",
      key: "nature_of_core_business_activity",
      value: gstData.natureOfCoreBusinessActivity,
      type: "single_line_text_field"
    });
  }

  if (metafields.length === 0) {
    return { success: false, error: "No GST data provided to update." };
  }

  return await setCustomerMetafields(customerId, metafields);
}

/**
 * Update customer business preferences
 */
export async function updateCustomerBusinessPreferences(
  customerId: string,
  preferences: {
    preferredPaymentMethod?: string;
    creditLimit?: number;
    businessCategory?: string;
    annualTurnover?: string;
    numberOfEmployees?: string;
    businessType?: string;
    industryType?: string;
    notes?: string;
  }
): Promise<{ success: boolean; error?: string }> {
  console.log("Server Action: Updating business preferences for customer", customerId, "with data:", JSON.stringify(preferences, null, 2));

  const metafields: MetafieldInput[] = [];

  if (preferences.preferredPaymentMethod) {
    metafields.push({
      namespace: "business",
      key: "preferred_payment_method",
      value: preferences.preferredPaymentMethod,
      type: "single_line_text_field"
    });
  }

  if (preferences.creditLimit !== undefined) {
    metafields.push({
      namespace: "business",
      key: "credit_limit",
      value: preferences.creditLimit.toString(),
      type: "number_decimal"
    });
  }

  if (preferences.businessCategory) {
    metafields.push({
      namespace: "business",
      key: "business_category",
      value: preferences.businessCategory,
      type: "single_line_text_field"
    });
  }

  if (preferences.annualTurnover) {
    metafields.push({
      namespace: "business",
      key: "annual_turnover",
      value: preferences.annualTurnover,
      type: "single_line_text_field"
    });
  }

  if (preferences.numberOfEmployees) {
    metafields.push({
      namespace: "business",
      key: "number_of_employees",
      value: preferences.numberOfEmployees,
      type: "single_line_text_field"
    });
  }

  if (preferences.businessType) {
    metafields.push({
      namespace: "business",
      key: "business_type",
      value: preferences.businessType,
      type: "single_line_text_field"
    });
  }

  if (preferences.industryType) {
    metafields.push({
      namespace: "business",
      key: "industry_type",
      value: preferences.industryType,
      type: "single_line_text_field"
    });
  }

  if (preferences.notes) {
    const richTextValue = JSON.stringify({
      "type": "root",
      "children": [
        {
          "type": "paragraph",
          "children": [
            {
              "type": "text",
              "value": preferences.notes
            }
          ]
        }
      ]
    });

    metafields.push({
      namespace: "business",
      key: "notes",
      value: richTextValue,
      type: "rich_text_field"
    });
  }

  if (metafields.length === 0) {
    return { success: false, error: "No business preferences provided to update." };
  }

  return await setCustomerMetafields(customerId, metafields);
}

/**
 * Update customer basic information (firstName, lastName only)
 */
export async function updateCustomerBasicInfo(
  customerId: string,
  customerData: {
    firstName?: string;
    lastName?: string;
  }
): Promise<{ success: boolean; error?: string; customer?: any }> {
  console.log("Server Action: Updating customer basic info for", customerId, "with data:", JSON.stringify(customerData, null, 2));

  const mutation = `
    mutation customerUpdate($input: CustomerInput!) {
      customerUpdate(input: $input) {
        customer {
          id
          firstName
          lastName
          email
          phone
          defaultAddress {
            id
            address1
            address2
            city
            province
            country
            zip
          }
        }
        userErrors {
          field
          message
        }
      }
    }
  `;

  // Build the input object - only include basic info, no addresses
  const input: any = {
    id: customerId,
  };

  // Add basic info if provided
  if (customerData.firstName !== undefined) {
    input.firstName = customerData.firstName;
  }
  if (customerData.lastName !== undefined) {
    input.lastName = customerData.lastName;
  }

  const variables = { input };

  try {
    const response = await adminFetch<any>({
      query: mutation,
      variables
    });

    console.log("Customer update response:", JSON.stringify(response, null, 2));

    if (response.errors) {
      console.error("GraphQL errors:", response.errors);
      return {
        success: false,
        error: `GraphQL errors: ${response.errors.map((e: any) => e.message).join(', ')}`
      };
    }

    const { customer, userErrors } = response.data?.customerUpdate || {};

    if (userErrors && userErrors.length > 0) {
      console.error("User errors:", userErrors);
      return {
        success: false,
        error: userErrors.map((e: any) => e.message).join(', ')
      };
    }

    if (!customer) {
      return {
        success: false,
        error: "No customer data returned from update"
      };
    }

    console.log("✅ Customer basic info updated successfully");
    return {
      success: true,
      customer
    };

  } catch (error) {
    console.error("Error updating customer basic info:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred"
    };
  }
}

/**
 * Update customer address information
 */
export async function updateCustomerAddress(
  customerId: string,
  addressData: {
    address1?: string;
    address2?: string;
    city?: string;
    province?: string;
    country?: string;
    zip?: string;
    firstName?: string;
    lastName?: string;
    phone?: string;
    company?: string;
  }
): Promise<{ success: boolean; error?: string; customer?: any }> {
  console.log("Server Action: Updating customer address for", customerId, "with data:", JSON.stringify(addressData, null, 2));

  // First, get the current customer to check if they have an existing address
  const getCustomerQuery = `
    query getCustomer($id: ID!) {
      customer(id: $id) {
        id
        defaultAddress {
          id
        }
      }
    }
  `;

  try {
    const customerResponse = await adminFetch<any>({
      query: getCustomerQuery,
      variables: { id: customerId }
    });

    if (customerResponse.errors) {
      console.error("Error fetching customer:", customerResponse.errors);
      return {
        success: false,
        error: `Error fetching customer: ${customerResponse.errors.map((e: any) => e.message).join(', ')}`
      };
    }

    const existingCustomer = customerResponse.data?.customer;
    const hasExistingAddress = existingCustomer?.defaultAddress?.id;

    let mutation: string;
    let variables: any;

    if (hasExistingAddress) {
      // Update existing address
      mutation = `
        mutation customerAddressUpdate($customerId: ID!, $addressId: ID!, $address: MailingAddressInput!) {
          customerAddressUpdate(customerId: $customerId, addressId: $addressId, address: $address) {
            address {
              id
              firstName
              lastName
              company
              address1
              address2
              city
              province
              country
              zip
              phone
            }
            userErrors {
              field
              message
            }
          }
        }
      `;

      variables = {
        customerId: customerId,
        addressId: existingCustomer.defaultAddress.id,
        address: {
          firstName: addressData.firstName || '',
          lastName: addressData.lastName || '',
          company: addressData.company || '',
          address1: addressData.address1 || '',
          address2: addressData.address2 || '',
          city: addressData.city || '',
          province: addressData.province || '',
          country: addressData.country || '',
          zip: addressData.zip || '',
          phone: addressData.phone || ''
        }
      };
    } else {
      // Create new address
      mutation = `
        mutation customerAddressCreate($customerId: ID!, $address: MailingAddressInput!, $setAsDefault: Boolean) {
          customerAddressCreate(customerId: $customerId, address: $address, setAsDefault: $setAsDefault) {
            address {
              id
              firstName
              lastName
              company
              address1
              address2
              city
              province
              country
              zip
              phone
            }
            userErrors {
              field
              message
            }
          }
        }
      `;

      variables = {
        customerId: customerId,
        address: {
          firstName: addressData.firstName || '',
          lastName: addressData.lastName || '',
          company: addressData.company || '',
          address1: addressData.address1 || '',
          address2: addressData.address2 || '',
          city: addressData.city || '',
          province: addressData.province || '',
          country: addressData.country || '',
          zip: addressData.zip || '',
          phone: addressData.phone || ''
        },
        setAsDefault: true
      };
    }

    const response = await adminFetch<any>({
      query: mutation,
      variables
    });

    console.log("Address update response:", JSON.stringify(response, null, 2));

    if (response.errors) {
      console.error("GraphQL errors:", response.errors);
      return {
        success: false,
        error: `GraphQL errors: ${response.errors.map((e: any) => e.message).join(', ')}`
      };
    }

    const result = response.data?.customerAddressUpdate || response.data?.customerAddressCreate;
    const { address, userErrors } = result || {};

    if (userErrors && userErrors.length > 0) {
      console.error("User errors:", userErrors);
      return {
        success: false,
        error: userErrors.map((e: any) => e.message).join(', ')
      };
    }

    if (!address) {
      return {
        success: false,
        error: "No address data returned from update"
      };
    }

    // For new addresses, the setAsDefault: true parameter handles setting it as default

    // Fetch the updated customer data
    const updatedCustomerResponse = await adminFetch<any>({
      query: `
        query getUpdatedCustomer($id: ID!) {
          customer(id: $id) {
            id
            firstName
            lastName
            email
            phone
            defaultAddress {
              id
              firstName
              lastName
              company
              address1
              address2
              city
              province
              country
              zip
              phone
            }
          }
        }
      `,
      variables: { id: customerId }
    });

    console.log("✅ Customer address updated successfully");
    return {
      success: true,
      customer: updatedCustomerResponse.data?.customer
    };

  } catch (error) {
    console.error("Error updating customer address:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred"
    };
  }
}

/**
 * Update customer marketing consent using separate mutations for email and SMS
 */
export async function updateCustomerMarketingConsent(
  customerId: string,
  consent: {
    emailMarketing?: boolean;
    smsMarketing?: boolean;
  }
): Promise<{ success: boolean; error?: string }> {
  console.log("Server Action: Updating marketing consent for customer", customerId, "with data:", JSON.stringify(consent, null, 2));

  const now = new Date().toISOString();
  let emailResult: any = null;
  let smsResult: any = null;

  // Update email marketing consent if provided using the dedicated mutation
  if (consent.emailMarketing !== undefined) {
    const emailMutation = `
      mutation customerEmailMarketingConsentUpdate($input: CustomerEmailMarketingConsentUpdateInput!) {
        customerEmailMarketingConsentUpdate(input: $input) {
          customer {
            id
            emailMarketingConsent {
              marketingState
              consentUpdatedAt
            }
          }
          userErrors {
            field
            message
          }
        }
      }
    `;

    const emailVariables = {
      input: {
        customerId: customerId,
        emailMarketingConsent: consent.emailMarketing ? {
          marketingState: "SUBSCRIBED",
          consentUpdatedAt: now,
          marketingOptInLevel: "SINGLE_OPT_IN"
        } : {
          marketingState: "UNSUBSCRIBED",
          consentUpdatedAt: now
        }
      }
    };

    try {
      emailResult = await adminFetch<any>({ query: emailMutation, variables: emailVariables });
      console.log("Server Action: Email marketing consent update result:", JSON.stringify(emailResult, null, 2));

      if (emailResult.errors || emailResult.data?.customerEmailMarketingConsentUpdate?.userErrors?.length > 0) {
        const errors = emailResult.errors || emailResult.data?.customerEmailMarketingConsentUpdate?.userErrors;
        console.error("Server Action: Failed to update email marketing consent:", JSON.stringify(errors));
        return { success: false, error: `Failed to update email marketing consent: ${JSON.stringify(errors)}` };
      }
    } catch (err: any) {
      console.error("Server Action: Error updating email marketing consent:", err);
      return { success: false, error: `Error updating email marketing consent: ${err.message || 'Unknown error'}` };
    }
  }

  // Update SMS marketing consent if provided using the dedicated mutation
  if (consent.smsMarketing !== undefined) {
    const smsMutation = `
      mutation customerSmsMarketingConsentUpdate($input: CustomerSmsMarketingConsentUpdateInput!) {
        customerSmsMarketingConsentUpdate(input: $input) {
          customer {
            id
            smsMarketingConsent {
              marketingState
              consentUpdatedAt
            }
          }
          userErrors {
            field
            message
          }
        }
      }
    `;

    const smsVariables = {
      input: {
        customerId: customerId,
        smsMarketingConsent: consent.smsMarketing ? {
          marketingState: "SUBSCRIBED",
          consentUpdatedAt: now,
          marketingOptInLevel: "SINGLE_OPT_IN"
        } : {
          marketingState: "UNSUBSCRIBED",
          consentUpdatedAt: now
        }
      }
    };

    try {
      smsResult = await adminFetch<any>({ query: smsMutation, variables: smsVariables });
      console.log("Server Action: SMS marketing consent update result:", JSON.stringify(smsResult, null, 2));

      if (smsResult.errors || smsResult.data?.customerSmsMarketingConsentUpdate?.userErrors?.length > 0) {
        const errors = smsResult.errors || smsResult.data?.customerSmsMarketingConsentUpdate?.userErrors;
        console.error("Server Action: Failed to update SMS marketing consent:", JSON.stringify(errors));
        return { success: false, error: `Failed to update SMS marketing consent: ${JSON.stringify(errors)}` };
      }
    } catch (err: any) {
      console.error("Server Action: Error updating SMS marketing consent:", err);
      return { success: false, error: `Error updating SMS marketing consent: ${err.message || 'Unknown error'}` };
    }
  }

  console.log("Server Action: Marketing consent updated successfully");
  return { success: true };
}

/**
 * Get customer GST information
 */
export async function getCustomerGSTInfo(customerId: string): Promise<{
  success: boolean;
  error?: string;
  gstInfo?: {
    gstNumber?: string;
    legalNameOfBusiness?: string;
    tradeName?: string;
    dateOfRegistration?: string;
    constitutionOfBusiness?: string;
    taxpayerType?: string;
    gstStatus?: string;
    principalPlaceOfBusinessAddress?: string;
    principalPlaceOfBusinessEmail?: string;
    principalPlaceOfBusinessMobile?: string;
    natureOfCoreBusinessActivity?: string;
  };
}> {
  console.log("Server Action: Getting GST info for customer", customerId);

  const result = await getCustomerMetafields(customerId, "custom");

  if (!result.success) {
    return result;
  }

  const gstInfo: any = {};

  result.metafields?.forEach(metafield => {
    switch (metafield.key) {
      case "gstin":
        gstInfo.gstNumber = metafield.value;
        break;
      case "legal_name_of_business":
        gstInfo.legalNameOfBusiness = metafield.value;
        break;
      case "trade_name":
        gstInfo.tradeName = metafield.value;
        break;
      case "date_of_registration":
        gstInfo.dateOfRegistration = metafield.value;
        break;
      case "constitution_of_business":
        gstInfo.constitutionOfBusiness = metafield.value;
        break;
      case "taxpayer_type":
        gstInfo.taxpayerType = metafield.value;
        break;
      case "gst_status":
        gstInfo.gstStatus = metafield.value;
        break;
      case "principal_place_of_business_address":
        try {
          const parsed = JSON.parse(metafield.value);
          gstInfo.principalPlaceOfBusinessAddress = parsed.children?.[0]?.children?.[0]?.value || metafield.value;
        } catch {
          gstInfo.principalPlaceOfBusinessAddress = metafield.value;
        }
        break;
      case "principal_place_of_business_email":
        gstInfo.principalPlaceOfBusinessEmail = metafield.value;
        break;
      case "principal_place_of_business_mobile":
        gstInfo.principalPlaceOfBusinessMobile = metafield.value;
        break;
      case "nature_of_core_business_activity":
        gstInfo.natureOfCoreBusinessActivity = metafield.value;
        break;
    }
  });

  console.log("Server Action: GST info retrieved successfully");
  return { success: true, gstInfo };
}

