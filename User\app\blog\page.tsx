import type { Metada<PERSON> } from "next"
import { Suspense } from 'react' // Added
import PageHeader from "@/components/page-header"
import BlogGrid from "@/components/blog-grid"
import { getAllBlogPosts } from "@/lib/blog"
import { Skeleton } from "@/components/ui/skeleton" // Added
import { Filter } from "lucide-react" // Added for skeleton filter button

export const metadata: Metadata = {
  title: "Blog | Benzochem Industries",
  description: "Latest news, insights, and updates from Benzochem Industries.",
}

// Skeleton for an individual blog post card
function BlogPostCardSkeleton() {
  return (
    <div className="bg-white rounded-lg overflow-hidden shadow-sm">
      <Skeleton className="aspect-[16/9] w-full rounded-t-lg" /> {/* Image */}
      <div className="p-6">
        <div className="flex items-center mb-4">
          <Skeleton className="h-5 w-20 rounded-full mr-3" /> {/* Category */}
          <Skeleton className="h-4 w-16 rounded" /> {/* Date */}
        </div>
        <Skeleton className="h-6 w-3/4 mb-3 rounded" /> {/* Title */}
        <Skeleton className="h-4 w-full mb-1 rounded" /> {/* Excerpt line 1 */}
        <Skeleton className="h-4 w-full mb-1 rounded" /> {/* Excerpt line 2 */}
        <Skeleton className="h-4 w-5/6 mb-4 rounded" /> {/* Excerpt line 3 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Skeleton className="w-8 h-8 rounded-full mr-2" /> {/* Author Avatar */}
            <Skeleton className="h-4 w-24 rounded" /> {/* Author Name */}
          </div>
          <Skeleton className="h-4 w-20 rounded" /> {/* Read More */}
        </div>
      </div>
    </div>
  )
}

// Skeleton for the entire blog page content (grid part)
function BlogPageSkeleton() {
  return (
    <section className="py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="space-y-8">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <Skeleton className="h-7 w-48 mb-1 rounded" /> {/* "Latest Articles" title */}
              <Skeleton className="h-4 w-32 rounded" /> {/* Article count */}
            </div>
            <Skeleton className="h-10 w-40 rounded-md flex items-center justify-center gap-2"> {/* Filter Button */}
              <Filter className="h-4 w-4 text-gray-300" />
              <span className="text-gray-300">Loading...</span>
            </Skeleton>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[...Array(6)].map((_, i) => ( // Show 6 skeleton cards
              <BlogPostCardSkeleton key={i} />
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}

// Component to fetch and render actual blog content
async function BlogPageContent() {
  // Although getAllBlogPosts might be synchronous if reading from local files,
  // using an async component here is good practice for Suspense.
  const posts = await Promise.resolve(getAllBlogPosts()) // Ensure it's awaitable

  return (
    <section className="py-16 md:py-24">
      <div className="container mx-auto px-4">
        <BlogGrid posts={posts} />
      </div>
    </section>
  )
}

export default function BlogPage() {
  return (
    <main className="flex min-h-screen flex-col pt-20">
      <PageHeader title="Blog & News" description="Industry insights, company updates, and chemical knowledge" />
      <Suspense fallback={<BlogPageSkeleton />}>
        <BlogPageContent />
      </Suspense>
    </main>
  )
}
