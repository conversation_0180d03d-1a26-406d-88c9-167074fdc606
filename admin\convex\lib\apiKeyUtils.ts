"use node";

import { webcrypto } from "crypto";

// API Key Configuration
const API_KEY_CONFIG = {
  // Prefix for Benzochem API keys (similar to sk- for OpenAI, pk_ for Stripe)
  prefix: "bzk_",
  // Length of the random part (excluding prefix and checksum)
  keyLength: 32,
  // Environment indicators
  environments: {
    live: "live",
    test: "test"
  }
} as const;

/**
 * Generate a cryptographically secure API key with proper format
 * Format: bzk_[env]_[32_random_chars][4_checksum_chars]
 * Example: bzk_live_1a2b3c4d5e6f7g8h9i0j1k2l3m4n5o6p7q8r
 */
export function generateSecureApiKey(environment: "live" | "test" = "live"): {
  key: string;
  keyId: string;
} {
  // Generate cryptographically secure random bytes
  const randomBytes = new Uint8Array(24); // 24 bytes = 32 base64url chars
  webcrypto.getRandomValues(randomBytes);
  
  // Convert to base64url (URL-safe base64 without padding)
  const randomPart = btoa(String.fromCharCode(...randomBytes))
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
  
  // Generate a key ID for tracking (first 8 chars of random part)
  const keyId = randomPart.substring(0, 8);
  
  // Create checksum from the random part for integrity verification
  const checksum = generateChecksum(randomPart);
  
  // Construct the final API key
  const key = `${API_KEY_CONFIG.prefix}${environment}_${randomPart}${checksum}`;
  
  return { key, keyId };
}

/**
 * Generate a 4-character checksum for the API key
 */
function generateChecksum(input: string): string {
  let hash = 0;
  for (let i = 0; i < input.length; i++) {
    const char = input.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  
  // Convert to base36 and pad to 4 characters
  return Math.abs(hash).toString(36).padStart(4, '0').substring(0, 4);
}

/**
 * Validate API key format
 */
export function validateApiKeyFormat(key: string): {
  isValid: boolean;
  environment?: "live" | "test";
  keyId?: string;
  error?: string;
} {
  if (!key || typeof key !== 'string') {
    return { isValid: false, error: 'API key must be a non-empty string' };
  }

  // Check prefix
  if (!key.startsWith(API_KEY_CONFIG.prefix)) {
    return { isValid: false, error: 'Invalid API key prefix' };
  }

  // Parse the key structure
  const withoutPrefix = key.substring(API_KEY_CONFIG.prefix.length);
  const parts = withoutPrefix.split('_');
  
  if (parts.length !== 2) {
    return { isValid: false, error: 'Invalid API key format' };
  }

  const [environment, keyPart] = parts;
  
  // Validate environment
  if (!Object.values(API_KEY_CONFIG.environments).includes(environment as any)) {
    return { isValid: false, error: 'Invalid environment in API key' };
  }

  // Validate key part length (32 chars + 4 checksum)
  if (keyPart.length !== 36) {
    return { isValid: false, error: 'Invalid API key length' };
  }

  const randomPart = keyPart.substring(0, 32);
  const providedChecksum = keyPart.substring(32);
  const expectedChecksum = generateChecksum(randomPart);

  if (providedChecksum !== expectedChecksum) {
    return { isValid: false, error: 'Invalid API key checksum' };
  }

  const keyId = randomPart.substring(0, 8);

  return {
    isValid: true,
    environment: environment as "live" | "test",
    keyId
  };
}

/**
 * Hash an API key for secure storage
 * Uses a combination of the key and a salt for security
 */
export async function hashApiKey(key: string): Promise<string> {
  // Validate key format first
  const validation = validateApiKeyFormat(key);
  if (!validation.isValid) {
    throw new Error(`Invalid API key format: ${validation.error}`);
  }

  // Use Web Crypto API for hashing
  const encoder = new TextEncoder();
  const data = encoder.encode(key);
  
  // Use SHA-256 for hashing
  const hashBuffer = await webcrypto.subtle.digest('SHA-256', data);
  const hashArray = new Uint8Array(hashBuffer);
  
  // Convert to hex string
  const hashHex = Array.from(hashArray)
    .map(b => b.toString(16).padStart(2, '0'))
    .join('');
  
  return hashHex;
}

/**
 * Verify an API key against its hash
 */
export async function verifyApiKey(key: string, hash: string): Promise<boolean> {
  try {
    const keyHash = await hashApiKey(key);
    return keyHash === hash;
  } catch (error) {
    console.error('Error verifying API key:', error);
    return false;
  }
}

/**
 * Extract metadata from API key without verification
 */
export function extractApiKeyMetadata(key: string): {
  keyId: string;
  environment: "live" | "test";
  prefix: string;
} | null {
  const validation = validateApiKeyFormat(key);
  if (!validation.isValid) {
    return null;
  }

  return {
    keyId: validation.keyId!,
    environment: validation.environment!,
    prefix: API_KEY_CONFIG.prefix
  };
}

/**
 * Generate API key for display (masked version)
 */
export function maskApiKey(key: string): string {
  const validation = validateApiKeyFormat(key);
  if (!validation.isValid) {
    return 'Invalid key';
  }

  // Show prefix, environment, first 4 chars of key ID, and last 4 chars
  const withoutPrefix = key.substring(API_KEY_CONFIG.prefix.length);
  const [environment, keyPart] = withoutPrefix.split('_');
  const keyId = keyPart.substring(0, 4);
  const lastChars = keyPart.substring(keyPart.length - 4);
  
  return `${API_KEY_CONFIG.prefix}${environment}_${keyId}...${lastChars}`;
}

/**
 * Rate limiting configuration types
 */
export interface RateLimitConfig {
  requestsPerMinute: number;
  requestsPerHour: number;
  requestsPerDay: number;
  burstLimit?: number; // Allow short bursts above the per-minute limit
}

/**
 * Default rate limits for different API key types
 */
export const DEFAULT_RATE_LIMITS: Record<string, RateLimitConfig> = {
  standard: {
    requestsPerMinute: 100,
    requestsPerHour: 5000,
    requestsPerDay: 50000,
    burstLimit: 150
  },
  premium: {
    requestsPerMinute: 500,
    requestsPerHour: 25000,
    requestsPerDay: 250000,
    burstLimit: 750
  },
  enterprise: {
    requestsPerMinute: 2000,
    requestsPerHour: 100000,
    requestsPerDay: 1000000,
    burstLimit: 3000
  }
};

/**
 * API key permissions
 */
export const API_PERMISSIONS = {
  // Product access
  'products:read': 'Read product information',
  'products:write': 'Create and update products',
  'products:delete': 'Delete products',
  
  // Collection access
  'collections:read': 'Read collection information',
  'collections:write': 'Create and update collections',
  'collections:delete': 'Delete collections',
  
  // User access (limited)
  'users:read': 'Read user information (limited)',
  'users:write': 'Update user information (limited)',
  
  // Analytics access
  'analytics:read': 'Read analytics data',
  
  // Webhook access
  'webhooks:read': 'Read webhook configurations',
  'webhooks:write': 'Create and update webhooks',
  
  // Admin access (highly restricted)
  'admin:read': 'Read admin information',
  'admin:write': 'Admin operations (highly restricted)',
} as const;

export type ApiPermission = keyof typeof API_PERMISSIONS;
