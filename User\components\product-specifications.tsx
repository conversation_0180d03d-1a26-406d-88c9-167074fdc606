"use client"
import { motion } from "framer-motion"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>bsTrigger } from "@/components/ui/tabs"
import type { Product } from "@/lib/types"

interface ProductSpecificationsProps {
  product: Product
}

export default function ProductSpecifications({ product }: ProductSpecificationsProps) {

  const getMetafieldValue = (key: string) => {
    if (!product.metafields || !Array.isArray(product.metafields)) {
      return ''
    }
    return product.metafields.find(field => field && field.key === key)?.value || ''
  }

  return (
    <div>
      <h2 className="text-2xl font-medium mb-6">Product Specifications</h2>

      <Tabs defaultValue="technical" className="w-full">
        <TabsList className={`grid w-full ${getMetafieldValue("recommended_applications") ? "grid-cols-4" : "grid-cols-3"} mb-8`}>
          <TabsTrigger value="technical">Technical Data</TabsTrigger>
          {getMetafieldValue("recommended_applications") && (
            <TabsTrigger value="applications">Applications</TabsTrigger>
          )}
          <TabsTrigger value="safety">Safety & Handling</TabsTrigger>
          <TabsTrigger value="shipping">Shipping & Storage</TabsTrigger>
        </TabsList>

        <TabsContent value="technical" className="space-y-6">
          <motion.div initial={{ opacity: 0, y: 10 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.3 }}>
            <div className="bg-white border rounded-lg overflow-hidden">
              <table className="w-full">
                <tbody>
                  <tr className="border-b">
                    <th className="text-left py-3 px-4 bg-neutral-50 font-medium text-neutral-700 w-1/3">
                      Chemical Name
                    </th>
                    <td className="py-3 px-4">{product.title || ""}</td>
                  </tr>
                  <tr className="border-b">
                    <th className="text-left py-3 px-4 bg-neutral-50 font-medium text-neutral-700">CAS Number</th>
                    {getMetafieldValue("cas_number") && (
                      <td className="py-3 px-4">{getMetafieldValue("cas_number")}</td>
                    )}
                  </tr>
                  <tr className="border-b">
                    <th className="text-left py-3 px-4 bg-neutral-50 font-medium text-neutral-700">
                      Molecular Formula
                    </th>
                    {getMetafieldValue("molecular_formula") && (
                      <td className="py-3 px-4">{JSON.parse(getMetafieldValue('molecular_formula')).children[0].children[0].value || "N/A"}</td>
                    )}
                  </tr>
                  <tr className="border-b">
                    <th className="text-left py-3 px-4 bg-neutral-50 font-medium text-neutral-700">Molecular Weight</th>
                    {getMetafieldValue("molecular_weight") && (
                      <td className="py-3 px-4">{JSON.parse(getMetafieldValue('molecular_weight')).value} g/mol</td>
                    )}
                  </tr>
                  <tr className="border-b">
                    <th className="text-left py-3 px-4 bg-neutral-50 font-medium text-neutral-700">Appearance</th>
                    {getMetafieldValue("appearance") && (
                      <td className="py-3 px-4">
                      {getMetafieldValue("appearance")}
                    </td>
                    )}
                  </tr>
                  <tr className="border-b">
                    <th className="text-left py-3 px-4 bg-neutral-50 font-medium text-neutral-700">Purity</th>
                   {getMetafieldValue("purity") && (
                     <td className="py-3 px-4">{getMetafieldValue("purity")}%</td>
                   )}
                  </tr>
                  <tr className="border-b">
                    <th className="text-left py-3 px-4 bg-neutral-50 font-medium text-neutral-700">Solubility</th>
                   {getMetafieldValue("solubility") && (
                     <td className="py-3 px-4">{getMetafieldValue("solubility")}</td>
                   )}
                  </tr>
                  <tr>
                    <th className="text-left py-3 px-4 bg-neutral-50 font-medium text-neutral-700">pH Value</th>
                    {getMetafieldValue("ph_value") && (
                      <td className="py-3 px-4">{getMetafieldValue("ph_value")}</td>
                    )}
                  </tr>
                </tbody>
              </table>
            </div>
          </motion.div>
        </TabsContent>

        <TabsContent value="applications" className="space-y-6">
          <motion.div initial={{ opacity: 0, y: 10 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.3 }}>
            <div className="bg-white border rounded-lg p-6">
              <h3 className="text-lg font-medium mb-4">Recommended Applications</h3>
              {getMetafieldValue("recommended_applications") && (
                <ul className="space-y-4">
                {JSON.parse(getMetafieldValue("recommended_applications")).children.filter((app: { level: number }) => app.level === 3).map((application:any, index:any) => (
                  <li key={index} className="flex items-start">
                    <span className="h-5 w-5 rounded-full bg-teal-100 text-teal-600 flex items-center justify-center text-xs font-medium mr-3 mt-0.5">
                      {index + 1}
                    </span>
                    <div>
                      <p className="font-medium">{application.children[0].value}</p>
                      <p className="text-neutral-600 text-sm mt-1">Provides excellent results with standard processing parameters.</p>
                    </div>
                  </li>
                ))}
              </ul>
              )}
            </div>
          </motion.div>
        </TabsContent>

        <TabsContent value="safety" className="space-y-6">
          <motion.div initial={{ opacity: 0, y: 10 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.3 }}>
            <div className="bg-white border rounded-lg p-6">
              <h3 className="text-lg font-medium mb-4">Safety Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium mb-2">Handling Precautions</h4>
                  <ul className="space-y-2 text-neutral-700">
                    <li>• Wear appropriate protective equipment</li>
                    <li>• Use in well-ventilated areas</li>
                    <li>• Avoid contact with skin and eyes</li>
                    <li>• Wash hands thoroughly after handling</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Storage Recommendations</h4>
                  <ul className="space-y-2 text-neutral-700">
                    <li>• Store in a cool, dry place</li>
                    <li>• Keep container tightly closed</li>
                    <li>• Protect from direct sunlight</li>
                    <li>• Keep away from incompatible materials</li>
                  </ul>
                </div>
              </div>

              <div className="mt-6">
                <h4 className="font-medium mb-2">First Aid Measures</h4>
                <div className="bg-neutral-50 rounded-md p-4 text-neutral-700">
                  <p className="mb-2">
                    <strong>Eye Contact:</strong> Rinse cautiously with water for several minutes. Remove contact lenses
                    if present and easy to do. Continue rinsing.
                  </p>
                  <p className="mb-2">
                    <strong>Skin Contact:</strong> Wash with plenty of soap and water. If skin irritation occurs, get
                    medical advice/attention.
                  </p>
                  <p className="mb-2">
                    <strong>Inhalation:</strong> Remove person to fresh air and keep comfortable for breathing.
                  </p>
                  <p>
                    <strong>Ingestion:</strong> Rinse mouth. Do NOT induce vomiting. Seek immediate medical attention.
                  </p>
                </div>
              </div>
            </div>
          </motion.div>
        </TabsContent>

        <TabsContent value="shipping" className="space-y-6">
          <motion.div initial={{ opacity: 0, y: 10 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.3 }}>
            <div className="bg-white border rounded-lg p-6">
              <h3 className="text-lg font-medium mb-4">Shipping & Storage Information</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div className="border rounded-md p-4">
                  <h4 className="font-medium mb-2">Packaging Options</h4>
                  <ul className="space-y-2 text-neutral-700">
                    <li>• (Standard)</li>
                    <li>• Bulk packaging (100kg+)</li>
                    <li>• Industrial packaging (1000kg+)</li>
                    <li>• Custom packaging available upon request</li>
                  </ul>
                </div>
                <div className="border rounded-md p-4">
                  <h4 className="font-medium mb-2">Shipping Classification</h4>
                  <ul className="space-y-2 text-neutral-700">
                    <li>• Hazard Class: {"Non-hazardous"}</li>
                  </ul>
                </div>
              </div>

              <div>
                <h4 className="font-medium mb-2">Storage Conditions</h4>
                <div className="bg-neutral-50 rounded-md p-4 text-neutral-700">
                  <p className="mb-2">
                    <strong>Temperature:</strong> {"Store at room temperature (15-25°C)"}
                  </p>
                  <p className="mb-2">
                    <strong>Humidity:</strong>{" "}
                    {"Keep in a dry environment (<60% relative humidity)"}
                  </p>
                  <p className="mb-2">
                    <strong>Light Sensitivity:</strong>{" "}
                    Protect from direct sunlight and UV light
                  </p>
                </div>
              </div>
            </div>
          </motion.div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
