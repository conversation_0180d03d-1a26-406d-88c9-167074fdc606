// Shopify API configuration and utility functions

// Get API version from environment or use default
const API_VERSION = process.env.SHOPIFY_API_VERSION || "2025-04"

// Storefront API endpoint
export const SHOPIFY_STOREFRONT_URL = `https://${process.env.NEXT_PUBLIC_SHOPIFY_STORE_DOMAIN}/api/${API_VERSION}/graphql.json`

// Admin API endpoint
export const SHOPIFY_ADMIN_URL = `https://${process.env.SHOPIFY_STORE_DOMAIN}/admin/api/${API_VERSION}/graphql.json`

// Get customer access token from localStorage
function getCustomerAccessToken(): string | null {
  if (typeof window === "undefined") return null
  try {
    // First try the standard lowercase key
    let token = localStorage.getItem("shopify_customer_access_token")

    // If not found, try the legacy uppercase key
    if (!token) {
      token = localStorage.getItem("SHOPIFY_CUSTOMER_ACCESS_TOKEN")

      // If found in legacy format, migrate it to the new format
      if (token) {
        console.log("🔄 [shopify.ts] Migrating token from legacy uppercase key")
        localStorage.setItem("shopify_customer_access_token", token)

        // Try to get expiration from legacy format
        const legacyExpires = localStorage.getItem("SHOPIFY_CUSTOMER_ACCESS_TOKEN_EXPIRES")
        if (legacyExpires) {
          localStorage.setItem("shopify_customer_access_token_expires", legacyExpires)
          localStorage.removeItem("SHOPIFY_CUSTOMER_ACCESS_TOKEN_EXPIRES")
        }

        // Clean up legacy key
        localStorage.removeItem("SHOPIFY_CUSTOMER_ACCESS_TOKEN")
        console.log("✅ [shopify.ts] Token migration completed")
      }
    }

    return token
  } catch (e) {
    console.error("Failed to access localStorage for customer token:", e)
    return null
  }
}

// Storefront API fetch function with customer authentication support
export async function storefrontFetch<T>({
  query,
  variables = {},
  includeCustomerToken = false,
}: {
  query: string
  variables?: Record<string, any>
  includeCustomerToken?: boolean
}): Promise<{ data: T; errors?: any }> {
  try {
    if (!process.env.NEXT_PUBLIC_SHOPIFY_STORE_DOMAIN || !process.env.NEXT_PUBLIC_SHOPIFY_STOREFRONT_ACCESS_TOKEN) {
      console.error("Missing Shopify environment variables for Storefront API")
      throw new Error("Missing Shopify environment variables")
    }



    // Build headers
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
      "X-Shopify-Storefront-Access-Token": process.env.NEXT_PUBLIC_SHOPIFY_STOREFRONT_ACCESS_TOKEN,
    }

    // Add customer access token if requested and available
    if (includeCustomerToken) {
      const customerToken = getCustomerAccessToken()
      if (customerToken) {
        headers["X-Shopify-Customer-Access-Token"] = customerToken
      }
    }

    const response = await fetch(SHOPIFY_STOREFRONT_URL, {
      method: "POST",
      headers,
      body: JSON.stringify({ query, variables }),
      cache: "no-store",
    })

    if (!response.ok) {
      throw new Error(`Storefront API error: ${response.statusText}`)
    }

    const json = await response.json()

    return json
  } catch (error) {
    // Return empty data object instead of throwing
    return { data: {} as T }
  }
}

// Admin API fetch function
export async function adminFetch<T>({
  query,
  variables = {},
}: {
  query: string
  variables?: Record<string, any>
}): Promise<{ data: T | null; errors?: any }> {
  try {
    if (!process.env.SHOPIFY_STORE_DOMAIN || !process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN) {
      console.error("Missing Shopify environment variables for Admin API")
      return {
        data: null,
        errors: [{ message: "Missing Shopify environment variables for Admin API" }]
      }
    }

    console.log("Fetching from Admin API:", SHOPIFY_ADMIN_URL)
    console.log("With variables:", JSON.stringify(variables, null, 2))

    const response = await fetch(SHOPIFY_ADMIN_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN,
      },
      body: JSON.stringify({ query, variables }),
      cache: "no-store",
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error(`Admin API HTTP error: ${response.status} ${response.statusText}`)
      console.error("Response body:", errorText)
      return {
        data: null,
        errors: [{ message: `Admin API HTTP error: ${response.status} ${response.statusText}` }]
      }
    }

    const json = await response.json()

    if (json.errors) {
      console.error("Admin API GraphQL Errors:", JSON.stringify(json.errors, null, 2))
    }

    return {
      data: json.data || null,
      errors: json.errors || null
    }
  } catch (error) {
    console.error("Admin API fetch error:", error)
    return {
      data: null,
      errors: [{ message: error instanceof Error ? error.message : "Unknown error in Admin API fetch" }]
    }
  }
}

// Simple test function to verify API connection
export async function testShopifyConnection() {
  try {
    const { data, errors } = await storefrontFetch<any>({
      query: `
        query {
          shop {
            name
            primaryDomain {
              url
            }
          }
        }
      `,
    })

    if (errors) {
      console.error("Shopify connection test failed:", errors)
      return { success: false, errors }
    }

    if (data?.shop) {
      console.log("Shopify connection successful:", data.shop.name)
      return { success: true, shop: data.shop }
    } else {
      console.error("Shopify connection test failed: No shop data returned")
      return { success: false, error: "No shop data returned" }
    }
  } catch (error) {
    console.error("Shopify connection test failed:", error)
    return { success: false, error }
  }
}
