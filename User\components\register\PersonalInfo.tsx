"use client";

import React from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { CountryCodeSelect } from "@/components/ui/country-code-select";
import { AlertCircle, CheckCircle2 } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface PersonalInfoProps {
  formData: {
    firstName: string;
    lastName: string;
    email: string;
    password: string;
    phone: string;
    countryCode: string;
  };
  handleChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleCountryCodeChange: (value: string) => void;
  fieldErrors: Record<string, string | null>;
  isCheckingEmail: boolean;
  isCheckingPhone: boolean;
  // isEmailVerified: boolean; // Removed as error display is sufficient
  // isPhoneVerified: boolean; // Removed as error display is sufficient
}

const PersonalInfo: React.FC<PersonalInfoProps> = ({
  formData,
  handleChange,
  handleCountry<PERSON>ode<PERSON>hange,
  fieldErrors,
  isCheckingEmail,
  isChecking<PERSON>hone,
}) => {
  return (
    <div className="space-y-5">
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="firstName" className="text-sm font-medium">First Name</Label>
          <Input
            id="firstName"
            name="firstName"
            value={formData.firstName}
            onChange={handleChange}
            placeholder="Enter your first name"
            required
            className={fieldErrors.firstName ? "border-red-500" : "border-neutral-200 focus:border-teal-500 focus:ring-teal-500/20"}
          />
          {fieldErrors.firstName && (
            <p className="text-red-500 text-xs mt-1">{fieldErrors.firstName}</p>
          )}
        </div>
        <div>
          <Label htmlFor="lastName" className="text-sm font-medium">Last Name</Label>
          <Input
            id="lastName"
            name="lastName"
            value={formData.lastName}
            onChange={handleChange}
            placeholder="Enter your last name"
            required
            className={fieldErrors.lastName ? "border-red-500" : "border-neutral-200 focus:border-teal-500 focus:ring-teal-500/20"}
          />
          {fieldErrors.lastName && (
            <p className="text-red-500 text-xs mt-1">{fieldErrors.lastName}</p>
          )}
        </div>
      </div>

      <div>
        <Label htmlFor="email" className="text-sm font-medium">Email Address</Label>
        <div className="relative">
          <Input
            id="email"
            name="email"
            type="email"
            value={formData.email}
            onChange={handleChange}
            placeholder="Enter your email address"
            required
            className={`${fieldErrors.email ? "border-red-500" : "border-neutral-200 focus:border-teal-500 focus:ring-teal-500/20"} pr-10`}
          />
          {isCheckingEmail && (
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
              <svg
                className="animate-spin h-4 w-4 text-gray-500"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
            </div>
          )}
          {!isCheckingEmail && !fieldErrors.email && formData.email && (
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
              <CheckCircle2 className="h-4 w-4 text-teal-600" />
            </div>
          )}
        </div>
        {fieldErrors.email && (
          <p className="text-red-500 text-xs mt-1">{fieldErrors.email}</p>
        )}
      </div>

      <div>
        <Label htmlFor="password" className="text-sm font-medium">Password</Label>
        <Input
          id="password"
          name="password"
          type="password"
          value={formData.password}
          onChange={handleChange}
          placeholder="Create a password"
          required
          className={fieldErrors.password ? "border-red-500" : "border-neutral-200 focus:border-teal-500 focus:ring-teal-500/20"}
        />
        {fieldErrors.password && (
          <p className="text-red-500 text-xs mt-1">{fieldErrors.password}</p>
        )}
        {!fieldErrors.password && formData.password && (
          <Alert variant="info" className="mt-2 text-xs p-2 bg-teal-50 text-teal-700 border-teal-100">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Password must be at least 8 characters long and include letters,
              numbers, and special characters (@$!%*#?&).
            </AlertDescription>
          </Alert>
        )}
      </div>

      <div>
        <Label htmlFor="phone" className="text-sm font-medium">Phone Number</Label>
        <div className="flex items-center gap-2">
          <CountryCodeSelect
            value={formData.countryCode}
            onValueChange={handleCountryCodeChange}
          />
          <div className="relative flex-grow">
            <Input
              id="phone"
              name="phone"
              type="tel"
              value={formData.phone}
              onChange={handleChange}
              placeholder="Enter your phone number"
              required
              className={`${fieldErrors.phone ? "border-red-500" : "border-neutral-200 focus:border-teal-500 focus:ring-teal-500/20"} pr-10`}
            />
            {isCheckingPhone && (
              <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                <svg
                  className="animate-spin h-4 w-4 text-gray-500"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
              </div>
            )}
            {!isCheckingPhone && !fieldErrors.phone && formData.phone && (
              <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                <CheckCircle2 className="h-4 w-4 text-teal-600" />
              </div>
            )}
          </div>
        </div>
        {fieldErrors.phone && (
          <p className="text-red-500 text-xs mt-1">{fieldErrors.phone}</p>
        )}
      </div>
      <div
        id="clerk-captcha"
        data-cl-theme="dark"
        data-cl-size="flexible"
        className="mt-4"
      />
    </div>
  );
};

export default PersonalInfo;
