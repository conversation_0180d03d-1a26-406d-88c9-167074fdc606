"use client";

import React from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { BadgeCheck, AlertCircle, CheckCircle2 } from "lucide-react";

interface GSTSectionProps {
  gstNumber: string;
  handleChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleGstVerification: (gst: string) => Promise<void>;
  isGstVerified: boolean;
  isVerifyingGst: boolean;
  gstApiError: string | null;
  fieldErrors: Record<string, string | null>;
}

const GSTSection: React.FC<GSTSectionProps> = ({
  gstNumber,
  handleChange,
  handleGstVerification,
  isGstVerified,
  isVerifyingGst,
  gstApiError,
  fieldErrors,
}) => {
  const handleVerifyClick = () => {
    if (gstNumber && !fieldErrors.gstNumber) {
      handleGstVerification(gstNumber);
    }
  };

  return (
    <div className="space-y-2">
      <Label htmlFor="gstNumber" className="text-sm font-medium">GST Number</Label>
      <div className="flex items-center gap-2">
        <Input
          id="gstNumber"
          name="gstNumber"
          value={gstNumber}
          onChange={handleChange}
          placeholder="Enter your 15-digit GST number"
          required
          className={`${fieldErrors.gstNumber || gstApiError ? "border-red-500" : "border-neutral-200 focus:border-teal-500 focus:ring-teal-500/20"} ${isGstVerified ? "bg-neutral-50" : ""}`}
          readOnly={isGstVerified} // Make read-only if verified
          maxLength={15}
        />
        <Button
          type="button"
          onClick={handleVerifyClick}
          disabled={isVerifyingGst || isGstVerified || !gstNumber || !!fieldErrors.gstNumber}
          size="sm"
          variant={isGstVerified ? "ghost" : "outline"}
          className={isGstVerified ? "cursor-default text-teal-600 hover:bg-transparent" : "border-teal-200 text-teal-700 hover:bg-teal-50"}
        >
          {isVerifyingGst ? (
            <svg className="animate-spin h-4 w-4 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          ) : isGstVerified ? (
            <BadgeCheck className="h-4 w-4 mr-1 text-teal-600" />
          ) : null}
          {isGstVerified ? "Verified" : isVerifyingGst ? "Verifying..." : "Verify GST"}
        </Button>
      </div>
      {fieldErrors.gstNumber && !gstApiError && (
        <p className="text-red-500 text-xs mt-1">{fieldErrors.gstNumber}</p>
      )}
      {gstApiError && (
        <Alert variant="destructive" className="mt-2 text-xs p-2 border-red-200">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{gstApiError}</AlertDescription>
        </Alert>
      )}
      {isGstVerified && (
        <Alert className="mt-2 text-xs p-2 bg-teal-50 text-teal-700 border border-teal-100">
          <AlertDescription>GST details verified and auto-filled.</AlertDescription>
        </Alert>
      )}
    </div>
  );
};

export default GSTSection;

