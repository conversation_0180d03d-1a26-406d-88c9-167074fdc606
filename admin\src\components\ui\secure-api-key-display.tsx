"use client"

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { <PERSON><PERSON>, Eye, EyeOff, Shield } from 'lucide-react';
import { toast } from 'sonner';
import { useReAuth } from '@/contexts/re-auth-context';

interface SecureApiKeyDisplayProps {
  apiKey: string;
  keyId: string;
  className?: string;
  showCopyButton?: boolean;
  showToggleButton?: boolean;
  placeholder?: string;
}

export function SecureApiKeyDisplay({
  apiKey,
  keyId,
  className = "",
  showCopyButton = true,
  showToggleButton = true,
  placeholder = "••••••••••••••••••••••••••••••••"
}: SecureApiKeyDisplayProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [isAuthenticating, setIsAuthenticating] = useState(false);
  const { requestReAuth, isReAuthValid } = useReAuth();

  const handleRevealKey = async () => {
    if (isVisible) {
      setIsVisible(false);
      return;
    }

    // Check if we need re-authentication
    if (!isReAuthValid()) {
      setIsAuthenticating(true);
      try {
        const success = await requestReAuth();
        if (success) {
          setIsVisible(true);
        } else {
          toast.error('Authentication required to view API key');
        }
      } catch (error) {
        toast.error('Authentication failed');
      } finally {
        setIsAuthenticating(false);
      }
    } else {
      setIsVisible(true);
    }
  };

  const handleCopyKey = async () => {
    // Check if we need re-authentication
    if (!isReAuthValid()) {
      setIsAuthenticating(true);
      try {
        const success = await requestReAuth();
        if (success) {
          await copyToClipboard();
        } else {
          toast.error('Authentication required to copy API key');
        }
      } catch (error) {
        toast.error('Authentication failed');
      } finally {
        setIsAuthenticating(false);
      }
    } else {
      await copyToClipboard();
    }
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(apiKey);
      toast.success('API key copied to clipboard');
    } catch (error) {
      toast.error('Failed to copy API key');
    }
  };

  const displayValue = isVisible ? apiKey : placeholder;

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <div className="flex-1 relative">
        <Input
          value={displayValue}
          readOnly
          className="font-mono text-sm"
          placeholder={placeholder}
        />
        {!isVisible && (
          <div className="absolute inset-y-0 right-2 flex items-center">
            <Shield className="h-4 w-4 text-muted-foreground" />
          </div>
        )}
      </div>
      
      <div className="flex gap-1">
        {showToggleButton && (
          <Button
            variant="outline"
            size="sm"
            onClick={handleRevealKey}
            disabled={isAuthenticating}
            title={isVisible ? "Hide API key" : "Show API key (requires authentication)"}
          >
            {isVisible ? (
              <EyeOff className="h-4 w-4" />
            ) : (
              <Eye className="h-4 w-4" />
            )}
          </Button>
        )}
        
        {showCopyButton && (
          <Button
            variant="outline"
            size="sm"
            onClick={handleCopyKey}
            disabled={isAuthenticating}
            title="Copy API key (requires authentication)"
          >
            <Copy className="h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  );
}

interface SecureApiKeyFieldProps {
  label: string;
  apiKey: string;
  keyId: string;
  description?: string;
  className?: string;
}

export function SecureApiKeyField({
  label,
  apiKey,
  keyId,
  description,
  className = ""
}: SecureApiKeyFieldProps) {
  return (
    <div className={`space-y-2 ${className}`}>
      <div className="flex items-center justify-between">
        <label className="text-sm font-medium">{label}</label>
        <div className="flex items-center gap-1 text-xs text-muted-foreground">
          <Shield className="h-3 w-3" />
          <span>Protected</span>
        </div>
      </div>
      
      <SecureApiKeyDisplay
        apiKey={apiKey}
        keyId={keyId}
      />
      
      {description && (
        <p className="text-xs text-muted-foreground">{description}</p>
      )}
    </div>
  );
}
