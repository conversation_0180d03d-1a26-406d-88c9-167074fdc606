"use client"

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, Eye, EyeOff, Shield } from 'lucide-react';
import { toast } from 'sonner';
import { useReAuth } from '@/contexts/re-auth-context';
import { useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';

interface SecureApiKeyDisplayProps {
  apiKey: string;
  keyId: string;
  apiKeyDocId?: string; // Convex document ID for fetching full key
  className?: string;
  showCopyButton?: boolean;
  showToggleButton?: boolean;
  placeholder?: string;
  allowImmediateAccess?: boolean; // For newly created keys
}

export function SecureApiKeyDisplay({
  apiKey,
  keyId,
  apiKeyDocId,
  className = "",
  showCopyButton = true,
  showToggleButton = true,
  placeholder = "••••••••••••••••••••••••••••••••",
  allowImmediateAccess = false
}: SecureApiKeyDisplayProps) {
  const [isVisible, setIsVisible] = useState(allowImmediateAccess);
  const [isAuthenticating, setIsAuthenticating] = useState(false);
  const { requestReAuth, isReAuthValid } = useReAuth();

  // Fetch full API key when needed (only when visible and we have a doc ID)
  const fullApiKeyData = useQuery(
    api.apiKeys?.getFullApiKeyById,
    isVisible && apiKeyDocId ? { id: apiKeyDocId as any } : "skip"
  );

  // Ref for textarea auto-resize
  const textareaRef = React.useRef<HTMLTextAreaElement>(null);

  const handleRevealKey = async () => {
    if (isVisible) {
      setIsVisible(false);
      return;
    }

    // If immediate access is allowed, show the key without re-auth
    if (allowImmediateAccess) {
      console.log('Immediate access allowed, showing key');
      setIsVisible(true);
      return;
    }

    // Check if we need re-authentication
    if (!isReAuthValid()) {
      setIsAuthenticating(true);
      try {
        console.log('Requesting re-auth for key reveal');
        const success = await requestReAuth();
        console.log('Re-auth result:', success); // Debug log
        if (success) {
          console.log('Re-auth successful, setting visible to true');
          setIsVisible(true);
          toast.success('API key revealed');
        } else {
          toast.error('Authentication required to view API key');
        }
      } catch (error) {
        console.error('Re-auth error:', error); // Debug log
        toast.error('Authentication failed');
      } finally {
        setIsAuthenticating(false);
      }
    } else {
      console.log('Re-auth still valid, setting visible to true');
      setIsVisible(true);
    }
  };

  const handleCopyKey = async () => {
    // If immediate access is allowed, copy without re-auth
    if (allowImmediateAccess) {
      console.log('Immediate access allowed, copying key');
      await copyToClipboard();
      return;
    }

    // Check if we need re-authentication
    if (!isReAuthValid()) {
      setIsAuthenticating(true);
      try {
        console.log('Requesting re-auth for key copy');
        const success = await requestReAuth();
        if (success) {
          console.log('Re-auth successful for copy, copying key');
          await copyToClipboard();
        } else {
          toast.error('Authentication required to copy API key');
        }
      } catch (error) {
        toast.error('Authentication failed');
      } finally {
        setIsAuthenticating(false);
      }
    } else {
      await copyToClipboard();
    }
  };

  const copyToClipboard = async () => {
    try {
      // Use the full API key if available, otherwise use the provided key
      const keyToCopy = fullApiKeyData?.key || apiKey;
      await navigator.clipboard.writeText(keyToCopy);
      toast.success('API key copied to clipboard');
    } catch (error) {
      toast.error('Failed to copy API key');
    }
  };

  // Use full API key if available and visible, otherwise use the provided (possibly masked) key
  const actualApiKey = isVisible && fullApiKeyData?.key ? fullApiKeyData.key : apiKey;
  const displayValue = isVisible ? actualApiKey : placeholder;

  // Auto-resize textarea when content changes
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = Math.max(40, textareaRef.current.scrollHeight) + 'px';
    }
  }, [displayValue]);

  // Debug logging
  console.log('SecureApiKeyDisplay:', {
    isVisible,
    allowImmediateAccess,
    hasFullKeyData: !!fullApiKeyData?.key,
    apiKeyLength: apiKey?.length,
    actualApiKeyLength: actualApiKey?.length,
    placeholderLength: placeholder?.length,
    displayValueLength: displayValue?.length,
    apiKeyStart: apiKey?.substring(0, 15),
    actualApiKeyStart: actualApiKey?.substring(0, 15),
    displayValueStart: displayValue?.substring(0, 15)
  });

  return (
    <div className={`flex items-start gap-2 ${className}`}>
      <div className="flex-1 relative min-w-0">
        <div className="relative">
          <textarea
            value={displayValue}
            readOnly
            rows={1}
            className="w-full resize-none border border-input bg-background px-3 py-2 text-xs font-mono ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 rounded-md pr-20 overflow-hidden"
            placeholder={placeholder}
            style={{
              minHeight: '40px',
              lineHeight: '1.4',
              wordBreak: 'break-all'
            }}
            onInput={(e) => {
              // Auto-resize textarea based on content
              const target = e.target as HTMLTextAreaElement;
              target.style.height = 'auto';
              target.style.height = Math.max(40, target.scrollHeight) + 'px';
            }}
          />
          {!isVisible && (
            <div className="absolute top-2 right-2 flex items-center">
              <Shield className="h-4 w-4 text-muted-foreground" />
            </div>
          )}
        </div>
      </div>

      <div className="flex gap-1 flex-shrink-0 mt-1">
        {showToggleButton && (
          <Button
            variant="outline"
            size="sm"
            onClick={handleRevealKey}
            disabled={isAuthenticating}
            title={isVisible ? "Hide API key" : "Show API key (requires authentication)"}
          >
            {isVisible ? (
              <EyeOff className="h-4 w-4" />
            ) : (
              <Eye className="h-4 w-4" />
            )}
          </Button>
        )}

        {showCopyButton && (
          <Button
            variant="outline"
            size="sm"
            onClick={handleCopyKey}
            disabled={isAuthenticating}
            title="Copy API key (requires authentication)"
          >
            <Copy className="h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  );
}

interface SecureApiKeyFieldProps {
  label: string;
  apiKey: string;
  keyId: string;
  apiKeyDocId?: string;
  description?: string;
  className?: string;
}

export function SecureApiKeyField({
  label,
  apiKey,
  keyId,
  apiKeyDocId,
  description,
  className = ""
}: SecureApiKeyFieldProps) {
  return (
    <div className={`space-y-2 ${className}`}>
      <div className="flex items-center justify-between">
        <label className="text-sm font-medium">{label}</label>
        <div className="flex items-center gap-1 text-xs text-muted-foreground">
          <Shield className="h-3 w-3" />
          <span>Protected</span>
        </div>
      </div>
      
      <SecureApiKeyDisplay
        apiKey={apiKey}
        keyId={keyId}
        apiKeyDocId={apiKeyDocId}
      />
      
      {description && (
        <p className="text-xs text-muted-foreground">{description}</p>
      )}
    </div>
  );
}
