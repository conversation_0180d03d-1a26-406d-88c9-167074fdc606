// Shopify Admin API utilities

// Environment variables for Shopify Admin API
const SHOPIFY_API_ACCESS_TOKEN = process.env.SHOPIFY_ADMIN_API_ACCESS_TOKEN;
const SHOPIFY_STORE_URL = process.env.SHOPIFY_STORE_DOMAIN;
const SHOPIFY_API_VERSION = process.env.SHOPIFY_API_VERSION || '2025-04'; // Use the version from env or fallback

// Function to make GraphQL requests to the Shopify Admin API
export async function adminFetch<T = any>({
  query,
  variables = {}
}: {
  query: string;
  variables?: Record<string, any>;
}): Promise<{ data: T | null; errors: any[] | null }> {
  try {
    // Log environment variables availability (not the values for security)
    console.log('Shopify Admin API environment variables check:', { 
      SHOPIFY_API_ACCESS_TOKEN: !!SHOPIFY_API_ACCESS_TOKEN,
      SHOPIFY_STORE_URL: SHOPIFY_STORE_URL,
      SHOPIFY_API_VERSION: SHOPIFY_API_VERSION
    });
    
    if (!SHOPIFY_API_ACCESS_TOKEN || !SHOPIFY_STORE_URL) {
      throw new Error('Shopify Admin API access token or store domain is not configured');
    }

    const response = await fetch(
      `https://${SHOPIFY_STORE_URL}/admin/api/${SHOPIFY_API_VERSION}/graphql.json`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Shopify-Access-Token': SHOPIFY_API_ACCESS_TOKEN,
        },
        body: JSON.stringify({
          query,
          variables
        })
      }
    );

    const result = await response.json();

    if (!response.ok) {
      console.error('Shopify Admin API error:', result);
      return { data: null, errors: [{ message: 'Shopify Admin API request failed' }] };
    }

    return {
      data: result.data || null,
      errors: result.errors || null
    };
  } catch (error) {
    console.error('Error in Shopify Admin API request:', error);
    return {
      data: null,
      errors: [{ message: error instanceof Error ? error.message : 'Unknown error' }]
    };
  }
}
