"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import SavedProducts from "@/components/saved-products" // Assuming this path is correct

// Props can be added here if this component becomes dynamic in the future
// interface SavedProductsTabProps {}

export default function SavedProductsTab(/*props: SavedProductsTabProps*/) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Saved Products</CardTitle>
        <CardDescription>
          Products you've saved for later
        </CardDescription>
      </CardHeader>
      <CardContent>
        <SavedProducts />
      </CardContent>
    </Card>
  )
}