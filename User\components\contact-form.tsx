"use client"

import type React from "react"

import { useState } from "react"
import { motion } from "framer-motion"
import { Check } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

export default function ContactForm() {
  const [isSubmitted, setIsSubmitted] = useState(false)

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // In a real app, you would submit the form data to an API
    setIsSubmitted(true)
  }

  return (
    <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5 }}>
      <h2 className="text-2xl font-medium mb-6">Send Us a Message</h2>

      {isSubmitted ? (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="bg-teal-50 border border-teal-200 rounded-lg p-6"
        >
          <div className="flex items-center mb-4">
            <div className="w-10 h-10 rounded-full bg-teal-100 flex items-center justify-center mr-4">
              <Check className="h-5 w-5 text-teal-600" />
            </div>
            <h3 className="text-lg font-medium text-teal-800">Message Sent Successfully</h3>
          </div>
          <p className="text-teal-700 mb-4">
            Thank you for contacting Benzochem Industries. Our team will review your message and get back to you within
            24 hours.
          </p>
          <Button onClick={() => setIsSubmitted(false)} variant="outline" className="text-teal-600 border-teal-200">
            Send Another Message
          </Button>
        </motion.div>
      ) : (
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <label htmlFor="firstName" className="text-sm font-medium">
                First Name
              </label>
              <Input id="firstName" required />
            </div>
            <div className="space-y-2">
              <label htmlFor="lastName" className="text-sm font-medium">
                Last Name
              </label>
              <Input id="lastName" required />
            </div>
          </div>

          <div className="space-y-2">
            <label htmlFor="email" className="text-sm font-medium">
              Email Address
            </label>
            <Input id="email" type="email" required />
          </div>

          <div className="space-y-2">
            <label htmlFor="company" className="text-sm font-medium">
              Company Name
            </label>
            <Input id="company" />
          </div>

          <div className="space-y-2">
            <label htmlFor="inquiryType" className="text-sm font-medium">
              Inquiry Type
            </label>
            <Select>
              <SelectTrigger>
                <SelectValue placeholder="Select inquiry type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="product">Product Information</SelectItem>
                <SelectItem value="quote">Request a Quote</SelectItem>
                <SelectItem value="technical">Technical Support</SelectItem>
                <SelectItem value="partnership">Partnership Opportunities</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label htmlFor="message" className="text-sm font-medium">
              Message
            </label>
            <Textarea id="message" rows={5} required />
          </div>

          <Button type="submit" className="w-full md:w-auto bg-teal-600 hover:bg-teal-700">
            Send Message
          </Button>
        </form>
      )}
    </motion.div>
  )
}
