import RegisterForm from "@/components/register-form"
import Link from "next/link"
import { ArrowRight } from "lucide-react"
import Image from "next/image"

export default function RegisterPageContent() {
  return (
    <main className="flex min-h-screen flex-col pt-16 bg-[#FAFAFA]">
      <section className="flex-1 flex items-stretch overflow-hidden">
        <div className="w-full max-w-6xl mx-auto my-8 rounded-2xl shadow-xl overflow-hidden flex bg-white">
          {/* Left side - Image and branding */}
          <div className="hidden md:block w-1/2 bg-gradient-to-br from-teal-600 to-teal-800 p-8 text-white relative overflow-hidden">
            <div className="absolute inset-0 opacity-50">
              <Image 
                src="/images/register.png" 
                alt="Chemistry laboratory" 
                fill 
                className="object-cover"
                priority
              />
            </div>
            
            <div className="relative z-10 h-full flex flex-col p-8">  
              <div className="mt-auto">
                <h3 className="text-3xl md:text-4xl font-light mb-4">Join Our Network<br />of Trusted Partners</h3>
                <p className="text-teal-100 opacity-80 max-w-md">
                  Create an account to access exclusive pricing, streamlined ordering, and dedicated support for your business.
                </p>
                
                <div className="mt-8 flex space-x-2">
                  <span className="w-2 h-2 rounded-full bg-white opacity-60"></span>
                  <span className="w-6 h-2 rounded-full bg-white"></span>
                  <span className="w-2 h-2 rounded-full bg-white opacity-60"></span>
                </div>
              </div>
            </div>
          </div>
          
          {/* Right side - Register form */}
          <div className="w-full md:w-1/2 p-6 md:p-12 flex flex-col">
            <div className="text-center md:text-left mb-8">
              <h2 className="text-2xl font-bold text-gray-800">Create an account</h2>
              <p className="text-gray-500 mt-1">Join Benzochem Industries</p>
            </div>
            
            <div className="flex-1">
              <RegisterForm />
            </div>
            
            <div className="mt-8 text-center">
              <p className="text-neutral-500 font-light">
                Already have an account?{" "}
                <Link 
                  href="/login" 
                  className="text-teal-600 hover:text-teal-700 font-normal inline-flex items-center transition-all duration-300 border-b border-transparent hover:border-teal-600"
                >
                  Sign in <ArrowRight className="h-3.5 w-3.5 ml-1 transition-transform group-hover:translate-x-0.5" />
                </Link>
              </p>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}
