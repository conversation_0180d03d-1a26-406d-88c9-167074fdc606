"use client"

import { motion } from "framer-motion"
import { cn } from "@/lib/utils"

export interface PageHeaderProps {
  title: string
  description?: string
  imageUrl?: string
  className?: string
}

export default function PageHeader({
  title,
  description,
  imageUrl = "https://images.unsplash.com/photo-1497366754035-f200968a6e72",
  className,
}: PageHeaderProps) {
  return (
    <div
      className="relative py-16 md:py-24 bg-cover bg-center"
      style={{
        backgroundImage: `url(${imageUrl})`,
        backgroundSize: "cover",
        backgroundPosition: "center",
      }}
    >
      {/* Dark overlay for better text readability */}
      <div className="absolute inset-0 bg-black/50"></div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className={cn("max-w-3xl", className)}
        >
          <h1 className="text-3xl md:text-4xl lg:text-5xl font-medium text-white mb-4">{title}</h1>
          {description && <p className="text-lg text-white/80">{description}</p>}
        </motion.div>
      </div>
    </div>
  )
}
