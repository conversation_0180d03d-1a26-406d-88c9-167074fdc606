import { NextRequest, NextResponse } from 'next/server';
import {
  setCustomerMetafields,
  getCustomerMetafields,
  getCustomerMetafieldValue,
  updateCustomerGSTInfo,
  updateCustomerBusinessPreferences,
  getCustomerGSTInfo
} from '@/actions/shopifyActions';

// API endpoint for testing metafield functionality
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, customerId, metafields, namespace, key, gstData, preferences } = body;

    console.log('🧪 Test Metafields API called with action:', action);

    switch (action) {
      case 'create':
        if (!customerId || !metafields) {
          return NextResponse.json(
            { success: false, error: 'customerId and metafields are required' },
            { status: 400 }
          );
        }

        const createResult = await setCustomerMetafields(customerId, metafields);
        return NextResponse.json(createResult);

      case 'get':
        if (!customerId) {
          return NextResponse.json(
            { success: false, error: 'customerId is required' },
            { status: 400 }
          );
        }

        const getResult = await getCustomerMetafields(customerId, namespace);
        return NextResponse.json(getResult);

      case 'getValue':
        if (!customerId || !namespace || !key) {
          return NextResponse.json(
            { success: false, error: 'customerId, namespace, and key are required' },
            { status: 400 }
          );
        }

        const valueResult = await getCustomerMetafieldValue(customerId, namespace, key);
        return NextResponse.json(valueResult);

      case 'updateGST':
        if (!customerId || !gstData) {
          return NextResponse.json(
            { success: false, error: 'customerId and gstData are required' },
            { status: 400 }
          );
        }

        const gstResult = await updateCustomerGSTInfo(customerId, gstData);
        return NextResponse.json(gstResult);

      case 'getGST':
        if (!customerId) {
          return NextResponse.json(
            { success: false, error: 'customerId is required' },
            { status: 400 }
          );
        }

        const getGstResult = await getCustomerGSTInfo(customerId);
        return NextResponse.json(getGstResult);

      case 'updatePreferences':
        if (!customerId || !preferences) {
          return NextResponse.json(
            { success: false, error: 'customerId and preferences are required' },
            { status: 400 }
          );
        }

        const prefsResult = await updateCustomerBusinessPreferences(customerId, preferences);
        return NextResponse.json(prefsResult);

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action. Supported actions: create, get, getValue, updateGST, getGST, updatePreferences' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Test Metafields API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET endpoint for basic information
export async function GET() {
  return NextResponse.json({
    message: 'Shopify Metafields Test API',
    availableActions: [
      'create - Create/update metafields',
      'get - Get customer metafields',
      'getValue - Get specific metafield value',
      'updateGST - Update GST information',
      'getGST - Get GST information',
      'updatePreferences - Update business preferences'
    ],
    usage: {
      create: {
        method: 'POST',
        body: {
          action: 'create',
          customerId: 'gid://shopify/Customer/123',
          metafields: [
            {
              namespace: 'custom',
              key: 'business_type',
              value: 'Manufacturing',
              type: 'single_line_text_field'
            }
          ]
        }
      },
      get: {
        method: 'POST',
        body: {
          action: 'get',
          customerId: 'gid://shopify/Customer/123',
          namespace: 'custom' // optional
        }
      },
      getValue: {
        method: 'POST',
        body: {
          action: 'getValue',
          customerId: 'gid://shopify/Customer/123',
          namespace: 'custom',
          key: 'business_type'
        }
      },
      updateGST: {
        method: 'POST',
        body: {
          action: 'updateGST',
          customerId: 'gid://shopify/Customer/123',
          gstData: {
            gstNumber: '27AABCU9603R1ZM',
            legalNameOfBusiness: 'Company Name',
            tradeName: 'Trade Name'
          }
        }
      },
      getGST: {
        method: 'POST',
        body: {
          action: 'getGST',
          customerId: 'gid://shopify/Customer/123'
        }
      },
      updatePreferences: {
        method: 'POST',
        body: {
          action: 'updatePreferences',
          customerId: 'gid://shopify/Customer/123',
          preferences: {
            preferredPaymentMethod: 'Net 30',
            creditLimit: 100000,
            businessCategory: 'Manufacturing'
          }
        }
      }
    }
  });
}
