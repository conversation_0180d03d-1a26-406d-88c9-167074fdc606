"use client";

import { useState } from "react";
import { ProtectedRoute } from "@/components/auth/protected-route";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Dialog<PERSON>rigger,
} from "@/components/ui/dialog";
import {
  ConfirmDialog,
} from "@/components/ui/enhanced-dialog";
import { SecureApiKeyDisplay, SecureApiKeyField } from "@/components/ui/secure-api-key-display";
import {
  Search,
  MoreHorizontal,
  Eye,
  Key,
  Plus,
  Copy,
  Trash2,
  Shield,
  Activity,
  Calendar,
  RefreshCw,
  Download,
  AlertTriangle,
  Loader2
} from "lucide-react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../../../convex/_generated/api";
import { useAuth } from "@/contexts/auth-context";
import { toast } from "sonner";

export default function APIKeysPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [currentPage, setCurrentPage] = useState(0);
  const [selectedApiKey, setSelectedApiKey] = useState<any>(null);
  const [showDetailsDialog, setShowDetailsDialog] = useState(false);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [newKeyName, setNewKeyName] = useState("");
  const [newKeyPermissions, setNewKeyPermissions] = useState<string[]>([]);
  const [showRevokeConfirm, setShowRevokeConfirm] = useState(false);
  const [apiKeyToRevoke, setApiKeyToRevoke] = useState<string | null>(null);
  const [isRevoking, setIsRevoking] = useState(false);
  const [isCreatingKey, setIsCreatingKey] = useState(false);
  const [apiKeyToDelete, setApiKeyToDelete] = useState<string | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [recentlyCreatedKeys, setRecentlyCreatedKeys] = useState<Set<string>>(new Set());
  const pageSize = 20;

  const { admin } = useAuth();

  // Queries
  const apiKeys = useQuery(api.apiKeys?.getApiKeys, {
    search: searchTerm || undefined,
    isActive: statusFilter === "all" ? undefined : statusFilter === "active",
    limit: pageSize,
    offset: currentPage * pageSize,
  });

  const apiKeyStats = useQuery(api.apiKeys?.getApiKeyStats);

  // Mutations
  const createApiKey = useMutation(api.apiKeys?.createApiKey);
  const revokeApiKey = useMutation(api.apiKeys?.revokeApiKey);
  const deleteApiKey = useMutation(api.apiKeys?.deleteApiKey);
  const getOrCreateDemoAdmin = useMutation(api.admins.getOrCreateDemoAdmin);

  const handleCreateApiKey = async () => {
    if (!admin || !newKeyName.trim()) return;

    setIsCreatingKey(true);
    try {
      const adminId = await getOrCreateDemoAdmin({ email: admin.email });
      const result = await createApiKey({
        name: newKeyName,
        permissions: newKeyPermissions,
        adminId: adminId,
      });

      // Track this key as recently created for immediate access
      if (result?.id) {
        setRecentlyCreatedKeys(prev => new Set(prev).add(result.id));
        // Remove from recently created after 5 minutes
        setTimeout(() => {
          setRecentlyCreatedKeys(prev => {
            const newSet = new Set(prev);
            newSet.delete(result.id);
            return newSet;
          });
        }, 5 * 60 * 1000);
      }

      toast.success("API key created successfully");
      setShowCreateDialog(false);
      setNewKeyName("");
      setNewKeyPermissions([]);
    } catch (error) {
      toast.error("Failed to create API key");
      console.error(error);
    } finally {
      setIsCreatingKey(false);
    }
  };

  const handleRevokeApiKey = async (apiKeyId: string) => {
    setApiKeyToRevoke(apiKeyId);
    setShowRevokeConfirm(true);
  };

  const confirmRevokeApiKey = async () => {
    if (!admin || !apiKeyToRevoke) return;

    setIsRevoking(true);
    try {
      // Get or create admin ID first
      const adminId = await getOrCreateDemoAdmin({ email: admin.email });

      await revokeApiKey({
        apiKeyId: apiKeyToRevoke as any,
        revokedBy: adminId,
        reason: "Revoked via admin dashboard"
      });
      toast.success("API key revoked successfully");
      setShowRevokeConfirm(false);
      setApiKeyToRevoke(null);
      if (selectedApiKey?._id === apiKeyToRevoke) {
        setShowDetailsDialog(false);
      }
    } catch (error) {
      toast.error("Failed to revoke API key");
      console.error(error);
    } finally {
      setIsRevoking(false);
    }
  };

  const handleDeleteApiKey = async (apiKeyId: string) => {
    setApiKeyToDelete(apiKeyId);
    setShowDeleteConfirm(true);
  };

  const confirmDeleteApiKey = async () => {
    if (!admin || !apiKeyToDelete) return;

    setIsDeleting(true);
    try {
      // Get or create admin ID first
      const adminId = await getOrCreateDemoAdmin({ email: admin.email });

      await deleteApiKey({
        apiKeyId: apiKeyToDelete as any,
        deletedBy: adminId,
        reason: "Permanently deleted via admin dashboard"
      });
      toast.success("API key permanently deleted");
      setShowDeleteConfirm(false);
      setApiKeyToDelete(null);
      if (selectedApiKey?._id === apiKeyToDelete) {
        setShowDetailsDialog(false);
      }
    } catch (error) {
      toast.error("Failed to delete API key");
      console.error(error);
    } finally {
      setIsDeleting(false);
    }
  };



  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const openDetailsDialog = (apiKey: any) => {
    setSelectedApiKey(apiKey);
    setShowDetailsDialog(true);
  };



  const availablePermissions = [
    "products.read", "products.write", "products.delete",
    "users.read", "users.write", "users.approve",
    "orders.read", "orders.write", "orders.fulfill",
    "analytics.read", "reports.read", "settings.read"
  ];

  return (
    <ProtectedRoute requiredPermission="api_keys.read">
      <DashboardLayout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">API Keys</h1>
              <p className="text-muted-foreground">
                Manage API keys for external integrations and access control
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export Usage
              </Button>
              <Button variant="outline" size="sm">
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
              <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
                <DialogTrigger asChild>
                  <Button size="sm">
                    <Plus className="h-4 w-4 mr-2" />
                    Create API Key
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle className="flex items-center gap-2">
                      {isCreatingKey && <Loader2 className="h-4 w-4 animate-spin" />}
                      Create New API Key
                    </DialogTitle>
                    <DialogDescription>
                      Generate a new API key with specific permissions
                    </DialogDescription>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">API Key Name</Label>
                      <Input
                        id="name"
                        placeholder="e.g., Production API, Mobile App"
                        value={newKeyName}
                        onChange={(e) => setNewKeyName(e.target.value)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>Permissions</Label>
                      <div className="grid grid-cols-2 gap-2 max-h-40 overflow-y-auto">
                        {availablePermissions.map((permission) => (
                          <div key={permission} className="flex items-center space-x-2">
                            <input
                              type="checkbox"
                              id={permission}
                              checked={newKeyPermissions.includes(permission)}
                              onChange={(e) => {
                                if (e.target.checked) {
                                  setNewKeyPermissions([...newKeyPermissions, permission]);
                                } else {
                                  setNewKeyPermissions(newKeyPermissions.filter(p => p !== permission));
                                }
                              }}
                            />
                            <Label htmlFor={permission} className="text-sm">
                              {permission}
                            </Label>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                  <DialogFooter>
                    <Button
                      variant="outline"
                      onClick={() => setShowCreateDialog(false)}
                      disabled={isCreatingKey}
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={handleCreateApiKey}
                      disabled={!newKeyName.trim() || isCreatingKey}
                    >
                      {isCreatingKey ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Creating...
                        </>
                      ) : (
                        <>
                          <Key className="h-4 w-4 mr-2" />
                          Create API Key
                        </>
                      )}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </div>

          {/* Stats Cards */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total API Keys</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{apiKeys?.length || 0}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Active Keys</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  {apiKeys?.filter(key => key.isActive).length || 0}
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total Requests</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">
                  {apiKeys?.reduce((sum, key) => sum + (key.usageCount || 0), 0).toLocaleString() || '0'}
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Expired Keys</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">
                  {apiKeys?.filter(key => key.expiresAt && key.expiresAt < Date.now()).length || 0}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* API Keys Table */}
          <Card>
            <CardHeader>
              <CardTitle>API Keys</CardTitle>
              <CardDescription>
                Manage API keys and monitor their usage
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-4 mb-6">
                <div className="relative flex-1 max-w-sm">
                  <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                  <Input
                    placeholder="Search API keys..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-[140px]">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Keys</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>API Key</TableHead>
                      <TableHead>Permissions</TableHead>
                      <TableHead>Usage</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Last Used</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {(apiKeys || [])
                      .filter(key =>
                        !searchTerm ||
                        key.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                        key.key.toLowerCase().includes(searchTerm.toLowerCase())
                      )
                      .filter(key => statusFilter === "all" ||
                        (statusFilter === "active" && key.isActive) ||
                        (statusFilter === "inactive" && !key.isActive)
                      )
                      .map((apiKey) => (
                      <TableRow key={apiKey._id}>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Key className="h-4 w-4 text-muted-foreground" />
                            <div>
                              <div className="font-medium">{apiKey.name}</div>
                              <div className="text-sm text-muted-foreground">
                                Created {formatDate(apiKey.createdAt)}
                              </div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="min-w-0 max-w-md">
                          <SecureApiKeyDisplay
                            apiKey={apiKey.key}
                            keyId={apiKey.keyId}
                            apiKeyDocId={apiKey._id}
                            className="w-full"
                            placeholder="••••••••••••••••••••••••••••••••"
                            allowImmediateAccess={recentlyCreatedKeys.has(apiKey._id)}
                          />
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-wrap gap-1">
                            {apiKey.permissions.slice(0, 2).map((permission, index) => (
                              <Badge key={index} variant="secondary" className="text-xs">
                                {permission}
                              </Badge>
                            ))}
                            {apiKey.permissions.length > 2 && (
                              <Badge variant="outline" className="text-xs">
                                +{apiKey.permissions.length - 2}
                              </Badge>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            <div className="font-medium">{apiKey.usageCount.toLocaleString()}</div>
                            <div className="text-muted-foreground">requests</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {apiKey.isActive ? (
                              <Badge variant="default" className="bg-green-500">Active</Badge>
                            ) : (
                              <Badge variant="secondary">Inactive</Badge>
                            )}
                            {apiKey.expiresAt && apiKey.expiresAt < Date.now() && (
                              <Badge variant="destructive" className="text-xs">
                                <AlertTriangle className="h-3 w-3 mr-1" />
                                Expired
                              </Badge>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            {apiKey.lastUsedAt ? formatDate(apiKey.lastUsedAt) : 'Never'}
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => openDetailsDialog(apiKey)}>
                                <Eye className="mr-2 h-4 w-4" />
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                className="text-orange-600"
                                onClick={() => handleRevokeApiKey(apiKey._id)}
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                Revoke Key
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                className="text-red-600"
                                onClick={() => handleDeleteApiKey(apiKey._id)}
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete Permanently
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {/* Pagination */}
              <div className="flex items-center justify-between mt-4">
                <div className="text-sm text-muted-foreground">
                  Showing {(apiKeys || []).filter(key =>
                    (!searchTerm ||
                     key.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                     key.key.toLowerCase().includes(searchTerm.toLowerCase())) &&
                    (statusFilter === "all" ||
                     (statusFilter === "active" && key.isActive) ||
                     (statusFilter === "inactive" && !key.isActive))
                  ).length} API keys
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(Math.max(0, currentPage - 1))}
                    disabled={currentPage === 0}
                  >
                    Previous
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(currentPage + 1)}
                    disabled={(apiKeys?.length || 0) < pageSize}
                  >
                    Next
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* API Key Details Dialog */}
          <Dialog open={showDetailsDialog} onOpenChange={setShowDetailsDialog}>
            <DialogContent className="max-w-3xl">
              <DialogHeader>
                <DialogTitle className="flex items-center gap-2">
                  <Key className="h-5 w-5" />
                  API Key Details
                </DialogTitle>
                <DialogDescription>
                  Complete information for {selectedApiKey?.name}
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="flex items-center gap-2">
                  {selectedApiKey?.isActive ? (
                    <Badge variant="default" className="bg-green-500">Active</Badge>
                  ) : (
                    <Badge variant="secondary">Inactive</Badge>
                  )}
                  {selectedApiKey?.expiresAt && selectedApiKey?.expiresAt < Date.now() && (
                    <Badge variant="destructive">
                      <AlertTriangle className="h-3 w-3 mr-1" />
                      Expired
                    </Badge>
                  )}
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-medium mb-2">API Key Information</h4>
                    <div className="space-y-2 text-sm">
                      <div>Name: {selectedApiKey?.name}</div>
                      <div>Created: {selectedApiKey?.createdAt ? formatDate(selectedApiKey?.createdAt) : 'N/A'}</div>
                      <div>Last Used: {selectedApiKey?.lastUsedAt ? formatDate(selectedApiKey?.lastUsedAt) : 'Never'}</div>
                      <div>Usage Count: {selectedApiKey?.usageCount?.toLocaleString() || 0}</div>
                      {selectedApiKey?.expiresAt && (
                        <div>Expires: {formatDate(selectedApiKey?.expiresAt)}</div>
                      )}
                    </div>
                  </div>
                  <div>
                    <h4 className="font-medium mb-2">Rate Limits</h4>
                    <div className="space-y-2 text-sm">
                      <div>Per Minute: {selectedApiKey?.rateLimit?.requestsPerMinute || 'N/A'}</div>
                      <div>Per Hour: {selectedApiKey?.rateLimit?.requestsPerHour || 'N/A'}</div>
                      <div>Per Day: {selectedApiKey?.rateLimit?.requestsPerDay || 'N/A'}</div>
                    </div>
                  </div>
                </div>

                <div>
                  <SecureApiKeyField
                    label="API Key"
                    apiKey={selectedApiKey?.key || ''}
                    keyId={selectedApiKey?.keyId || ''}
                    apiKeyDocId={selectedApiKey?._id}
                    description="This API key provides access to your account. Keep it secure and never share it publicly."
                  />
                </div>

                <div>
                  <h4 className="font-medium mb-2">Permissions</h4>
                  <div className="flex flex-wrap gap-2">
                    {selectedApiKey?.permissions?.map((permission: string, index: number) => (
                      <Badge key={index} variant="secondary">
                        <Shield className="h-3 w-3 mr-1" />
                        {permission}
                      </Badge>
                    ))}
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-2">Usage Statistics</h4>
                  <div className="grid grid-cols-3 gap-4">
                    <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg text-center">
                      <div className="text-2xl font-bold text-blue-600">
                        {selectedApiKey?.usageCount?.toLocaleString() || 0}
                      </div>
                      <div className="text-xs text-blue-600">Total Requests</div>
                    </div>
                    <div className="p-3 bg-green-50 border border-green-200 rounded-lg text-center">
                      <div className="text-2xl font-bold text-green-600">
                        {selectedApiKey?.isActive ? '100%' : '0%'}
                      </div>
                      <div className="text-xs text-green-600">Uptime</div>
                    </div>
                    <div className="p-3 bg-purple-50 border border-purple-200 rounded-lg text-center">
                      <div className="text-2xl font-bold text-purple-600">
                        {selectedApiKey?.lastUsedAt ?
                          Math.round((Date.now() - selectedApiKey.lastUsedAt) / (1000 * 60 * 60)) : 'N/A'
                        }h
                      </div>
                      <div className="text-xs text-purple-600">Since Last Use</div>
                    </div>
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setShowDetailsDialog(false)}>
                  Close
                </Button>
                <Button
                  variant="outline"
                  className="text-orange-600 border-orange-600 hover:bg-orange-50"
                  onClick={() => {
                    if (selectedApiKey) {
                      handleRevokeApiKey(selectedApiKey._id);
                    }
                  }}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Revoke Key
                </Button>
                <Button
                  variant="destructive"
                  onClick={() => {
                    if (selectedApiKey) {
                      handleDeleteApiKey(selectedApiKey._id);
                    }
                  }}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Permanently
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          {/* Revoke Confirmation Dialog */}
          <ConfirmDialog
            open={showRevokeConfirm}
            onOpenChange={setShowRevokeConfirm}
            title="Revoke API Key"
            description="Are you sure you want to revoke this API key? This will disable the key but keep it in the database for audit purposes. The key can be viewed but not used for API requests."
            confirmText="Revoke Key"
            cancelText="Cancel"
            variant="destructive"
            loading={isRevoking}
            onConfirm={confirmRevokeApiKey}
            onCancel={() => {
              setShowRevokeConfirm(false);
              setApiKeyToRevoke(null);
            }}
          />

          {/* Delete Confirmation Dialog */}
          <ConfirmDialog
            open={showDeleteConfirm}
            onOpenChange={setShowDeleteConfirm}
            title="Delete API Key Permanently"
            description="Are you sure you want to permanently delete this API key? This action cannot be undone and will completely remove the key from the database. All audit logs will be preserved, but the key itself will be gone forever."
            confirmText="Delete Permanently"
            cancelText="Cancel"
            variant="destructive"
            loading={isDeleting}
            onConfirm={confirmDeleteApiKey}
            onCancel={() => {
              setShowDeleteConfirm(false);
              setApiKeyToDelete(null);
            }}
          />
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  );
}
