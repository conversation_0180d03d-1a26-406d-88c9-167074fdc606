import { mutation, query } from "./_generated/server";

// Migration to update existing data from Shopify fields to new field names
export const migrateShopifyFields = mutation({
  args: {},
  handler: async (ctx) => {
    console.log("Starting migration from Shopify fields...");
    
    // Migrate users table
    const users = await ctx.db.query("users").collect();
    console.log("Found users:", users.length);
    let usersMigrated = 0;
    
    for (const user of users) {
      const updates: any = {};

      console.log("Processing user:", user._id, "userId:", (user as any).userId);

      // Check if user doesn't have userId
      if (!(user as any).userId) {
        // Use shopifyCustomerId if it exists and is not empty, otherwise generate a new ID
        if ((user as any).shopifyCustomerId && (user as any).shopifyCustomerId.trim() !== '') {
          updates.userId = (user as any).shopifyCustomerId;
        } else {
          // Generate a new userId based on email or a unique identifier
          const emailPrefix = user.email && user.email.trim() !== '' ? user.email.split('@')[0] : 'user';
          updates.userId = `user_${emailPrefix}_${Date.now()}`;
        }
        usersMigrated++;
      }

      // If there are updates to make
      if (Object.keys(updates).length > 0) {
        await ctx.db.patch(user._id, updates);
      }
    }
    
    // Migrate products table
    const products = await ctx.db.query("products").collect();
    let productsMigrated = 0;
    
    for (const product of products) {
      const updates: any = {};

      // Check if product doesn't have productId
      if (!(product as any).productId) {
        // Use shopifyProductId if it exists and is not empty, otherwise generate a new ID
        if ((product as any).shopifyProductId && (product as any).shopifyProductId.trim() !== '') {
          updates.productId = (product as any).shopifyProductId;
        } else {
          // Generate a new productId based on title or a unique identifier
          const titleSlug = (product as any).title?.toLowerCase().replace(/[^a-z0-9]/g, '_').substring(0, 20) || 'product';
          updates.productId = `${titleSlug}_${Date.now()}`;
        }
        productsMigrated++;
      }

      // If there are updates to make
      if (Object.keys(updates).length > 0) {
        await ctx.db.patch(product._id, updates);
      }
    }
    
    console.log(`Migration completed: ${usersMigrated} users, ${productsMigrated} products migrated`);
    
    return {
      message: "Migration completed successfully",
      usersMigrated,
      productsMigrated,
    };
  },
});

// Simple function to fix the specific user that's missing userId
export const fixExistingUser = mutation({
  args: {},
  handler: async (ctx) => {
    const users = await ctx.db.query("users").collect();

    for (const user of users) {
      if (!(user as any).userId) {
        await ctx.db.patch(user._id, {
          userId: `user_${user.firstName.toLowerCase()}_${Date.now()}`,
        });
        console.log(`Fixed user ${user._id} with userId`);
      }
    }

    const products = await ctx.db.query("products").collect();

    for (const product of products) {
      if (!(product as any).productId) {
        const titleSlug = (product as any).title?.toLowerCase().replace(/[^a-z0-9]/g, '_').substring(0, 20) || 'product';
        await ctx.db.patch(product._id, {
          productId: `${titleSlug}_${Date.now()}`,
        });
        console.log(`Fixed product ${product._id} with productId`);
      }
    }

    return { message: "Fixed existing records" };
  },
});

// Function to remove old Shopify fields by recreating records
export const removeShopifyFields = mutation({
  args: {},
  handler: async (ctx) => {
    console.log("Removing old Shopify fields...");

    // Get all users
    const users = await ctx.db.query("users").collect();

    for (const user of users) {
      if ((user as any).shopifyCustomerId !== undefined) {
        // Create a new user object without the shopifyCustomerId field
        const cleanUser = {
          userId: (user as any).userId,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          phone: user.phone,
          businessName: user.businessName,
          gstNumber: user.gstNumber,
          isGstVerified: user.isGstVerified,
          status: user.status,
          role: user.role,
          approvedBy: user.approvedBy,
          approvedAt: user.approvedAt,
          rejectedBy: user.rejectedBy,
          rejectedAt: user.rejectedAt,
          rejectionReason: user.rejectionReason,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt,
          lastLoginAt: user.lastLoginAt,
          legalNameOfBusiness: user.legalNameOfBusiness,
          tradeName: user.tradeName,
          constitutionOfBusiness: user.constitutionOfBusiness,
          taxpayerType: user.taxpayerType,
          gstStatus: user.gstStatus,
          principalPlaceOfBusiness: user.principalPlaceOfBusiness,
          agreedToEmailMarketing: user.agreedToEmailMarketing,
          agreedToSmsMarketing: user.agreedToSmsMarketing,
        };

        // Delete the old record and create a new one
        await ctx.db.delete(user._id);
        await ctx.db.insert("users", cleanUser);
        console.log(`Cleaned user ${user._id}`);
      }
    }

    return { message: "Removed old Shopify fields from users" };
  },
});
