"use client";

import { useState, useEffect } from "react"; // Import useEffect
import { useCart } from "@/hooks/use-cart"; // Import useCart
// import { useToast } from "@/components/ui/use-toast"; // Placeholder for toast notifications
import { motion } from "framer-motion";
import {
  ShoppingBag,
  Heart,
  Share2,
  Download,
  ChevronDown,
  ChevronUp,
} from "lucide-react";
import { Button } from "@/components/ui/button"
import AddToCartButton from "@/components/add-to-cart-button";
import { Badge } from "@/components/ui/badge";
import type { Product, ProductVariant } from "@/lib/types";
import ProductImageGallery from "@/components/product-image-gallery";
// import { useToast } from "@/components/ui/use-toast"; // Placeholder for toast notifications

interface ProductDetailProps {
  product: Product;
}

export default function ProductDetail({ product }: ProductDetailProps) {
  const [quantity, setQuantity] = useState(1);
  const [selectedVariant, setSelectedVariant] = useState<ProductVariant | undefined>(undefined);
  const [packageSize, setPackageSize] = useState("1 KG"); // Add state for package size
  const { addItem, isLoading: isCartLoading } = useCart();
  // const { toast } = useToast();

  useEffect(() => {
    // Initialize selectedVariant with the first available variant
    if (product.variants?.edges && product.variants.edges.length > 0) {
      const firstAvailableVariant = product.variants.edges.find(edge => edge.node.availableForSale)?.node;
      setSelectedVariant(firstAvailableVariant || product.variants.edges[0].node); // Fallback to first variant if none are availableForSale
    }
  }, [product.variants]);

  const handleVariantChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const variantId = event.target.value;
    const newSelectedVariant = product.variants?.edges?.find(
      (edge) => edge.node.id === variantId
    )?.node;
    setSelectedVariant(newSelectedVariant);
  };

  // Handle package size change
  const handlePackageSizeChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    setPackageSize(event.target.value);
  };

  const handleAddToCart = async () => {
    if (!selectedVariant) {
      console.error("No variant selected or available");
      // toast({ title: "Error", description: "Please select a product option.", variant: "destructive" });
      return;
    }
    if (!selectedVariant.availableForSale) {
      console.error("Selected variant is not available for sale");
      // toast({ title: "Unavailable", description: "This option is currently unavailable.", variant: "default" });
      return;
    }

    const itemToAdd = {
      variantId: selectedVariant.id,
      quantity: quantity,
    };

    console.log("Attempting to add to cart:", itemToAdd);

    try {
      await addItem(itemToAdd);
      console.log(`${product.title} (${selectedVariant.title}) added to cart`);
      // toast({ title: "Success", description: `${product.title} (${selectedVariant.title}) added to cart.` });
    } catch (error) {
      console.error("Failed to add item to cart:", error);
      // toast({ title: "Error", description: "Could not add item to cart.", variant: "destructive" });
    }
  };

  const incrementQuantity = () => {
    setQuantity((prev) => prev + 1);
  };

  const decrementQuantity = () => {
    if (quantity > 1) {
      setQuantity((prev) => prev - 1);
    }
  };

  const getCategory = () => {
    return product.collections?.edges?.[0]?.node?.title || "Uncategorized";
  };

  const displayPrice = selectedVariant?.priceV2?.amount ? parseFloat(selectedVariant.priceV2.amount).toFixed(2) : product.priceRange?.minVariantPrice?.amount ? parseFloat(product.priceRange.minVariantPrice.amount).toFixed(2) : "N/A";
  const displayCompareAtPrice = selectedVariant?.compareAtPriceV2?.amount ? parseFloat(selectedVariant.compareAtPriceV2.amount).toFixed(2) : null;


  // Helper function to get metafield value
  const getMetafieldValue = (key: string) => {
    if (!product.metafields || !Array.isArray(product.metafields)) {
      return "";
    }
    return (
      product.metafields.find((field) => field && field.key === key)?.value ||
      ""
    );
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
      <motion.div
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.5 }}
      >
        <ProductImageGallery product={product} />
      </motion.div>

      <motion.div
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.5 }}
        className="flex flex-col"
      >
        <div className="mb-6">
          <div className="flex items-center mb-2">
            <Badge
              variant="outline"
              className="text-xs font-medium bg-teal-50 text-teal-700 border-teal-200 mr-2"
            >
              {getCategory()}
            </Badge>
            <Badge
              variant="outline"
              className={`text-xs font-medium border-neutral-200 ${
                !selectedVariant?.availableForSale
                  ? "bg-red-50 text-red-700 border-red-200"
                  : selectedVariant?.quantityAvailable !== null &&
                    selectedVariant?.quantityAvailable !== undefined &&
                    selectedVariant.quantityAvailable < 10 &&
                    selectedVariant.quantityAvailable > 0
                  ? "bg-yellow-50 text-yellow-700 border-yellow-200"
                  : "bg-green-50 text-green-700 border-green-200"
              }`}
            >
              {!selectedVariant?.availableForSale
                ? "Out of Stock"
                : selectedVariant?.quantityAvailable !== null &&
                  selectedVariant?.quantityAvailable !== undefined &&
                  selectedVariant.quantityAvailable < 10 &&
                  selectedVariant.quantityAvailable > 0
                ? "Low in stock"
                : "In Stock"}
            </Badge>
          </div>

          {selectedVariant?.availableForSale && selectedVariant?.quantityAvailable !== null && selectedVariant?.quantityAvailable !== undefined && selectedVariant.quantityAvailable < 10 && selectedVariant.quantityAvailable > 0 && (
            <p className="text-sm text-yellow-600 mb-2">
              Hurry! only {selectedVariant.quantityAvailable} are left
            </p>
          )}

          <h1 className="text-3xl md:text-4xl font-medium mb-2">
            {product.title}
          </h1>

          <div className="flex items-center mb-4">
            {displayCompareAtPrice && (
              <span className="text-sm text-neutral-500 line-through mr-2">
                ₹{displayCompareAtPrice}
              </span>
            )}
            <span className="text-xl font-medium text-neutral-900">
              ₹{displayPrice}
            </span>
          </div>

          <p className="text-neutral-600 mb-6" dangerouslySetInnerHTML={{ __html: product.descriptionHtml || product.description }}></p>
        </div>

        <div className="border-t border-b py-6 mb-6">
          <div className="grid grid-cols-2 gap-4 mb-6">
            <div>
              <h3 className="text-sm text-neutral-500 mb-1">Purity</h3>
              <p className="font-medium">{getMetafieldValue("purity")}%</p>
            </div>
            <div>
              <h3 className="text-sm text-neutral-500 mb-1">HSN Number</h3>
              <p className="font-medium">{getMetafieldValue("hsn_number")}</p>
            </div>
            <div>
              <h3 className="text-sm text-neutral-500 mb-1">CAS Number</h3>
              <p className="font-medium">
                {getMetafieldValue("cas_number") || "N/A"}
              </p>
            </div>
            <div>
              <h3 className="text-sm text-neutral-500 mb-1">
                Molecular Formula
              </h3>
              <p className="font-medium">
                {JSON.parse(getMetafieldValue("molecular_formula")).children[0]
                  .children[0].value || "N/A"}
              </p>
            </div>
          </div>

          <div className="flex items-center mb-6">
            <div className="mr-4">
              <h3 className="text-sm text-neutral-500 mb-2">Quantity</h3>
              <div className="flex items-center border rounded-md">
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-9 w-9 rounded-none"
                  onClick={decrementQuantity}
                >
                  <ChevronDown className="h-4 w-4" />
                </Button>
                <span className="w-12 text-center font-medium">{quantity}</span>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-9 w-9 rounded-none"
                  onClick={incrementQuantity}
                >
                  <ChevronUp className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div>
              <h3 className="text-sm text-neutral-500 mb-2">Package Size</h3>
              <select 
                className="border rounded-md h-9 px-3 w-full focus:outline-none focus:ring-2 focus:ring-teal-500"
                value={packageSize}
                onChange={handlePackageSizeChange}
              >
                <option value="1 KG">1 KG</option>
                <option value="2 KG">2 KG</option>
                <option value="3 KG">3 KG</option>
              </select>
            </div>
          </div>

          <div className="flex flex-wrap gap-4">
            <AddToCartButton
              productId={product.id}
              variantId={selectedVariant?.id || ""}
              name={product.title}
              price={parseFloat(selectedVariant?.priceV2.amount || "0")}
              quantity={quantity}
              image={product.images?.edges?.[0]?.node.url}
              category={getCategory()}
              packageSize={packageSize} // Pass package size to AddToCartButton
              disabled={!selectedVariant || !selectedVariant.availableForSale}
            />
            <Button variant="outline" size="lg" className="flex-1">
              <Heart className="h-5 w-5 mr-2" />
              Save for Later
            </Button>
          </div>
        </div>

        <div className="flex items-center justify-between text-sm text-neutral-500">
          <Button
            variant="ghost"
            size="sm"
            className="text-neutral-500 hover:text-neutral-700"
          >
            <Share2 className="h-4 w-4 mr-2" />
            Share Product
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="text-neutral-500 hover:text-neutral-700"
          >
            <Download className="h-4 w-4 mr-2" />
            Download Specs
          </Button>
        </div>
      </motion.div>
    </div>
  );
}
