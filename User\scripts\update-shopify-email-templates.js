// Script to update Shopify email templates via Admin API
// Note: This requires Admin API access and proper permissions

const SHOPIFY_DOMAIN = 'benzochem.myshopify.com';
const ADMIN_ACCESS_TOKEN = 'your-admin-access-token'; // Use environment variable in production

// Email template configurations
const emailTemplates = {
  'customer_account_activate': {
    subject: 'Activate Your Benzochem Industries Account',
    body_html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #14b8a6; color: white; padding: 20px; text-align: center;">
          <h1>Welcome to {{ shop.name }}!</h1>
        </div>
        
        <div style="padding: 20px;">
          <p>Hi {{ customer.first_name }},</p>
          
          <p>Thank you for registering with Benzochem Industries! We're excited to have you as part of our business community.</p>
          
          <p>To complete your registration and activate your account, please click the button below:</p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="{{ account_activation_url }}"
               style="background-color: #14b8a6; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; display: inline-block; font-size: 16px;">
              Activate Your Account
            </a>
          </div>

          <p>Or copy and paste this link into your browser:</p>
          <p style="background-color: #f8f9fa; padding: 10px; border-radius: 4px; word-break: break-all;">
            {{ account_activation_url }}
          </p>
          
          <div style="background-color: #f0f9ff; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #14b8a6; margin-top: 0;">What's Next?</h3>
            <ul>
              <li>Browse our extensive chemical product catalog</li>
              <li>Access exclusive business pricing</li>
              <li>Track your orders and shipments</li>
              <li>Manage your business profile and GST details</li>
            </ul>
          </div>
          
          <div style="background-color: #fef3c7; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <p style="margin: 0;"><strong>Important:</strong> This activation link will expire in 24 hours for security reasons.</p>
          </div>
          
          <p>If you have any questions, please don't hesitate to contact our support team.</p>
          
          <p>Best regards,<br>
          The Benzochem Industries Team</p>
        </div>
        
        <div style="background-color: #f8f9fa; padding: 15px; text-align: center; font-size: 12px; color: #666;">
          <p>© {{ "now" | date: "%Y" }} {{ shop.name }}. All rights reserved.</p>
          <p>{{ shop.address.address1 }}, {{ shop.address.city }}, {{ shop.address.province }} {{ shop.address.zip }}</p>
        </div>
      </div>
    `
  },
  
  'customer_account_welcome': {
    subject: 'Welcome to {{ shop.name }} - Your Account is Active!',
    body_html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #14b8a6; color: white; padding: 20px; text-align: center;">
          <h1>Account Activated Successfully!</h1>
        </div>
        
        <div style="padding: 20px;">
          <p>Hi {{ customer.first_name }},</p>
          
          <p>Great news! Your Benzochem Industries account has been successfully activated and is ready to use.</p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="{{ shop.url }}/account" 
               style="background-color: #14b8a6; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; display: inline-block; font-size: 16px;">
              Access Your Account
            </a>
          </div>
          
          <div style="background-color: #f0f9ff; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #14b8a6; margin-top: 0;">Your Account Benefits:</h3>
            <ul>
              <li><strong>Business Pricing:</strong> Access to wholesale and bulk pricing</li>
              <li><strong>Order Tracking:</strong> Real-time updates on your shipments</li>
              <li><strong>GST Management:</strong> Automated GST calculations and invoicing</li>
              <li><strong>Product Catalog:</strong> Browse our complete chemical inventory</li>
              <li><strong>Quick Reorders:</strong> Easily reorder your frequently purchased items</li>
            </ul>
          </div>
          
          <p>Ready to get started? Browse our catalog or contact our sales team for personalized assistance.</p>
          
          <p>Best regards,<br>
          The Benzochem Industries Team</p>
        </div>
        
        <div style="background-color: #f8f9fa; padding: 15px; text-align: center; font-size: 12px; color: #666;">
          <p>© {{ "now" | date: "%Y" }} {{ shop.name }}. All rights reserved.</p>
        </div>
      </div>
    `
  }
};

// Function to update email templates
async function updateEmailTemplates() {
  console.log('🔧 Updating Shopify Email Templates...');
  
  for (const [templateName, template] of Object.entries(emailTemplates)) {
    try {
      console.log(`Updating template: ${templateName}`);
      
      // Note: Shopify doesn't have a direct API to update notification templates
      // This would typically be done through the admin interface
      // or using Shopify CLI/Theme development tools
      
      console.log(`Template ${templateName} configuration:`, {
        subject: template.subject,
        bodyLength: template.body_html.length
      });
      
    } catch (error) {
      console.error(`Failed to update template ${templateName}:`, error);
    }
  }
  
  console.log('✅ Email template update process completed!');
  console.log('Note: Apply these templates manually in Shopify Admin > Settings > Notifications');
}

// Instructions for manual application
function showInstructions() {
  console.log('\n📋 Manual Application Instructions:');
  console.log('=====================================');
  console.log('1. Go to Shopify Admin: https://benzochem.myshopify.com/admin');
  console.log('2. Navigate to Settings > Notifications');
  console.log('3. Find the email template you want to update');
  console.log('4. Copy the HTML content from the templates above');
  console.log('5. Paste into the email body editor');
  console.log('6. Update the subject line');
  console.log('7. Preview and save the changes');
  console.log('\n🎨 Templates available:');
  Object.keys(emailTemplates).forEach(template => {
    console.log(`   - ${template}`);
  });
}

// Run the update process
updateEmailTemplates();
showInstructions();
