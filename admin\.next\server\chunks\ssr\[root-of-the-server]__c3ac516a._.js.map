{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/providers/convex-provider.tsx"], "sourcesContent": ["\"use client\";\n\nimport { ReactNode } from \"react\";\nimport { ConvexProvider, ConvexReactClient } from \"convex/react\";\n\nconst convex = new ConvexReactClient(process.env.NEXT_PUBLIC_CONVEX_URL!);\n\nexport function ConvexClientProvider({ children }: { children: ReactNode }) {\n  return <ConvexProvider client={convex}>{children}</ConvexProvider>;\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAHA;;;AAKA,MAAM,SAAS,IAAI,wJAAA,CAAA,oBAAiB;AAE7B,SAAS,qBAAqB,EAAE,QAAQ,EAA2B;IACxE,qBAAO,8OAAC,wJAAA,CAAA,iBAAc;QAAC,QAAQ;kBAAS;;;;;;AAC1C", "debugId": null}}, {"offset": {"line": 41, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/providers/theme-provider.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport { ThemeProvider as NextThemesProvider } from \"next-themes\";\nimport { type ThemeProviderProps } from \"next-themes/dist/types\";\n\nexport function ThemeProvider({ children, ...props }: ThemeProviderProps) {\n  return <NextThemesProvider {...props}>{children}</NextThemesProvider>;\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAMO,SAAS,cAAc,EAAE,QAAQ,EAAE,GAAG,OAA2B;IACtE,qBAAO,8OAAC,gJAAA,CAAA,gBAAkB;QAAE,GAAG,KAAK;kBAAG;;;;;;AACzC", "debugId": null}}, {"offset": {"line": 65, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/contexts/auth-context.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { createContext, useContext, useEffect, useState } from \"react\";\n\nexport interface Admin {\n  _id: string;\n  email: string;\n  firstName: string;\n  lastName: string;\n  role?: string;\n  permissions?: string[];\n}\n\ninterface AuthContextType {\n  admin: Admin | null;\n  isLoading: boolean;\n  isAuthenticated: boolean;\n  csrfToken: string | null;\n  login: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;\n  logout: () => Promise<{ success: boolean; error?: string }>;\n  refreshSession: () => Promise<void>;\n  hasPermission: (permission: string) => boolean;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport function useAuth() {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error(\"useAuth must be used within an AuthProvider\");\n  }\n  return context;\n}\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [admin, setAdmin] = useState<Admin | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [csrfToken, setCsrfToken] = useState<string | null>(null);\n\n  // Get CSRF token from cookie\n  const getCSRFToken = (): string | null => {\n    if (typeof document === 'undefined') return null;\n    const cookies = document.cookie.split(';');\n    const csrfCookie = cookies.find(cookie => cookie.trim().startsWith('benzochem-csrf-token='));\n    return csrfCookie ? csrfCookie.split('=')[1] : null;\n  };\n\n  // Check session on mount and periodically\n  useEffect(() => {\n    const checkSession = async () => {\n      try {\n        const response = await fetch('/api/auth/session', {\n          method: 'GET',\n          credentials: 'include',\n        });\n\n        if (response.ok) {\n          const data = await response.json();\n          if (data.success && data.admin) {\n            setAdmin(data.admin);\n            setCsrfToken(getCSRFToken());\n          } else {\n            setAdmin(null);\n            setCsrfToken(null);\n          }\n        } else {\n          setAdmin(null);\n          setCsrfToken(null);\n        }\n      } catch (error) {\n        console.error('Session check error:', error);\n        setAdmin(null);\n        setCsrfToken(null);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    checkSession();\n\n    // Set up periodic session refresh (every 30 minutes)\n    const interval = setInterval(checkSession, 30 * 60 * 1000);\n    return () => clearInterval(interval);\n  }, []);\n\n  const login = async (email: string, password: string): Promise<{ success: boolean; error?: string }> => {\n    try {\n      setIsLoading(true);\n\n      const response = await fetch('/api/auth/login', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          ...(csrfToken && { 'X-CSRF-Token': csrfToken }),\n        },\n        credentials: 'include',\n        body: JSON.stringify({ email, password, csrfToken }),\n      });\n\n      const data = await response.json();\n\n      if (response.ok && data.success) {\n        setAdmin(data.admin);\n        setCsrfToken(data.csrfToken);\n        setIsLoading(false);\n        return { success: true };\n      } else {\n        setIsLoading(false);\n        return { success: false, error: data.error || \"Login failed\" };\n      }\n    } catch (error) {\n      console.error(\"Login error:\", error);\n      setIsLoading(false);\n      return { success: false, error: \"Login failed. Please try again.\" };\n    }\n  };\n\n  const logout = async (): Promise<{ success: boolean; error?: string }> => {\n    try {\n      const response = await fetch('/api/auth/logout', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          ...(csrfToken && { 'X-CSRF-Token': csrfToken }),\n        },\n        credentials: 'include',\n        body: JSON.stringify({ csrfToken }),\n      });\n\n      // Clear local state regardless of API response\n      setAdmin(null);\n      setCsrfToken(null);\n\n      // Force redirect to login page after logout\n      if (typeof window !== 'undefined') {\n        window.location.href = '/login';\n      }\n\n      if (response.ok) {\n        return { success: true };\n      } else {\n        const data = await response.json();\n        return { success: false, error: data.error || \"Logout failed\" };\n      }\n    } catch (error) {\n      console.error(\"Logout error:\", error);\n      // Clear local state even on error\n      setAdmin(null);\n      setCsrfToken(null);\n\n      // Force redirect to login page even on error\n      if (typeof window !== 'undefined') {\n        window.location.href = '/login';\n      }\n\n      return { success: false, error: \"Logout failed\" };\n    }\n  };\n\n  const refreshSession = async (): Promise<void> => {\n    try {\n      const response = await fetch('/api/auth/session', {\n        method: 'GET',\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success && data.admin) {\n          setAdmin(data.admin);\n          setCsrfToken(getCSRFToken());\n        } else {\n          setAdmin(null);\n          setCsrfToken(null);\n        }\n      } else {\n        setAdmin(null);\n        setCsrfToken(null);\n      }\n    } catch (error) {\n      console.error('Session refresh error:', error);\n      setAdmin(null);\n      setCsrfToken(null);\n    }\n  };\n\n  const hasPermission = (permission: string): boolean => {\n    if (!admin) return false;\n    if (admin.role === \"super_admin\") return true; // Super admins have all permissions\n    return admin.permissions?.includes(permission) || false;\n  };\n\n  const isAuthenticated = !!admin;\n\n  const value: AuthContextType = {\n    admin,\n    isLoading,\n    isAuthenticated,\n    csrfToken,\n    login,\n    logout,\n    refreshSession,\n    hasPermission,\n  };\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAwBA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,SAAS,aAAa,EAAE,QAAQ,EAAiC;IACtE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE1D,6BAA6B;IAC7B,MAAM,eAAe;QACnB,IAAI,OAAO,aAAa,aAAa,OAAO;QAC5C,MAAM,UAAU,SAAS,MAAM,CAAC,KAAK,CAAC;QACtC,MAAM,aAAa,QAAQ,IAAI,CAAC,CAAA,SAAU,OAAO,IAAI,GAAG,UAAU,CAAC;QACnE,OAAO,aAAa,WAAW,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG;IACjD;IAEA,0CAA0C;IAC1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,qBAAqB;oBAChD,QAAQ;oBACR,aAAa;gBACf;gBAEA,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,OAAO,MAAM,SAAS,IAAI;oBAChC,IAAI,KAAK,OAAO,IAAI,KAAK,KAAK,EAAE;wBAC9B,SAAS,KAAK,KAAK;wBACnB,aAAa;oBACf,OAAO;wBACL,SAAS;wBACT,aAAa;oBACf;gBACF,OAAO;oBACL,SAAS;oBACT,aAAa;gBACf;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,wBAAwB;gBACtC,SAAS;gBACT,aAAa;YACf,SAAU;gBACR,aAAa;YACf;QACF;QAEA;QAEA,qDAAqD;QACrD,MAAM,WAAW,YAAY,cAAc,KAAK,KAAK;QACrD,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,MAAM,QAAQ,OAAO,OAAe;QAClC,IAAI;YACF,aAAa;YAEb,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,GAAI,aAAa;wBAAE,gBAAgB;oBAAU,CAAC;gBAChD;gBACA,aAAa;gBACb,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAO;oBAAU;gBAAU;YACpD;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,IAAI,KAAK,OAAO,EAAE;gBAC/B,SAAS,KAAK,KAAK;gBACnB,aAAa,KAAK,SAAS;gBAC3B,aAAa;gBACb,OAAO;oBAAE,SAAS;gBAAK;YACzB,OAAO;gBACL,aAAa;gBACb,OAAO;oBAAE,SAAS;oBAAO,OAAO,KAAK,KAAK,IAAI;gBAAe;YAC/D;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,aAAa;YACb,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAkC;QACpE;IACF;IAEA,MAAM,SAAS;QACb,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,oBAAoB;gBAC/C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,GAAI,aAAa;wBAAE,gBAAgB;oBAAU,CAAC;gBAChD;gBACA,aAAa;gBACb,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAU;YACnC;YAEA,+CAA+C;YAC/C,SAAS;YACT,aAAa;YAEb,4CAA4C;YAC5C,uCAAmC;;YAEnC;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,OAAO;oBAAE,SAAS;gBAAK;YACzB,OAAO;gBACL,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,OAAO;oBAAE,SAAS;oBAAO,OAAO,KAAK,KAAK,IAAI;gBAAgB;YAChE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,kCAAkC;YAClC,SAAS;YACT,aAAa;YAEb,6CAA6C;YAC7C,uCAAmC;;YAEnC;YAEA,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAgB;QAClD;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,qBAAqB;gBAChD,QAAQ;gBACR,aAAa;YACf;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,IAAI,KAAK,KAAK,EAAE;oBAC9B,SAAS,KAAK,KAAK;oBACnB,aAAa;gBACf,OAAO;oBACL,SAAS;oBACT,aAAa;gBACf;YACF,OAAO;gBACL,SAAS;gBACT,aAAa;YACf;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,SAAS;YACT,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,CAAC,OAAO,OAAO;QACnB,IAAI,MAAM,IAAI,KAAK,eAAe,OAAO,MAAM,oCAAoC;QACnF,OAAO,MAAM,WAAW,EAAE,SAAS,eAAe;IACpD;IAEA,MAAM,kBAAkB,CAAC,CAAC;IAE1B,MAAM,QAAyB;QAC7B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBAAO,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAC9C", "debugId": null}}, {"offset": {"line": 273, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/contexts/re-auth-context.tsx"], "sourcesContent": ["\"use client\"\n\nimport React, { createContext, useContext, useState, useCallback } from 'react';\n\ninterface ReAuthContextType {\n  isReAuthenticated: boolean;\n  reAuthTimestamp: number | null;\n  requestReAuth: () => Promise<boolean>;\n  clearReAuth: () => void;\n  isReAuthValid: () => boolean;\n}\n\nconst ReAuthContext = createContext<ReAuthContextType | undefined>(undefined);\n\nconst RE_AUTH_TIMEOUT = 10 * 60 * 1000; // 10 minutes\n\nexport function ReAuthProvider({ children }: { children: React.ReactNode }) {\n  const [isReAuthenticated, setIsReAuthenticated] = useState(false);\n  const [reAuthTimestamp, setReAuthTimestamp] = useState<number | null>(null);\n\n  const isReAuthValid = useCallback(() => {\n    if (!isReAuthenticated || !reAuthTimestamp) return false;\n    return Date.now() - reAuthTimestamp < RE_AUTH_TIMEOUT;\n  }, [isReAuthenticated, reAuthTimestamp]);\n\n  const requestReAuth = useCallback(async (): Promise<boolean> => {\n    // Check if current re-auth is still valid\n    if (isReAuthValid()) {\n      return true;\n    }\n\n    // This will be implemented to show the re-auth modal\n    // For now, we'll return a promise that resolves when re-auth is complete\n    return new Promise((resolve) => {\n      // This will be replaced with actual modal logic\n      const event = new CustomEvent('request-reauth', {\n        detail: { resolve }\n      });\n      window.dispatchEvent(event);\n    });\n  }, [isReAuthValid]);\n\n  const clearReAuth = useCallback(() => {\n    setIsReAuthenticated(false);\n    setReAuthTimestamp(null);\n  }, []);\n\n  // Internal method to set re-auth success (called by the modal)\n  const setReAuthSuccess = useCallback(() => {\n    setIsReAuthenticated(true);\n    setReAuthTimestamp(Date.now());\n  }, []);\n\n  // Listen for re-auth success events\n  React.useEffect(() => {\n    const handleReAuthSuccess = () => {\n      setReAuthSuccess();\n    };\n\n    window.addEventListener('reauth-success', handleReAuthSuccess);\n    return () => window.removeEventListener('reauth-success', handleReAuthSuccess);\n  }, [setReAuthSuccess]);\n\n  const value: ReAuthContextType = {\n    isReAuthenticated,\n    reAuthTimestamp,\n    requestReAuth,\n    clearReAuth,\n    isReAuthValid,\n  };\n\n  return (\n    <ReAuthContext.Provider value={value}>\n      {children}\n    </ReAuthContext.Provider>\n  );\n}\n\nexport function useReAuth() {\n  const context = useContext(ReAuthContext);\n  if (context === undefined) {\n    throw new Error('useReAuth must be used within a ReAuthProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAYA,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAiC;AAEnE,MAAM,kBAAkB,KAAK,KAAK,MAAM,aAAa;AAE9C,SAAS,eAAe,EAAE,QAAQ,EAAiC;IACxE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEtE,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC,IAAI,CAAC,qBAAqB,CAAC,iBAAiB,OAAO;QACnD,OAAO,KAAK,GAAG,KAAK,kBAAkB;IACxC,GAAG;QAAC;QAAmB;KAAgB;IAEvC,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC,0CAA0C;QAC1C,IAAI,iBAAiB;YACnB,OAAO;QACT;QAEA,qDAAqD;QACrD,yEAAyE;QACzE,OAAO,IAAI,QAAQ,CAAC;YAClB,gDAAgD;YAChD,MAAM,QAAQ,IAAI,YAAY,kBAAkB;gBAC9C,QAAQ;oBAAE;gBAAQ;YACpB;YACA,OAAO,aAAa,CAAC;QACvB;IACF,GAAG;QAAC;KAAc;IAElB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,qBAAqB;QACrB,mBAAmB;IACrB,GAAG,EAAE;IAEL,+DAA+D;IAC/D,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACnC,qBAAqB;QACrB,mBAAmB,KAAK,GAAG;IAC7B,GAAG,EAAE;IAEL,oCAAoC;IACpC,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,MAAM,sBAAsB;YAC1B;QACF;QAEA,OAAO,gBAAgB,CAAC,kBAAkB;QAC1C,OAAO,IAAM,OAAO,mBAAmB,CAAC,kBAAkB;IAC5D,GAAG;QAAC;KAAiB;IAErB,MAAM,QAA2B;QAC/B;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,cAAc,QAAQ;QAAC,OAAO;kBAC5B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 361, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 377, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50 backdrop-blur-sm duration-300 transition-all ease-out\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-top-[2%] data-[state=open]:slide-in-from-top-[2%] fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-0 rounded-xl border border-border/50 shadow-2xl duration-300 sm:max-w-lg\",\n          \"backdrop-blur-sm bg-background/95 supports-[backdrop-filter]:bg-background/80\",\n          \"transition-all ease-out\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-all duration-200 hover:opacity-100 hover:bg-accent hover:scale-110 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\n        \"flex flex-col gap-3 text-center sm:text-left px-4 pt-4 pb-3 sm:px-6 sm:pt-6 sm:pb-4\",\n        \"border-b border-border/50 bg-muted/20\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-3 sm:flex-row sm:justify-end px-4 py-3 sm:px-6 sm:py-4\",\n        \"border-t border-border/50 bg-muted/10\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\n        \"text-xl font-semibold leading-tight tracking-tight text-foreground\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\n        \"text-muted-foreground text-sm leading-relaxed\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gNACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gdACA,iFACA,2BACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,8OAAC,kKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uFACA,yCACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oFACA,yCACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iDACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 551, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 608, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 634, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 662, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return (\n    <TabsPrimitive.Root\n      data-slot=\"tabs\"\n      className={cn(\"flex flex-col gap-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return (\n    <TabsPrimitive.List\n      data-slot=\"tabs-list\"\n      className={cn(\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return (\n    <TabsPrimitive.Trigger\n      data-slot=\"tabs-trigger\"\n      className={cn(\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return (\n    <TabsPrimitive.Content\n      data-slot=\"tabs-content\"\n      className={cn(\"flex-1 outline-none\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 726, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/convex/_generated/api.js"], "sourcesContent": ["/* eslint-disable */\n/**\n * Generated `api` utility.\n *\n * THIS CODE IS AUTOMATICALLY GENERATED.\n *\n * To regenerate, run `npx convex dev`.\n * @module\n */\n\nimport { anyApi } from \"convex/server\";\n\n/**\n * A utility for referencing Convex functions in your app's API.\n *\n * Usage:\n * ```js\n * const myFunctionReference = api.myModule.myFunction;\n * ```\n */\nexport const api = anyApi;\nexport const internal = anyApi;\n"], "names": [], "mappings": "AAAA,kBAAkB,GAClB;;;;;;;CAOC;;;;AAED;AAAA;;AAUO,MAAM,MAAM,sJAAA,CAAA,SAAM;AAClB,MAAM,WAAW,sJAAA,CAAA,SAAM", "debugId": null}}, {"offset": {"line": 748, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/components/auth/re-auth-modal.tsx"], "sourcesContent": ["\"use client\"\n\nimport React, { useState, useEffect } from 'react';\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogDescription } from '@/components/ui/dialog';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\nimport { Fingerprint, Lock, Loader2, Shield } from 'lucide-react';\nimport { toast } from 'sonner';\nimport { useAuth } from '@/contexts/auth-context';\nimport { useMutation } from 'convex/react';\nimport { api } from '../../../convex/_generated/api';\n\ninterface ReAuthModalProps {\n  open: boolean;\n  onOpenChange: (open: boolean) => void;\n  onSuccess: () => void;\n  onCancel: () => void;\n  title?: string;\n  description?: string;\n}\n\nexport function ReAuthModal({\n  open,\n  onOpenChange,\n  onSuccess,\n  onCancel,\n  title = \"Re-authenticate to Continue\",\n  description = \"For security, please verify your identity to access sensitive information.\"\n}: ReAuthModalProps) {\n  const [activeTab, setActiveTab] = useState<'biometric' | 'password'>('biometric');\n  const [password, setPassword] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [biometricSupported, setBiometricSupported] = useState(false);\n  const { admin } = useAuth();\n  const verifyPassword = useMutation(api.admins.verifyAdminPassword);\n\n  // Check for WebAuthn/biometric support\n  useEffect(() => {\n    const checkBiometricSupport = async () => {\n      if (typeof window !== 'undefined' && window.PublicKeyCredential) {\n        try {\n          const available = await PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable();\n          setBiometricSupported(available);\n          if (available) {\n            setActiveTab('biometric');\n          } else {\n            setActiveTab('password');\n          }\n        } catch (error) {\n          setBiometricSupported(false);\n          setActiveTab('password');\n        }\n      } else {\n        setBiometricSupported(false);\n        setActiveTab('password');\n      }\n    };\n\n    if (open) {\n      checkBiometricSupport();\n    }\n  }, [open]);\n\n  const handleBiometricAuth = async () => {\n    if (!biometricSupported) {\n      toast.error('Biometric authentication not supported');\n      return;\n    }\n\n    setIsLoading(true);\n    try {\n      // Create a simple WebAuthn assertion for re-authentication\n      const credential = await navigator.credentials.get({\n        publicKey: {\n          challenge: new TextEncoder().encode('reauth-challenge-' + Date.now()),\n          allowCredentials: [],\n          userVerification: 'required',\n          timeout: 60000,\n        }\n      });\n\n      if (credential) {\n        toast.success('Biometric authentication successful');\n        onSuccess();\n        onOpenChange(false);\n      } else {\n        toast.error('Biometric authentication failed');\n      }\n    } catch (error) {\n      console.error('Biometric auth error:', error);\n      if (error instanceof Error && error.name === 'NotAllowedError') {\n        toast.error('Biometric authentication was cancelled');\n      } else {\n        toast.error('Biometric authentication failed. Please try password authentication.');\n        setActiveTab('password');\n      }\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handlePasswordAuth = async () => {\n    if (!password.trim()) {\n      toast.error('Please enter your password');\n      return;\n    }\n\n    if (!admin?.email) {\n      toast.error('Admin email not found');\n      return;\n    }\n\n    setIsLoading(true);\n    try {\n      await verifyPassword({\n        email: admin.email,\n        password: password.trim(),\n      });\n\n      toast.success('Password authentication successful');\n      onSuccess();\n      onOpenChange(false);\n      setPassword('');\n    } catch (error) {\n      console.error('Password auth error:', error);\n      toast.error('Invalid password');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleCancel = () => {\n    setPassword('');\n    onCancel();\n    onOpenChange(false);\n  };\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"sm:max-w-md\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <Shield className=\"h-5 w-5 text-blue-600\" />\n            {title}\n          </DialogTitle>\n          <DialogDescription>\n            {description}\n          </DialogDescription>\n        </DialogHeader>\n\n        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'biometric' | 'password')}>\n          <TabsList className=\"grid w-full grid-cols-2\">\n            <TabsTrigger value=\"biometric\" disabled={!biometricSupported}>\n              <Fingerprint className=\"h-4 w-4 mr-2\" />\n              Biometric\n            </TabsTrigger>\n            <TabsTrigger value=\"password\">\n              <Lock className=\"h-4 w-4 mr-2\" />\n              Password\n            </TabsTrigger>\n          </TabsList>\n\n          <TabsContent value=\"biometric\" className=\"space-y-4\">\n            <div className=\"text-center py-6\">\n              <Fingerprint className=\"h-16 w-16 mx-auto text-blue-600 mb-4\" />\n              <p className=\"text-sm text-muted-foreground mb-4\">\n                Use your device's biometric authentication to verify your identity.\n              </p>\n              <Button\n                onClick={handleBiometricAuth}\n                disabled={isLoading || !biometricSupported}\n                className=\"w-full\"\n              >\n                {isLoading ? (\n                  <>\n                    <Loader2 className=\"h-4 w-4 mr-2 animate-spin\" />\n                    Authenticating...\n                  </>\n                ) : (\n                  <>\n                    <Fingerprint className=\"h-4 w-4 mr-2\" />\n                    Authenticate with Biometrics\n                  </>\n                )}\n              </Button>\n            </div>\n          </TabsContent>\n\n          <TabsContent value=\"password\" className=\"space-y-4\">\n            <div className=\"space-y-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"reauth-password\">Admin Password</Label>\n                <Input\n                  id=\"reauth-password\"\n                  type=\"password\"\n                  placeholder=\"Enter your admin password\"\n                  value={password}\n                  onChange={(e) => setPassword(e.target.value)}\n                  onKeyDown={(e) => {\n                    if (e.key === 'Enter') {\n                      handlePasswordAuth();\n                    }\n                  }}\n                  disabled={isLoading}\n                />\n              </div>\n              <Button\n                onClick={handlePasswordAuth}\n                disabled={isLoading || !password.trim()}\n                className=\"w-full\"\n              >\n                {isLoading ? (\n                  <>\n                    <Loader2 className=\"h-4 w-4 mr-2 animate-spin\" />\n                    Verifying...\n                  </>\n                ) : (\n                  <>\n                    <Lock className=\"h-4 w-4 mr-2\" />\n                    Verify Password\n                  </>\n                )}\n              </Button>\n            </div>\n          </TabsContent>\n        </Tabs>\n\n        <div className=\"flex justify-end gap-2 pt-4 border-t\">\n          <Button variant=\"outline\" onClick={handleCancel} disabled={isLoading}>\n            Cancel\n          </Button>\n        </div>\n      </DialogContent>\n    </Dialog>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AACA;AAZA;;;;;;;;;;;;;AAuBO,SAAS,YAAY,EAC1B,IAAI,EACJ,YAAY,EACZ,SAAS,EACT,QAAQ,EACR,QAAQ,6BAA6B,EACrC,cAAc,4EAA4E,EACzE;IACjB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4B;IACrE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IACxB,MAAM,iBAAiB,CAAA,GAAA,wJAAA,CAAA,cAAW,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,MAAM,CAAC,mBAAmB;IAEjE,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,wBAAwB;YAC5B,uCAAiE;;YAajE,OAAO;gBACL,sBAAsB;gBACtB,aAAa;YACf;QACF;QAEA,IAAI,MAAM;YACR;QACF;IACF,GAAG;QAAC;KAAK;IAET,MAAM,sBAAsB;QAC1B,IAAI,CAAC,oBAAoB;YACvB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,aAAa;QACb,IAAI;YACF,2DAA2D;YAC3D,MAAM,aAAa,MAAM,UAAU,WAAW,CAAC,GAAG,CAAC;gBACjD,WAAW;oBACT,WAAW,IAAI,cAAc,MAAM,CAAC,sBAAsB,KAAK,GAAG;oBAClE,kBAAkB,EAAE;oBACpB,kBAAkB;oBAClB,SAAS;gBACX;YACF;YAEA,IAAI,YAAY;gBACd,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd;gBACA,aAAa;YACf,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,IAAI,iBAAiB,SAAS,MAAM,IAAI,KAAK,mBAAmB;gBAC9D,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,aAAa;YACf;QACF,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,SAAS,IAAI,IAAI;YACpB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,CAAC,OAAO,OAAO;YACjB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,aAAa;QACb,IAAI;YACF,MAAM,eAAe;gBACnB,OAAO,MAAM,KAAK;gBAClB,UAAU,SAAS,IAAI;YACzB;YAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd;YACA,aAAa;YACb,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,eAAe;QACnB,YAAY;QACZ;QACA,aAAa;IACf;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCACjB;;;;;;;sCAEH,8OAAC,kIAAA,CAAA,oBAAiB;sCACf;;;;;;;;;;;;8BAIL,8OAAC,gIAAA,CAAA,OAAI;oBAAC,OAAO;oBAAW,eAAe,CAAC,QAAU,aAAa;;sCAC7D,8OAAC,gIAAA,CAAA,WAAQ;4BAAC,WAAU;;8CAClB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAY,UAAU,CAAC;;sDACxC,8OAAC,gNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAG1C,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;;sDACjB,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;sCAKrC,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAY,WAAU;sCACvC,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,gNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,8OAAC;wCAAE,WAAU;kDAAqC;;;;;;kDAGlD,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS;wCACT,UAAU,aAAa,CAAC;wCACxB,WAAU;kDAET,0BACC;;8DACE,8OAAC,iNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAA8B;;yEAInD;;8DACE,8OAAC,gNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;sCAQlD,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAW,WAAU;sCACtC,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAkB;;;;;;0DACjC,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;gDAC3C,WAAW,CAAC;oDACV,IAAI,EAAE,GAAG,KAAK,SAAS;wDACrB;oDACF;gDACF;gDACA,UAAU;;;;;;;;;;;;kDAGd,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS;wCACT,UAAU,aAAa,CAAC,SAAS,IAAI;wCACrC,WAAU;kDAET,0BACC;;8DACE,8OAAC,iNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAA8B;;yEAInD;;8DACE,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;8BAS7C,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,SAAS;wBAAc,UAAU;kCAAW;;;;;;;;;;;;;;;;;;;;;;AAOhF", "debugId": null}}, {"offset": {"line": 1143, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/components/auth/re-auth-manager.tsx"], "sourcesContent": ["\"use client\"\n\nimport React, { useState, useEffect } from 'react';\nimport { ReAuthModal } from './re-auth-modal';\n\ninterface ReAuthRequest {\n  resolve: (success: boolean) => void;\n  title?: string;\n  description?: string;\n}\n\nexport function ReAuthManager() {\n  const [showModal, setShowModal] = useState(false);\n  const [currentRequest, setCurrentRequest] = useState<ReAuthRequest | null>(null);\n\n  useEffect(() => {\n    const handleReAuthRequest = (event: CustomEvent<ReAuthRequest>) => {\n      setCurrentRequest(event.detail);\n      setShowModal(true);\n    };\n\n    window.addEventListener('request-reauth', handleReAuthRequest as EventListener);\n    return () => window.removeEventListener('request-reauth', handleReAuthRequest as EventListener);\n  }, []);\n\n  const handleSuccess = () => {\n    // Dispatch success event for the context\n    window.dispatchEvent(new CustomEvent('reauth-success'));\n    \n    // Resolve the current request\n    if (currentRequest) {\n      currentRequest.resolve(true);\n    }\n    \n    // Clean up\n    setShowModal(false);\n    setCurrentRequest(null);\n  };\n\n  const handleCancel = () => {\n    // Resolve the current request with failure\n    if (currentRequest) {\n      currentRequest.resolve(false);\n    }\n    \n    // Clean up\n    setShowModal(false);\n    setCurrentRequest(null);\n  };\n\n  return (\n    <ReAuthModal\n      open={showModal}\n      onOpenChange={setShowModal}\n      onSuccess={handleSuccess}\n      onCancel={handleCancel}\n      title={currentRequest?.title}\n      description={currentRequest?.description}\n    />\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAWO,SAAS;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IAE3E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,sBAAsB,CAAC;YAC3B,kBAAkB,MAAM,MAAM;YAC9B,aAAa;QACf;QAEA,OAAO,gBAAgB,CAAC,kBAAkB;QAC1C,OAAO,IAAM,OAAO,mBAAmB,CAAC,kBAAkB;IAC5D,GAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,yCAAyC;QACzC,OAAO,aAAa,CAAC,IAAI,YAAY;QAErC,8BAA8B;QAC9B,IAAI,gBAAgB;YAClB,eAAe,OAAO,CAAC;QACzB;QAEA,WAAW;QACX,aAAa;QACb,kBAAkB;IACpB;IAEA,MAAM,eAAe;QACnB,2CAA2C;QAC3C,IAAI,gBAAgB;YAClB,eAAe,OAAO,CAAC;QACzB;QAEA,WAAW;QACX,aAAa;QACb,kBAAkB;IACpB;IAEA,qBACE,8OAAC,iJAAA,CAAA,cAAW;QACV,MAAM;QACN,cAAc;QACd,WAAW;QACX,UAAU;QACV,OAAO,gBAAgB;QACvB,aAAa,gBAAgB;;;;;;AAGnC", "debugId": null}}, {"offset": {"line": 1203, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/components/ui/sonner.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useTheme } from \"next-themes\"\nimport { Toaster as Son<PERSON>, ToasterP<PERSON> } from \"sonner\"\n\nconst Toaster = ({ ...props }: ToasterProps) => {\n  const { theme = \"system\" } = useTheme()\n\n  return (\n    <Sonner\n      theme={theme as ToasterProps[\"theme\"]}\n      className=\"toaster group\"\n      style={\n        {\n          \"--normal-bg\": \"var(--popover)\",\n          \"--normal-text\": \"var(--popover-foreground)\",\n          \"--normal-border\": \"var(--border)\",\n        } as React.CSSProperties\n      }\n      {...props}\n    />\n  )\n}\n\nexport { Toaster }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,UAAU,CAAC,EAAE,GAAG,OAAqB;IACzC,MAAM,EAAE,QAAQ,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IAEpC,qBACE,8OAAC,wIAAA,CAAA,UAAM;QACL,OAAO;QACP,WAAU;QACV,OACE;YACE,eAAe;YACf,iBAAiB;YACjB,mBAAmB;QACrB;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}]}