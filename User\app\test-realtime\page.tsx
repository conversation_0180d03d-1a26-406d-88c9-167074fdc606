"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { RefreshCw, Clock, CheckCircle2, AlertCircle, Info, User, Building, FileText } from "lucide-react"
import { useAuth } from "@/contexts/shopify-auth-context"
import { motion } from "framer-motion"

export default function TestRealTimePage() {
  const { user, isRefreshing, lastUpdated, refreshUserData, enableRealTimeUpdates } = useAuth()
  const [realTimeEnabled, setRealTimeEnabled] = useState(true)
  const [statusHistory, setStatusHistory] = useState<Array<{
    timestamp: string
    event: string
    data: any
  }>>([])

  // Listen for real-time user data updates
  useEffect(() => {
    const handleUserDataUpdate = (event: CustomEvent) => {
      const updatedData = event.detail
      
      setStatusHistory(prev => [{
        timestamp: new Date().toLocaleTimeString(),
        event: 'User Data Updated',
        data: {
          email: updatedData.email,
          isGstVerified: updatedData.isGstVerified,
          businessName: updatedData.businessName,
          gstNumber: updatedData.gstNumber
        }
      }, ...prev.slice(0, 9)]) // Keep last 10 events
    }

    window.addEventListener('user-data-updated', handleUserDataUpdate as EventListener)
    
    return () => {
      window.removeEventListener('user-data-updated', handleUserDataUpdate as EventListener)
    }
  }, [])

  const formatLastUpdated = (date: Date | null) => {
    if (!date) return 'Never'
    const now = new Date()
    const diff = now.getTime() - date.getTime()
    const seconds = Math.floor(diff / 1000)
    const minutes = Math.floor(seconds / 60)
    
    if (seconds < 30) return 'Just now'
    if (seconds < 60) return `${seconds}s ago`
    if (minutes < 60) return `${minutes}m ago`
    const hours = Math.floor(minutes / 60)
    if (hours < 24) return `${hours}h ago`
    return date.toLocaleDateString()
  }

  const toggleRealTime = () => {
    const newState = !realTimeEnabled
    setRealTimeEnabled(newState)
    enableRealTimeUpdates(newState)
    
    setStatusHistory(prev => [{
      timestamp: new Date().toLocaleTimeString(),
      event: `Real-time Updates ${newState ? 'Enabled' : 'Disabled'}`,
      data: { enabled: newState }
    }, ...prev.slice(0, 9)])
  }

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Real-time User Data Test</h1>
        <p className="text-neutral-600">
          This page demonstrates real-time updates for Business Profile and Account Status.
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Real-time Controls */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Real-time Controls
              <div className="flex items-center space-x-2">
                {lastUpdated && (
                  <div className="flex items-center text-xs text-neutral-500">
                    <Clock className="h-3 w-3 mr-1" />
                    {formatLastUpdated(lastUpdated)}
                  </div>
                )}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={refreshUserData}
                  disabled={isRefreshing}
                  className="h-8 w-8 p-0"
                >
                  <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
                </Button>
              </div>
            </CardTitle>
            <CardDescription>
              Control real-time data updates and manual refresh
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Real-time Updates</span>
              <Button
                variant={realTimeEnabled ? "default" : "outline"}
                size="sm"
                onClick={toggleRealTime}
              >
                {realTimeEnabled ? "Enabled" : "Disabled"}
              </Button>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Manual Refresh</span>
              <Button
                variant="outline"
                size="sm"
                onClick={refreshUserData}
                disabled={isRefreshing}
              >
                {isRefreshing ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Refreshing...
                  </>
                ) : (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Refresh Now
                  </>
                )}
              </Button>
            </div>

            <div className="pt-4 border-t">
              <h4 className="text-sm font-medium mb-2">Status</h4>
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Polling Status:</span>
                  <Badge variant={realTimeEnabled ? "default" : "secondary"}>
                    {realTimeEnabled ? "Active" : "Inactive"}
                  </Badge>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span>Last Update:</span>
                  <span className="text-neutral-600">
                    {formatLastUpdated(lastUpdated)}
                  </span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span>Refreshing:</span>
                  <Badge variant={isRefreshing ? "destructive" : "outline"}>
                    {isRefreshing ? "Yes" : "No"}
                  </Badge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Current User Status */}
        <Card>
          <CardHeader>
            <CardTitle>Current User Status</CardTitle>
            <CardDescription>
              Real-time view of user account status
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <div className="flex items-center">
                  <User className="h-4 w-4 mr-2 text-neutral-500" />
                  <span className="text-sm">User</span>
                </div>
                <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                  {user ? "Authenticated" : "Not Authenticated"}
                </Badge>
              </div>
              
              <div className="flex justify-between items-center">
                <div className="flex items-center">
                  <Building className="h-4 w-4 mr-2 text-neutral-500" />
                  <span className="text-sm">Business</span>
                </div>
                <motion.div
                  key={user?.businessName}
                  initial={{ scale: 0.8, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ duration: 0.2 }}
                >
                  {user?.businessName ? (
                    <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                      <CheckCircle2 className="h-3 w-3 mr-1" />
                      Registered
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="bg-neutral-100 text-neutral-700 border-neutral-200">
                      <Info className="h-3 w-3 mr-1" />
                      Not Added
                    </Badge>
                  )}
                </motion.div>
              </div>
              
              <div className="flex justify-between items-center">
                <div className="flex items-center">
                  <FileText className="h-4 w-4 mr-2 text-neutral-500" />
                  <span className="text-sm">GST</span>
                </div>
                <motion.div
                  key={`${user?.isGstVerified}-${user?.gstNumber}`}
                  initial={{ scale: 0.8, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ duration: 0.2 }}
                >
                  {user?.isGstVerified ? (
                    <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                      <CheckCircle2 className="h-3 w-3 mr-1" />
                      Verified
                    </Badge>
                  ) : user?.gstNumber ? (
                    <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
                      <AlertCircle className="h-3 w-3 mr-1" />
                      Pending
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="bg-neutral-100 text-neutral-700 border-neutral-200">
                      <Info className="h-3 w-3 mr-1" />
                      Not Added
                    </Badge>
                  )}
                </motion.div>
              </div>

              {user && (
                <div className="pt-4 border-t">
                  <h4 className="text-sm font-medium mb-2">User Details</h4>
                  <div className="space-y-1 text-sm text-neutral-600">
                    <div>Email: {user.email}</div>
                    <div>Name: {user.firstName} {user.lastName}</div>
                    {user.businessName && <div>Business: {user.businessName}</div>}
                    {user.gstNumber && <div>GST: {user.gstNumber}</div>}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Event History */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>Real-time Event History</CardTitle>
            <CardDescription>
              Live log of data updates and system events
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {statusHistory.length === 0 ? (
                <div className="text-center text-neutral-500 py-8">
                  No events yet. Try refreshing data or toggling real-time updates.
                </div>
              ) : (
                statusHistory.map((event, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.2 }}
                    className="flex items-start space-x-3 p-3 bg-neutral-50 rounded-lg"
                  >
                    <div className="text-xs text-neutral-500 min-w-[60px]">
                      {event.timestamp}
                    </div>
                    <div className="flex-1">
                      <div className="text-sm font-medium">{event.event}</div>
                      <div className="text-xs text-neutral-600 mt-1">
                        {JSON.stringify(event.data, null, 2)}
                      </div>
                    </div>
                  </motion.div>
                ))
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
