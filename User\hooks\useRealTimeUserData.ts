import { useState, useEffect, useCallback, useRef } from 'react'
import { UserData } from '@/lib/auth'

interface RealTimeUserData extends UserData {
  lastUpdated?: string
  isRefreshing?: boolean
}

interface UseRealTimeUserDataOptions {
  pollingInterval?: number // in milliseconds
  enablePolling?: boolean
  onDataUpdate?: (data: RealTimeUserData) => void
  onError?: (error: string) => void
}

export function useRealTimeUserData(
  initialData: UserData | null,
  options: UseRealTimeUserDataOptions = {}
) {
  const {
    pollingInterval = 30000, // 30 seconds default
    enablePolling = true,
    onDataUpdate,
    onError
  } = options

  const [userData, setUserData] = useState<RealTimeUserData | null>(initialData)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)
  
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const abortControllerRef = useRef<AbortController | null>(null)

  // Function to fetch fresh user data from the server
  const fetchUserData = useCallback(async (showLoading = false) => {
    if (showLoading) {
      setIsRefreshing(true)
    }
    setError(null)

    // Check and migrate legacy token keys before making API call
    if (typeof window !== 'undefined') {
      const legacyToken = localStorage.getItem('SHOPIFY_CUSTOMER_ACCESS_TOKEN')
      const currentToken = localStorage.getItem('shopify_customer_access_token')

      if (legacyToken && !currentToken) {
        console.log('🔄 [useRealTimeUserData] Migrating legacy token key')
        localStorage.setItem('shopify_customer_access_token', legacyToken)

        const legacyExpires = localStorage.getItem('SHOPIFY_CUSTOMER_ACCESS_TOKEN_EXPIRES')
        if (legacyExpires) {
          localStorage.setItem('shopify_customer_access_token_expires', legacyExpires)
          localStorage.removeItem('SHOPIFY_CUSTOMER_ACCESS_TOKEN_EXPIRES')
        }

        localStorage.removeItem('SHOPIFY_CUSTOMER_ACCESS_TOKEN')
        console.log('✅ [useRealTimeUserData] Token migration completed')
      }
    }

    // Cancel any ongoing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }

    // Create new abort controller for this request
    abortControllerRef.current = new AbortController()

    try {
      // Get the customer access token from localStorage
      const customerAccessToken = localStorage.getItem('shopify_customer_access_token');

      if (!customerAccessToken) {
        throw new Error('No customer access token found');
      }

      const response = await fetch('/api/current-user', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'x-customer-access-token': customerAccessToken,
        },
        signal: abortControllerRef.current.signal
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const freshData = await response.json()
      
      if (freshData.error) {
        throw new Error(freshData.error)
      }

      const updatedData: RealTimeUserData = {
        ...freshData,
        lastUpdated: new Date().toISOString(),
        isRefreshing: false
      }

      setUserData(updatedData)
      setLastUpdated(new Date())
      
      // Update localStorage with fresh data
      localStorage.setItem('benzochem_user', JSON.stringify(updatedData))
      
      // Call onDataUpdate callback if provided
      if (onDataUpdate) {
        onDataUpdate(updatedData)
      }

      console.log('✅ User data refreshed successfully', {
        email: updatedData.email,
        isGstVerified: updatedData.isGstVerified,
        businessName: updatedData.businessName
      })

    } catch (error: any) {
      if (error.name === 'AbortError') {
        console.log('Request was aborted')
        return
      }

      const errorMessage = error.message || 'Failed to fetch user data'
      setError(errorMessage)
      
      if (onError) {
        onError(errorMessage)
      }

      console.error('❌ Error fetching user data:', error)
    } finally {
      setIsRefreshing(false)
    }
  }, [onDataUpdate, onError])

  // Manual refresh function
  const refreshUserData = useCallback(() => {
    return fetchUserData(true)
  }, [fetchUserData])

  // Start polling
  const startPolling = useCallback(() => {
    if (!enablePolling) return

    // Clear existing interval
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current)
    }

    // Set up new polling interval
    pollingIntervalRef.current = setInterval(() => {
      fetchUserData(false) // Don't show loading for background polls
    }, pollingInterval)

    console.log(`🔄 Started polling user data every ${pollingInterval / 1000} seconds`)
  }, [enablePolling, pollingInterval, fetchUserData])

  // Stop polling
  const stopPolling = useCallback(() => {
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current)
      pollingIntervalRef.current = null
      console.log('⏹️ Stopped polling user data')
    }
  }, [])

  // Effect to handle polling lifecycle
  useEffect(() => {
    if (enablePolling && userData) {
      startPolling()
    }

    return () => {
      stopPolling()
      // Cancel any ongoing request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, [enablePolling, userData, startPolling, stopPolling])

  // Effect to handle visibility change (pause polling when tab is not visible)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        stopPolling()
      } else if (enablePolling && userData) {
        startPolling()
        // Refresh data when tab becomes visible again
        fetchUserData(false)
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [enablePolling, userData, startPolling, stopPolling, fetchUserData])

  return {
    userData,
    isRefreshing,
    error,
    lastUpdated,
    refreshUserData,
    startPolling,
    stopPolling
  }
}
