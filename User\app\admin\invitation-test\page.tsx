"use client"

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'

export default function InvitationTestPage() {
  const [email, setEmail] = useState('')
  const [firstName, setFirstName] = useState('')
  const [lastName, setLastName] = useState('')
  const [password, setPassword] = useState('')
  const [phone, setPhone] = useState('')
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<any>(null)

  const handleCreateCustomer = async () => {
    if (!email || !firstName || !lastName || !password) {
      setResult({
        success: false,
        message: 'Please fill in all required fields'
      })
      return
    }

    setLoading(true)
    setResult(null)

    try {
      const response = await fetch('/api/shopify/send-customer-invite', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email,
          firstName,
          lastName,
          password,
          phone
        })
      })

      const data = await response.json()
      setResult(data)
    } catch (error) {
      setResult({
        success: false,
        message: 'Network error occurred'
      })
    } finally {
      setLoading(false)
    }
  }

  const handleResendInvitation = async () => {
    if (!email) {
      setResult({
        success: false,
        message: 'Please enter an email address'
      })
      return
    }

    setLoading(true)
    setResult(null)

    try {
      const response = await fetch('/api/shopify/resend-invitation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ email })
      })

      const data = await response.json()
      setResult(data)
    } catch (error) {
      setResult({
        success: false,
        message: 'Network error occurred'
      })
    } finally {
      setLoading(false)
    }
  }

  const generateTestEmail = () => {
    const timestamp = Date.now()
    setEmail(`test-${timestamp}@example.com`)
    setFirstName('Test')
    setLastName('Customer')
    setPassword('TestPassword123!')
    setPhone('+**********')
  }

  return (
    <div className="container mx-auto py-8 max-w-2xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Customer Account Test</h1>
        <p className="text-muted-foreground">
          Test the customer account creation with welcome email functionality. Accounts are activated immediately and customers receive a welcome email instead of an invitation.
        </p>
      </div>

      <div className="space-y-6">
        {/* Create New Customer */}
        <Card>
          <CardHeader>
            <CardTitle>Create New Customer with Welcome Email</CardTitle>
            <CardDescription>
              Create a new customer account that is immediately activated and send the "Customer Account Welcome" email
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium">First Name *</label>
                <Input
                  value={firstName}
                  onChange={(e) => setFirstName(e.target.value)}
                  placeholder="Enter first name"
                />
              </div>
              <div>
                <label className="text-sm font-medium">Last Name *</label>
                <Input
                  value={lastName}
                  onChange={(e) => setLastName(e.target.value)}
                  placeholder="Enter last name"
                />
              </div>
            </div>
            
            <div>
              <label className="text-sm font-medium">Email *</label>
              <Input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter email address"
              />
            </div>
            
            <div>
              <label className="text-sm font-medium">Password *</label>
              <Input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter password"
              />
            </div>
            
            <div>
              <label className="text-sm font-medium">Phone</label>
              <Input
                value={phone}
                onChange={(e) => setPhone(e.target.value)}
                placeholder="Enter phone number"
              />
            </div>

            <div className="flex gap-2">
              <Button 
                onClick={handleCreateCustomer} 
                disabled={loading}
                className="flex-1"
              >
                {loading ? 'Creating...' : 'Create Customer & Send Welcome Email'}
              </Button>
              <Button 
                variant="outline" 
                onClick={generateTestEmail}
                disabled={loading}
              >
                Generate Test Data
              </Button>
            </div>
          </CardContent>
        </Card>

        <Separator />

        {/* Resend Invitation */}
        <Card>
          <CardHeader>
            <CardTitle>Send Password Reset Email</CardTitle>
            <CardDescription>
              Send a password reset email to an existing customer (since accounts are activated immediately)
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium">Email Address</label>
              <Input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter customer email address"
              />
            </div>
            
            <Button 
              onClick={handleResendInvitation} 
              disabled={loading || !email}
              className="w-full"
              variant="secondary"
            >
              {loading ? 'Sending...' : 'Send Password Reset Email'}
            </Button>
          </CardContent>
        </Card>

        {/* Results */}
        {result && (
          <Card>
            <CardHeader>
              <CardTitle>Result</CardTitle>
            </CardHeader>
            <CardContent>
              <Alert className={result.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
                <AlertDescription>
                  <div className="space-y-2">
                    <div className="font-medium">
                      {result.success ? '✅ Success' : '❌ Error'}
                    </div>
                    <div>{result.message}</div>
                    {result.error && (
                      <div className="text-sm text-muted-foreground">
                        Error Code: {result.error}
                      </div>
                    )}
                    {result.customer && (
                      <div className="text-sm text-muted-foreground">
                        Customer ID: {result.customer.id}
                      </div>
                    )}
                    {result.activationUrl && (
                      <div className="text-sm">
                        <div className="font-medium">Activation URL:</div>
                        <div className="break-all bg-gray-100 p-2 rounded text-xs">
                          {result.activationUrl}
                        </div>
                      </div>
                    )}
                  </div>
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        )}

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>Instructions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2 text-sm text-muted-foreground">
            <div>1. Use "Generate Test Data" to create sample customer information</div>
            <div>2. Click "Create Customer & Send Welcome Email" to create a new customer</div>
            <div>3. Check your email inbox (including spam folder) for the welcome email</div>
            <div>4. The email subject should be "Customer account welcome"</div>
            <div>5. The account is immediately activated and you can log in right away</div>
            <div>6. Use "Send Password Reset Email" if you need to reset password for existing customers</div>
            <div>7. If emails are not being sent, check Shopify Admin > Settings > Notifications</div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
