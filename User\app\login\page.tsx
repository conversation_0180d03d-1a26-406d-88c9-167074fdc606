import type { <PERSON>ada<PERSON> } from "next"
import Link from "next/link"
import ShopifyLoginForm from "@/components/shopify-login-form"
import { ArrowRight } from "lucide-react"
import Image from "next/image"

export const metadata: Metadata = {
  title: "Login | Benzochem Industries",
  description: "Login to your Benzochem Industries account",
}

export default function LoginPage() {
  return (
    <main className="flex min-h-screen flex-col pt-16 bg-[#FAFAFA]">
      <section className="flex-1 flex items-stretch overflow-hidden">
        <div className="w-full max-w-6xl mx-auto my-8 rounded-2xl shadow-xl overflow-hidden flex bg-white">
          {/* Left side - Image and branding */}
          <div className="hidden md:block w-1/2 bg-gradient-to-br from-teal-600 to-teal-800 p-8 text-white relative overflow-hidden">
            <div className="absolute inset-0 opacity-50">
              <Image 
                src="/images/login.png" 
                alt="Chemistry laboratory" 
                fill 
                className="object-cover"
                priority
              />
            </div>
            
            <div className="relative z-10 h-full flex flex-col">
              <div className="mt-auto">
                <h3 className="text-3xl md:text-4xl font-light mb-4">Delivering Quality<br />Creating Value</h3>
                <p className="text-teal-100 opacity-80 max-w-md">
                  Access your account to manage orders, track shipments, and explore our complete product catalog.
                </p>
                
                <div className="mt-8 flex space-x-2">
                  <span className="w-2 h-2 rounded-full bg-white opacity-60"></span>
                  <span className="w-2 h-2 rounded-full bg-white opacity-60"></span>
                  <span className="w-6 h-2 rounded-full bg-white"></span>
                </div>
              </div>
            </div>
          </div>
          
          {/* Right side - Login form */}
          <div className="w-full md:w-1/2 p-6 md:p-12 flex flex-col">
            <div className="text-center md:text-left mb-8">
              <h2 className="text-2xl font-semibold text-gray-800 mb-2">Welcome back</h2>
              <p className="text-gray-500 text-sm">Sign in to your account</p>
            </div>

            <div className="flex-1 flex items-center">
              <div className="w-full">
                <ShopifyLoginForm />
              </div>
            </div>

            <div className="mt-8 text-center">
              <p className="text-neutral-500 font-light text-sm">
                Don't have an account?{" "}
                <Link
                  href="/register"
                  className="text-teal-600 hover:text-teal-700 font-medium inline-flex items-center transition-all duration-300 border-b border-transparent hover:border-teal-600"
                >
                  Register <ArrowRight className="h-3 w-3 ml-1 transition-transform group-hover:translate-x-0.5" />
                </Link>
              </p>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}
