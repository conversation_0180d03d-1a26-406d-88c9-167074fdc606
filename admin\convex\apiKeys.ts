import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Query to get all API keys
export const getApiKeys = query({
  args: {
    search: v.optional(v.string()),
    isActive: v.optional(v.boolean()),
    createdBy: v.optional(v.id("admins")),
    limit: v.optional(v.number()),
    offset: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    let query = ctx.db.query("apiKeys");

    // Filter by active status if provided
    if (args.isActive !== undefined) {
      query = query.filter((q) => q.eq(q.field("isActive"), args.isActive));
    }

    // Filter by creator if provided
    if (args.createdBy) {
      query = query.filter((q) => q.eq(q.field("createdBy"), args.createdBy));
    }

    // Collect all results first
    const allApiKeys = await query.collect();

    // Apply search filter
    let filteredKeys = allApiKeys;
    if (args.search) {
      filteredKeys = allApiKeys.filter(key =>
        key.name.toLowerCase().includes(args.search!.toLowerCase()) ||
        key.key.toLowerCase().includes(args.search!.toLowerCase())
      );
    }

    // Sort by creation date (newest first)
    filteredKeys.sort((a, b) => b.createdAt - a.createdAt);

    // Apply pagination
    const offset = args.offset || 0;
    const limit = args.limit || 50;
    const apiKeys = filteredKeys.slice(offset, offset + limit);

    // Return the full key for admin dashboard (they need to copy it)
    return apiKeys;
  },
});

// Query to get API key by ID (without exposing the actual key)
export const getApiKeyById = query({
  args: { id: v.id("apiKeys") },
  handler: async (ctx, args) => {
    const apiKey = await ctx.db.get(args.id);
    if (!apiKey) return null;
    
    // Don't return the actual key value for security
    return {
      ...apiKey,
      key: apiKey.key.substring(0, 8) + "..."
    };
  },
});

// Query to validate API key (internal use)
export const validateApiKey = query({
  args: { key: v.string() },
  handler: async (ctx, args) => {
    const apiKey = await ctx.db
      .query("apiKeys")
      .withIndex("by_key", (q) => q.eq("key", args.key))
      .first();
    
    if (!apiKey) return null;
    
    // Check if key is active
    if (!apiKey.isActive) return null;
    
    // Check if key has expired
    if (apiKey.expiresAt && apiKey.expiresAt < Date.now()) {
      return null;
    }
    
    return {
      id: apiKey._id,
      name: apiKey.name,
      permissions: apiKey.permissions,
      rateLimit: apiKey.rateLimit,
    };
  },
});

// Mutation to create a new API key
export const createApiKey = mutation({
  args: {
    name: v.string(),
    permissions: v.array(v.string()),
    adminId: v.id("admins"),
    expiresAt: v.optional(v.number()),
    rateLimit: v.optional(v.object({
      requestsPerMinute: v.number(),
      requestsPerHour: v.number(),
      requestsPerDay: v.number(),
    })),
  },
  handler: async (ctx, args) => {
    // Generate a secure API key
    const apiKey = generateApiKey();
    const now = Date.now();

    const apiKeyId = await ctx.db.insert("apiKeys", {
      name: args.name,
      key: apiKey, // In production, this should be hashed
      permissions: args.permissions,
      isActive: true,
      expiresAt: args.expiresAt,
      usageCount: 0,
      rateLimit: args.rateLimit || {
        requestsPerMinute: 100,
        requestsPerHour: 5000,
        requestsPerDay: 50000,
      },
      createdBy: args.adminId,
      createdAt: now,
      updatedAt: now,
    });

    // Log the activity
    await ctx.db.insert("activityLogs", {
      action: "api_key_created",
      entityType: "api_key",
      entityId: apiKeyId,
      newValues: {
        name: args.name,
        permissions: args.permissions,
        expiresAt: args.expiresAt,
      },
      performedBy: args.adminId,
      performedByType: "admin",
      createdAt: now,
    });

    // Return the API key only once (for the user to copy)
    return {
      id: apiKeyId,
      key: apiKey,
      name: args.name,
      permissions: args.permissions,
    };
  },
});

// Mutation to update API key
export const updateApiKey = mutation({
  args: {
    apiKeyId: v.id("apiKeys"),
    name: v.optional(v.string()),
    permissions: v.optional(v.array(v.string())),
    isActive: v.optional(v.boolean()),
    expiresAt: v.optional(v.number()),
    rateLimit: v.optional(v.object({
      requestsPerMinute: v.number(),
      requestsPerHour: v.number(),
      requestsPerDay: v.number(),
    })),
    updatedBy: v.id("admins"),
  },
  handler: async (ctx, args) => {
    const apiKey = await ctx.db.get(args.apiKeyId);
    if (!apiKey) {
      throw new Error("API key not found");
    }

    const now = Date.now();
    const updates: any = { updatedAt: now };
    
    if (args.name !== undefined) updates.name = args.name;
    if (args.permissions !== undefined) updates.permissions = args.permissions;
    if (args.isActive !== undefined) updates.isActive = args.isActive;
    if (args.expiresAt !== undefined) updates.expiresAt = args.expiresAt;
    if (args.rateLimit !== undefined) updates.rateLimit = args.rateLimit;
    
    await ctx.db.patch(args.apiKeyId, updates);

    // Log the activity
    await ctx.db.insert("activityLogs", {
      action: "api_key_updated",
      entityType: "api_key",
      entityId: args.apiKeyId,
      oldValues: {
        name: apiKey.name,
        permissions: apiKey.permissions,
        isActive: apiKey.isActive,
        expiresAt: apiKey.expiresAt,
        rateLimit: apiKey.rateLimit,
      },
      newValues: updates,
      performedBy: args.updatedBy,
      performedByType: "admin",
      createdAt: now,
    });

    return args.apiKeyId;
  },
});

// Mutation to revoke API key
export const revokeApiKey = mutation({
  args: {
    apiKeyId: v.id("apiKeys"),
    revokedBy: v.id("admins"),
  },
  handler: async (ctx, args) => {
    const apiKey = await ctx.db.get(args.apiKeyId);
    if (!apiKey) {
      throw new Error("API key not found");
    }

    const now = Date.now();
    
    await ctx.db.patch(args.apiKeyId, {
      isActive: false,
      updatedAt: now,
    });

    // Log the activity
    await ctx.db.insert("activityLogs", {
      action: "api_key_revoked",
      entityType: "api_key",
      entityId: args.apiKeyId,
      oldValues: { isActive: apiKey.isActive },
      newValues: { isActive: false },
      performedBy: args.revokedBy,
      performedByType: "admin",
      createdAt: now,
    });

    return args.apiKeyId;
  },
});

// Mutation to update API key usage
export const updateApiKeyUsage = mutation({
  args: {
    key: v.string(),
  },
  handler: async (ctx, args) => {
    const apiKey = await ctx.db
      .query("apiKeys")
      .withIndex("by_key", (q) => q.eq("key", args.key))
      .first();
    
    if (!apiKey) return null;
    
    const now = Date.now();
    
    await ctx.db.patch(apiKey._id, {
      usageCount: apiKey.usageCount + 1,
      lastUsedAt: now,
      updatedAt: now,
    });

    return apiKey._id;
  },
});

// Query to get API key statistics
export const getApiKeyStats = query({
  args: {},
  handler: async (ctx) => {
    const allApiKeys = await ctx.db.query("apiKeys").collect();
    const now = Date.now();
    
    const stats = {
      total: allApiKeys.length,
      active: allApiKeys.filter(k => k.isActive === true).length,
      inactive: allApiKeys.filter(k => k.isActive === false).length,
      expired: allApiKeys.filter(k => k.expiresAt && k.expiresAt < now).length,
      recentlyUsed: allApiKeys.filter(k => 
        k.lastUsedAt && k.lastUsedAt > now - (24 * 60 * 60 * 1000) // Last 24 hours
      ).length,
      totalUsage: allApiKeys.reduce((sum, k) => sum + k.usageCount, 0),
    };
    
    return stats;
  },
});

// Activity Logs functions

// Query to get activity logs
export const getActivityLogs = query({
  args: {
    entityType: v.optional(v.union(
      v.literal("user"),
      v.literal("product"),
      v.literal("admin"),
      v.literal("setting"),
      v.literal("api_key")
    )),
    entityId: v.optional(v.string()),
    performedBy: v.optional(v.union(v.id("admins"), v.id("users"))),
    action: v.optional(v.string()),
    startDate: v.optional(v.number()),
    endDate: v.optional(v.number()),
    limit: v.optional(v.number()),
    offset: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    let query = ctx.db.query("activityLogs");
    
    // Filter by entity type if provided
    if (args.entityType) {
      query = query.filter((q) => q.eq(q.field("entityType"), args.entityType));
    }
    
    // Filter by entity ID if provided
    if (args.entityId) {
      query = query.filter((q) => q.eq(q.field("entityId"), args.entityId));
    }
    
    // Filter by performer if provided
    if (args.performedBy) {
      query = query.filter((q) => q.eq(q.field("performedBy"), args.performedBy));
    }
    
    // Filter by action if provided
    if (args.action) {
      query = query.filter((q) => q.eq(q.field("action"), args.action));
    }
    
    // Collect all results first
    const allLogs = await query.collect();

    // Filter by date range if provided
    let filteredLogs = allLogs;
    if (args.startDate) {
      filteredLogs = filteredLogs.filter(log => log.createdAt >= args.startDate!);
    }
    if (args.endDate) {
      filteredLogs = filteredLogs.filter(log => log.createdAt <= args.endDate!);
    }

    // Sort by creation date (newest first)
    filteredLogs.sort((a, b) => b.createdAt - a.createdAt);

    // Apply pagination
    const offset = args.offset || 0;
    const limit = args.limit || 100;
    const logs = filteredLogs.slice(offset, offset + limit);
    
    return logs;
  },
});

// Query to get activity log statistics
export const getActivityLogStats = query({
  args: {
    startDate: v.optional(v.number()),
    endDate: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    // Get all activity logs first
    const allLogs = await ctx.db.query("activityLogs").collect();

    // Filter by date range if provided
    let logs = allLogs;
    if (args.startDate) {
      logs = logs.filter(log => log.createdAt >= args.startDate!);
    }
    if (args.endDate) {
      logs = logs.filter(log => log.createdAt <= args.endDate!);
    }
    
    const stats = {
      total: logs.length,
      byEntityType: {
        user: logs.filter(l => l.entityType === "user").length,
        product: logs.filter(l => l.entityType === "product").length,
        admin: logs.filter(l => l.entityType === "admin").length,
        setting: logs.filter(l => l.entityType === "setting").length,
        api_key: logs.filter(l => l.entityType === "api_key").length,
      },
      byPerformerType: {
        admin: logs.filter(l => l.performedByType === "admin").length,
        user: logs.filter(l => l.performedByType === "user").length,
        system: logs.filter(l => l.performedByType === "system").length,
      },
      recentActivity: logs.filter(l => 
        l.createdAt > Date.now() - (24 * 60 * 60 * 1000) // Last 24 hours
      ).length,
    };
    
    return stats;
  },
});

// Helper function to generate API key
function generateApiKey(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = 'bz_'; // Prefix for Benzochem
  for (let i = 0; i < 32; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}
