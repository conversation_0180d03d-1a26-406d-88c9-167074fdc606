import { NextResponse } from "next/server"
import crypto from "crypto"

// Verify Shopify webhook signature
function verifyShopifyWebhook(request: Request, body: string): boolean {
  const hmacHeader = request.headers.get("x-shopify-hmac-sha256")

  if (!hmacHeader) return false

  const hmac = crypto.createHmac("sha256", process.env.SHOPIFY_WEBHOOK_SECRET || "")
  hmac.update(body)
  const digest = hmac.digest("base64")

  return crypto.timingSafeEqual(Buffer.from(hmacHeader), Buffer.from(digest))
}

export async function POST(request: Request) {
  try {
    // Get the raw body
    const body = await request.text()

    // Verify the webhook signature
    if (!verifyShopifyWebhook(request, body)) {
      return NextResponse.json({ error: "Invalid webhook signature" }, { status: 401 })
    }

    // Get the webhook topic
    const topic = request.headers.get("x-shopify-topic")

    // Parse the body
    const data = JSON.parse(body)

    // Handle different webhook topics
    switch (topic) {
      case "products/create":
      case "products/update":
        // Handle product updates
        console.log("Product updated:", data.id)
        break

      case "orders/create":
        // Handle new orders
        console.log("New order:", data.id)
        break

      case "customers/create":
      case "customers/update":
        // Handle customer updates
        console.log("Customer updated:", data.id)
        break

      default:
        console.log("Unhandled webhook topic:", topic)
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error processing Shopify webhook:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
