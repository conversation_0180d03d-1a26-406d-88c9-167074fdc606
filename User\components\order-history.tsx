"use client"

import { motion } from "framer-motion"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Eye, Download, ShoppingBag } from "lucide-react" // Added ShoppingBag for empty state
import { Skeleton } from "@/components/ui/skeleton" // Added
import { useState, useEffect } from "react" // Added

// Skeleton for a single order history row
function OrderHistoryRowSkeleton() {
  return (
    <tr>
      <td className="px-6 py-4 whitespace-nowrap"><Skeleton className="h-5 w-24 rounded" /></td>
      <td className="px-6 py-4 whitespace-nowrap"><Skeleton className="h-5 w-32 rounded" /></td>
      <td className="px-6 py-4 whitespace-nowrap"><Skeleton className="h-6 w-20 rounded-full" /></td>
      <td className="px-6 py-4 whitespace-nowrap"><Skeleton className="h-5 w-16 rounded" /></td>
      <td className="px-6 py-4 whitespace-nowrap"><Skeleton className="h-5 w-8 rounded" /></td>
      <td className="px-6 py-4 whitespace-nowrap text-right space-x-2">
        <Skeleton className="h-8 w-20 rounded-md inline-block" />
        <Skeleton className="h-8 w-24 rounded-md inline-block" />
      </td>
    </tr>
  )
}

// Skeleton for the entire OrderHistory table
function OrderHistoryTableSkeleton() {
  return (
    <div className="overflow-x-auto">
      <table className="w-full">
        <thead className="bg-neutral-50 text-neutral-700">
          <tr>
            <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Order ID</th>
            <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Date</th>
            <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Status</th>
            <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Total</th>
            <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Items</th>
            <th className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">Actions</th>
          </tr>
        </thead>
        <tbody className="divide-y">
          {[...Array(4)].map((_, i) => ( // Show 4 skeleton rows
            <OrderHistoryRowSkeleton key={i} />
          ))}
        </tbody>
      </table>
    </div>
  )
}


export default function OrderHistory() {
  const [orders, setOrders] = useState<any[]>([]) // Start with empty array
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Simulate API call
    setIsLoading(true)
    setTimeout(() => {
      const fetchedOrders = [
        { id: "BZ-29384756", date: "May 15, 2023", status: "Delivered", total: 364.97, items: 3 },
        { id: "BZ-28473625", date: "April 2, 2023", status: "Processing", total: 529.99, items: 2 },
        { id: "BZ-27364512", date: "March 18, 2023", status: "Shipped", total: 189.50, items: 1 },
        { id: "BZ-26253411", date: "February 5, 2023", status: "Delivered", total: 742.25, items: 4 },
      ];
      setOrders(fetchedOrders)
      setIsLoading(false)
    }, 1500); // Simulate 1.5 second delay
  }, [])


  const getStatusColor = (status: string) => {
    switch (status) {
      case "Delivered": return "bg-green-100 text-green-800 hover:bg-green-100";
      case "Processing": return "bg-blue-100 text-blue-800 hover:bg-blue-100";
      case "Shipped": return "bg-purple-100 text-purple-800 hover:bg-purple-100";
      case "Cancelled": return "bg-red-100 text-red-800 hover:bg-red-100";
      default: return "bg-neutral-100 text-neutral-800 hover:bg-neutral-100";
    }
  }

  if (isLoading) {
    return <OrderHistoryTableSkeleton />;
  }

  return (
    <div className="bg-white rounded-lg overflow-hidden">
      {orders.length === 0 ? (
        <div className="p-8 text-center">
           <div className="w-16 h-16 bg-neutral-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <ShoppingBag className="w-8 h-8 text-neutral-500" />
          </div>
          <h3 className="text-lg font-medium mb-2">No Orders Yet</h3>
          <p className="text-neutral-600 mb-6">You haven't placed any orders yet. Start shopping to see your order history here.</p>
          <Button asChild className="bg-teal-600 hover:bg-teal-700 text-white">
            <Link href="/categories/powder">Browse Products</Link>
          </Button>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-neutral-50 text-neutral-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Order ID</th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Date</th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Status</th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Total</th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Items</th>
                <th className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y">
              {orders.map((order, index) => (
                <motion.tr
                  key={order.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <td className="px-6 py-4 whitespace-nowrap font-medium">{order.id}</td>
                  <td className="px-6 py-4 whitespace-nowrap">{order.date}</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <Badge variant="outline" className={`${getStatusColor(order.status)} border-none`}>
                      {order.status}
                    </Badge>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">₹{order.total.toFixed(2)}</td>
                  <td className="px-6 py-4 whitespace-nowrap">{order.items}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">
                    <Button variant="ghost" size="sm" className="mr-2 text-teal-600 hover:text-teal-700">
                      <Eye className="h-4 w-4 mr-1" />
                      View
                    </Button>
                    <Button variant="ghost" size="sm" className="text-neutral-600 hover:text-neutral-700">
                      <Download className="h-4 w-4 mr-1" />
                      Invoice
                    </Button>
                  </td>
                </motion.tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  )
}
