{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/convex/_generated/api.js"], "sourcesContent": ["/* eslint-disable */\n/**\n * Generated `api` utility.\n *\n * THIS CODE IS AUTOMATICALLY GENERATED.\n *\n * To regenerate, run `npx convex dev`.\n * @module\n */\n\nimport { anyApi } from \"convex/server\";\n\n/**\n * A utility for referencing Convex functions in your app's API.\n *\n * Usage:\n * ```js\n * const myFunctionReference = api.myModule.myFunction;\n * ```\n */\nexport const api = anyApi;\nexport const internal = anyApi;\n"], "names": [], "mappings": "AAAA,kBAAkB,GAClB;;;;;;;CAOC;;;;AAED;AAAA;;AAUO,MAAM,MAAM,wJAAA,CAAA,SAAM;AAClB,MAAM,WAAW,wJAAA,CAAA,SAAM", "debugId": null}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/lib/session.ts"], "sourcesContent": ["import { SignJWT, jwtVerify } from 'jose';\nimport { cookies } from 'next/headers';\nimport { NextRequest, NextResponse } from 'next/server';\n\n// Session configuration\nconst SESSION_COOKIE_NAME = 'benzochem-admin-session';\nconst CSRF_COOKIE_NAME = 'benzochem-csrf-token';\nconst SESSION_DURATION = 24 * 60 * 60 * 1000; // 24 hours in milliseconds\nconst CSRF_TOKEN_LENGTH = 32;\n\n// Get JWT secret from environment variable\nfunction getJWTSecret(): Uint8Array {\n  const secret = process.env.JWT_SECRET;\n  if (!secret) {\n    throw new Error('JWT_SECRET environment variable is required');\n  }\n  return new TextEncoder().encode(secret);\n}\n\n// Get session encryption key from environment variable\nfunction getSessionKey(): string {\n  const secret = process.env.SESSION_SECRET;\n  if (!secret) {\n    throw new Error('SESSION_SECRET environment variable is required');\n  }\n  return secret;\n}\n\n// Admin session data interface\nexport interface AdminSession {\n  adminId: string;\n  email: string;\n  firstName: string;\n  lastName: string;\n  role: 'admin' | 'super_admin';\n  permissions: string[];\n  isActive: boolean;\n  loginTime: number;\n  expiresAt: number;\n}\n\n// CSRF token generation\nexport function generateCSRFToken(): string {\n  const array = new Uint8Array(CSRF_TOKEN_LENGTH);\n  crypto.getRandomValues(array);\n  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');\n}\n\n// Create JWT token for session\nexport async function createSessionToken(adminData: Omit<AdminSession, 'loginTime' | 'expiresAt'>): Promise<string> {\n  const now = Date.now();\n  const expiresAt = now + SESSION_DURATION;\n  \n  const payload: AdminSession = {\n    ...adminData,\n    loginTime: now,\n    expiresAt,\n  };\n\n  const token = await new SignJWT(payload)\n    .setProtectedHeader({ alg: 'HS256' })\n    .setIssuedAt(now / 1000)\n    .setExpirationTime(expiresAt / 1000)\n    .setIssuer('benzochem-admin')\n    .setAudience('benzochem-admin-dashboard')\n    .sign(getJWTSecret());\n\n  return token;\n}\n\n// Verify and decode JWT token\nexport async function verifySessionToken(token: string): Promise<AdminSession | null> {\n  try {\n    console.log('verifySessionToken: Verifying token...');\n    const { payload } = await jwtVerify(token, getJWTSecret(), {\n      issuer: 'benzochem-admin',\n      audience: 'benzochem-admin-dashboard',\n    });\n\n    const session = payload as unknown as AdminSession;\n    console.log('verifySessionToken: Token verified, checking expiration...');\n    console.log('verifySessionToken: Session expires at:', new Date(session.expiresAt));\n    console.log('verifySessionToken: Current time:', new Date());\n\n    // Check if session has expired\n    if (session.expiresAt < Date.now()) {\n      console.log('verifySessionToken: Session has expired');\n      return null;\n    }\n\n    console.log('verifySessionToken: Session is valid');\n    return session;\n  } catch (error) {\n    console.error('Session token verification failed:', error);\n    return null;\n  }\n}\n\n// Set secure session cookie\nexport function setSessionCookie(response: NextResponse, token: string): void {\n  const isProduction = process.env.NODE_ENV === 'production';\n  \n  response.cookies.set(SESSION_COOKIE_NAME, token, {\n    httpOnly: true,\n    secure: isProduction, // Only use secure in production (HTTPS)\n    sameSite: 'lax',\n    maxAge: SESSION_DURATION / 1000, // Convert to seconds\n    path: '/',\n  });\n}\n\n// Set CSRF token cookie\nexport function setCSRFCookie(response: NextResponse, csrfToken: string): void {\n  const isProduction = process.env.NODE_ENV === 'production';\n  \n  response.cookies.set(CSRF_COOKIE_NAME, csrfToken, {\n    httpOnly: false, // CSRF token needs to be accessible to client-side JavaScript\n    secure: isProduction,\n    sameSite: 'lax',\n    maxAge: SESSION_DURATION / 1000,\n    path: '/',\n  });\n}\n\n// Get session from request cookies\nexport async function getSessionFromRequest(request: NextRequest): Promise<AdminSession | null> {\n  const token = request.cookies.get(SESSION_COOKIE_NAME)?.value;\n  console.log('getSessionFromRequest: Token found:', !!token);\n\n  if (!token) {\n    console.log('getSessionFromRequest: No token found');\n    return null;\n  }\n\n  const session = await verifySessionToken(token);\n  console.log('getSessionFromRequest: Session verified:', !!session);\n  return session;\n}\n\n// Get session from server-side cookies\nexport async function getServerSession(): Promise<AdminSession | null> {\n  const cookieStore = cookies();\n  const token = cookieStore.get(SESSION_COOKIE_NAME)?.value;\n  \n  if (!token) {\n    return null;\n  }\n\n  return await verifySessionToken(token);\n}\n\n// Clear session cookies\nexport function clearSessionCookies(response: NextResponse): void {\n  response.cookies.delete(SESSION_COOKIE_NAME);\n  response.cookies.delete(CSRF_COOKIE_NAME);\n}\n\n// Validate CSRF token\nexport function validateCSRFToken(request: NextRequest, providedToken: string): boolean {\n  const cookieToken = request.cookies.get(CSRF_COOKIE_NAME)?.value;\n  return cookieToken === providedToken && cookieToken !== undefined;\n}\n\n// Session cookie configuration for iron-session (alternative approach)\nexport const sessionOptions = {\n  cookieName: SESSION_COOKIE_NAME,\n  password: getSessionKey(),\n  cookieOptions: {\n    secure: process.env.NODE_ENV === 'production',\n    httpOnly: true,\n    sameSite: 'lax' as const,\n    maxAge: SESSION_DURATION / 1000,\n  },\n};\n\n// Type for iron-session\ndeclare module 'iron-session' {\n  interface IronSessionData {\n    admin?: AdminSession;\n    csrfToken?: string;\n  }\n}\n\n// Refresh session token (extend expiration)\nexport async function refreshSessionToken(currentToken: string): Promise<string | null> {\n  const session = await verifySessionToken(currentToken);\n  \n  if (!session) {\n    return null;\n  }\n\n  // Create new token with extended expiration\n  const refreshedSession: Omit<AdminSession, 'loginTime' | 'expiresAt'> = {\n    adminId: session.adminId,\n    email: session.email,\n    firstName: session.firstName,\n    lastName: session.lastName,\n    role: session.role,\n    permissions: session.permissions,\n    isActive: session.isActive,\n  };\n\n  return await createSessionToken(refreshedSession);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AAAA;AACA;;;AAGA,wBAAwB;AACxB,MAAM,sBAAsB;AAC5B,MAAM,mBAAmB;AACzB,MAAM,mBAAmB,KAAK,KAAK,KAAK,MAAM,2BAA2B;AACzE,MAAM,oBAAoB;AAE1B,2CAA2C;AAC3C,SAAS;IACP,MAAM,SAAS,QAAQ,GAAG,CAAC,UAAU;IACrC,IAAI,CAAC,QAAQ;QACX,MAAM,IAAI,MAAM;IAClB;IACA,OAAO,IAAI,cAAc,MAAM,CAAC;AAClC;AAEA,uDAAuD;AACvD,SAAS;IACP,MAAM,SAAS,QAAQ,GAAG,CAAC,cAAc;IACzC,IAAI,CAAC,QAAQ;QACX,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAgBO,SAAS;IACd,MAAM,QAAQ,IAAI,WAAW;IAC7B,OAAO,eAAe,CAAC;IACvB,OAAO,MAAM,IAAI,CAAC,OAAO,CAAA,OAAQ,KAAK,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG,MAAM,IAAI,CAAC;AAC5E;AAGO,eAAe,mBAAmB,SAAwD;IAC/F,MAAM,MAAM,KAAK,GAAG;IACpB,MAAM,YAAY,MAAM;IAExB,MAAM,UAAwB;QAC5B,GAAG,SAAS;QACZ,WAAW;QACX;IACF;IAEA,MAAM,QAAQ,MAAM,IAAI,uJAAA,CAAA,UAAO,CAAC,SAC7B,kBAAkB,CAAC;QAAE,KAAK;IAAQ,GAClC,WAAW,CAAC,MAAM,MAClB,iBAAiB,CAAC,YAAY,MAC9B,SAAS,CAAC,mBACV,WAAW,CAAC,6BACZ,IAAI,CAAC;IAER,OAAO;AACT;AAGO,eAAe,mBAAmB,KAAa;IACpD,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,CAAA,GAAA,yJAAA,CAAA,YAAS,AAAD,EAAE,OAAO,gBAAgB;YACzD,QAAQ;YACR,UAAU;QACZ;QAEA,MAAM,UAAU;QAChB,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,2CAA2C,IAAI,KAAK,QAAQ,SAAS;QACjF,QAAQ,GAAG,CAAC,qCAAqC,IAAI;QAErD,+BAA+B;QAC/B,IAAI,QAAQ,SAAS,GAAG,KAAK,GAAG,IAAI;YAClC,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;QAEA,QAAQ,GAAG,CAAC;QACZ,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,OAAO;IACT;AACF;AAGO,SAAS,iBAAiB,QAAsB,EAAE,KAAa;IACpE,MAAM,eAAe,oDAAyB;IAE9C,SAAS,OAAO,CAAC,GAAG,CAAC,qBAAqB,OAAO;QAC/C,UAAU;QACV,QAAQ;QACR,UAAU;QACV,QAAQ,mBAAmB;QAC3B,MAAM;IACR;AACF;AAGO,SAAS,cAAc,QAAsB,EAAE,SAAiB;IACrE,MAAM,eAAe,oDAAyB;IAE9C,SAAS,OAAO,CAAC,GAAG,CAAC,kBAAkB,WAAW;QAChD,UAAU;QACV,QAAQ;QACR,UAAU;QACV,QAAQ,mBAAmB;QAC3B,MAAM;IACR;AACF;AAGO,eAAe,sBAAsB,OAAoB;IAC9D,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,sBAAsB;IACxD,QAAQ,GAAG,CAAC,uCAAuC,CAAC,CAAC;IAErD,IAAI,CAAC,OAAO;QACV,QAAQ,GAAG,CAAC;QACZ,OAAO;IACT;IAEA,MAAM,UAAU,MAAM,mBAAmB;IACzC,QAAQ,GAAG,CAAC,4CAA4C,CAAC,CAAC;IAC1D,OAAO;AACT;AAGO,eAAe;IACpB,MAAM,cAAc,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAC1B,MAAM,QAAQ,YAAY,GAAG,CAAC,sBAAsB;IAEpD,IAAI,CAAC,OAAO;QACV,OAAO;IACT;IAEA,OAAO,MAAM,mBAAmB;AAClC;AAGO,SAAS,oBAAoB,QAAsB;IACxD,SAAS,OAAO,CAAC,MAAM,CAAC;IACxB,SAAS,OAAO,CAAC,MAAM,CAAC;AAC1B;AAGO,SAAS,kBAAkB,OAAoB,EAAE,aAAqB;IAC3E,MAAM,cAAc,QAAQ,OAAO,CAAC,GAAG,CAAC,mBAAmB;IAC3D,OAAO,gBAAgB,iBAAiB,gBAAgB;AAC1D;AAGO,MAAM,iBAAiB;IAC5B,YAAY;IACZ,UAAU;IACV,eAAe;QACb,QAAQ,oDAAyB;QACjC,UAAU;QACV,UAAU;QACV,QAAQ,mBAAmB;IAC7B;AACF;AAWO,eAAe,oBAAoB,YAAoB;IAC5D,MAAM,UAAU,MAAM,mBAAmB;IAEzC,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,4CAA4C;IAC5C,MAAM,mBAAkE;QACtE,SAAS,QAAQ,OAAO;QACxB,OAAO,QAAQ,KAAK;QACpB,WAAW,QAAQ,SAAS;QAC5B,UAAU,QAAQ,QAAQ;QAC1B,MAAM,QAAQ,IAAI;QAClB,aAAa,QAAQ,WAAW;QAChC,UAAU,QAAQ,QAAQ;IAC5B;IAEA,OAAO,MAAM,mBAAmB;AAClC", "debugId": null}}, {"offset": {"line": 258, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/app/api/auth/login/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { useMutation } from 'convex/react';\nimport { ConvexHttpClient } from 'convex/browser';\nimport { api } from '../../../../../convex/_generated/api';\nimport { \n  createSessionToken, \n  setSessionCookie, \n  setCSRFCookie, \n  generateCSRFToken,\n  validateCSRFToken \n} from '@/lib/session';\n\n// Initialize Convex client for server-side operations\nconst convex = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL!);\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { email, password, csrfToken } = body;\n\n    // Validate required fields\n    if (!email || !password) {\n      return NextResponse.json(\n        { success: false, error: 'Email and password are required' },\n        { status: 400 }\n      );\n    }\n\n    // Validate CSRF token (skip for initial login, but validate for subsequent requests)\n    const hasCsrfCookie = request.cookies.get('benzochem-csrf-token');\n    if (hasCsrfCookie && csrfToken && !validateCSRFToken(request, csrfToken)) {\n      return NextResponse.json(\n        { success: false, error: 'Invalid CSRF token' },\n        { status: 403 }\n      );\n    }\n\n    // Authenticate with Convex\n    const authResult = await convex.mutation(api.auth.authenticateAdmin, {\n      email,\n      password,\n    });\n\n    if (!authResult.success || !authResult.admin) {\n      return NextResponse.json(\n        { success: false, error: authResult.error || 'Authentication failed' },\n        { status: 401 }\n      );\n    }\n\n    // Create session token\n    const sessionToken = await createSessionToken({\n      adminId: authResult.admin._id,\n      email: authResult.admin.email,\n      firstName: authResult.admin.firstName,\n      lastName: authResult.admin.lastName,\n      role: authResult.admin.role,\n      permissions: authResult.admin.permissions,\n      isActive: authResult.admin.isActive,\n    });\n\n    // Generate CSRF token\n    const newCSRFToken = generateCSRFToken();\n\n    // Create response\n    const response = NextResponse.json({\n      success: true,\n      admin: {\n        _id: authResult.admin._id,\n        email: authResult.admin.email,\n        firstName: authResult.admin.firstName,\n        lastName: authResult.admin.lastName,\n        role: authResult.admin.role,\n        permissions: authResult.admin.permissions,\n        isActive: authResult.admin.isActive,\n      },\n      csrfToken: newCSRFToken,\n    });\n\n    // Set secure cookies\n    setSessionCookie(response, sessionToken);\n    setCSRFCookie(response, newCSRFToken);\n\n    return response;\n  } catch (error) {\n    console.error('Login API error:', error);\n    return NextResponse.json(\n      { success: false, error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n\n// Handle preflight requests for CORS\nexport async function OPTIONS(request: NextRequest) {\n  return new NextResponse(null, {\n    status: 200,\n    headers: {\n      'Access-Control-Allow-Origin': '*',\n      'Access-Control-Allow-Methods': 'POST, OPTIONS',\n      'Access-Control-Allow-Headers': 'Content-Type, X-CSRF-Token',\n    },\n  });\n}\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AAAA;AACA;AACA;;;;;AAQA,sDAAsD;AACtD,MAAM,SAAS,IAAI,iKAAA,CAAA,mBAAgB;AAE5B,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG;QAEvC,2BAA2B;QAC3B,IAAI,CAAC,SAAS,CAAC,UAAU;YACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAkC,GAC3D;gBAAE,QAAQ;YAAI;QAElB;QAEA,qFAAqF;QACrF,MAAM,gBAAgB,QAAQ,OAAO,CAAC,GAAG,CAAC;QAC1C,IAAI,iBAAiB,aAAa,CAAC,CAAA,GAAA,uHAAA,CAAA,oBAAiB,AAAD,EAAE,SAAS,YAAY;YACxE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAqB,GAC9C;gBAAE,QAAQ;YAAI;QAElB;QAEA,2BAA2B;QAC3B,MAAM,aAAa,MAAM,OAAO,QAAQ,CAAC,6HAAA,CAAA,MAAG,CAAC,IAAI,CAAC,iBAAiB,EAAE;YACnE;YACA;QACF;QAEA,IAAI,CAAC,WAAW,OAAO,IAAI,CAAC,WAAW,KAAK,EAAE;YAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO,WAAW,KAAK,IAAI;YAAwB,GACrE;gBAAE,QAAQ;YAAI;QAElB;QAEA,uBAAuB;QACvB,MAAM,eAAe,MAAM,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;YAC5C,SAAS,WAAW,KAAK,CAAC,GAAG;YAC7B,OAAO,WAAW,KAAK,CAAC,KAAK;YAC7B,WAAW,WAAW,KAAK,CAAC,SAAS;YACrC,UAAU,WAAW,KAAK,CAAC,QAAQ;YACnC,MAAM,WAAW,KAAK,CAAC,IAAI;YAC3B,aAAa,WAAW,KAAK,CAAC,WAAW;YACzC,UAAU,WAAW,KAAK,CAAC,QAAQ;QACrC;QAEA,sBAAsB;QACtB,MAAM,eAAe,CAAA,GAAA,uHAAA,CAAA,oBAAiB,AAAD;QAErC,kBAAkB;QAClB,MAAM,WAAW,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACjC,SAAS;YACT,OAAO;gBACL,KAAK,WAAW,KAAK,CAAC,GAAG;gBACzB,OAAO,WAAW,KAAK,CAAC,KAAK;gBAC7B,WAAW,WAAW,KAAK,CAAC,SAAS;gBACrC,UAAU,WAAW,KAAK,CAAC,QAAQ;gBACnC,MAAM,WAAW,KAAK,CAAC,IAAI;gBAC3B,aAAa,WAAW,KAAK,CAAC,WAAW;gBACzC,UAAU,WAAW,KAAK,CAAC,QAAQ;YACrC;YACA,WAAW;QACb;QAEA,qBAAqB;QACrB,CAAA,GAAA,uHAAA,CAAA,mBAAgB,AAAD,EAAE,UAAU;QAC3B,CAAA,GAAA,uHAAA,CAAA,gBAAa,AAAD,EAAE,UAAU;QAExB,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oBAAoB;QAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAwB,GACjD;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,QAAQ,OAAoB;IAChD,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,MAAM;QAC5B,QAAQ;QACR,SAAS;YACP,+BAA+B;YAC/B,gCAAgC;YAChC,gCAAgC;QAClC;IACF;AACF", "debugId": null}}]}