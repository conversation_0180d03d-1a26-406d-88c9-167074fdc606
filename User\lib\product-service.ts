import { storefrontFetch } from './shopify'

// Main product query
const PRODUCTS_QUERY = `
  query Products { 
    products(first: 50) { 
      edges { 
        node { 
          collections(first: 20) { 
            edges { 
              node { 
                title 
              } 
            } 
          } 
          id 
          title 
          description
          tags
          totalInventory
          images(first: 1) {
            edges {
              node {
                url
              }
            }
          }
          media(first: 10) { 
            edges {
              node {
                ... on MediaImage {
                  id
                  image {
                    url
                  }
                }
              }
            }
          } 
          priceRange { 
            minVariantPrice { 
              amount 
              currencyCode
            } 
            maxVariantPrice {
              amount
              currencyCode
            }
          } 
          compareAtPriceRange { 
            minVariantPrice { 
              amount 
              currencyCode
            } 
            maxVariantPrice {
              amount
              currencyCode
            }
          } 
          metafields(identifiers: [
            { namespace: "custom", key: "purity" },
            { namespace: "custom", key: "package_size" },
            { namespace: "custom", key: "hsn_number" }
            { namespace: "custom", key: "cas_number" }
            { namespace: "custom", key: "molecular_formula" },
            { namespace: "custom", key: "molecular_weight" }
            { namespace: "custom", key: "appearance" }
            { namespace: "custom", key: "solubility" }
            { namespace: "custom", key: "ph_value" }
            { namespace: "custom", key: "recommended_applications" }
          ]) { 
            id 
            key 
            namespace
            value 
            type 
          } 
        } 
      } 
    } 
  }
`

// New detailed product query with collection handles
const PRODUCT_WITH_COLLECTIONS_QUERY = `
  query ProductWithCollections($id: ID!) { 
    product(id: $id) { 
      collections(first: 20) { 
        edges { 
          node { 
            title
            handle
            id
          } 
        } 
      } 
      id
      title
      description
      descriptionHtml
      tags
      totalInventory
      images(first: 1) {
        edges {
          node {
            url
          }
        }
      }
      media(first: 10) { 
        edges {
          node {
            ... on MediaImage {
              id
              image {
                url
              }
            }
          }
        }
      } 
      priceRange { 
        minVariantPrice { 
          amount 
          currencyCode
        } 
        maxVariantPrice {
          amount
          currencyCode
        }
      } 
      compareAtPriceRange { 
        minVariantPrice { 
          amount 
          currencyCode
        } 
        maxVariantPrice {
          amount
          currencyCode
        }
      } 
      metafields(identifiers: [
        { namespace: "custom", key: "purity" },
        { namespace: "custom", key: "package_size" },
        { namespace: "custom", key: "hsn_number" }
        { namespace: "custom", key: "cas_number" }
        { namespace: "custom", key: "molecular_formula" }
        { namespace: "custom", key: "molecular_weight" }
        { namespace: "custom", key: "appearance" }
        { namespace: "custom", key: "solubility" }
        { namespace: "custom", key: "ph_value" }
        { namespace: "custom", key: "recommended_applications" }
      ]) {
        id
        key
        namespace
        value
        type
      }
      variants(first: 10) {
        edges {
          node {
            id
            title
            availableForSale
            quantityAvailable
            priceV2 {
              amount
              currencyCode
            }
            compareAtPriceV2 {
              amount
              currencyCode
            }
            selectedOptions {
              name
              value
            }
            image {
              url
              altText
            }
          }
        }
      }
    }
  }
`

// Types for the product data
export interface ProductImage {
  url: string
}

export interface ProductMedia {
  id: string
  image: ProductImage
}

export interface PriceRange {
  minVariantPrice: {
    amount: string
    currencyCode: string
  }
  maxVariantPrice: {
    amount: string
    currencyCode: string
  }
}

export interface Metafield {
  id: string
  key: string
  namespace: string
  value: string
  type: string
}

// Base collection node interface
export interface BaseCollectionNode {
  title: string
  id: string
}

// Extended collection node with handle
export interface DetailedCollectionNode extends BaseCollectionNode {
  handle: string
}

export interface ProductVariantOption {
  name: string;
  value: string;
}

export interface ProductVariant {
  id: string;
  title: string; // e.g., "Small / Red" or "25kg Bag"
  availableForSale: boolean;
  quantityAvailable?: number | null; // Optional as not always available or relevant
  priceV2: {
    amount: string;
    currencyCode: string;
  };
  compareAtPriceV2?: { // Optional
    amount: string;
    currencyCode: string;
  } | null;
  selectedOptions: ProductVariantOption[];
  image?: { // Variant image
    url: string;
    altText?: string | null;
  } | null;
}

// Original Product interface (keep as is for backward compatibility)
// Consider updating this too if getProducts might eventually fetch variants
export interface Product {
  id: string
  title: string
  description: string
  descriptionHtml?: string; // Added
  tags: string[]
  collections: {
    edges: Array<{
      node: BaseCollectionNode
    }>
  }
  images: {
    edges: Array<{
      node: ProductImage
    }>
  }
  media: {
    edges: Array<{
      node: ProductMedia
    }>
  }
  priceRange: PriceRange
  compareAtPriceRange: PriceRange
  metafields: Metafield[]
  variants?: { // Optional for basic Product type
    edges: Array<{
      node: ProductVariant
    }>
  }
}

// New interface for detailed product with collection handles and variants
export interface DetailedProduct {
  id: string
  title: string
  description: string
  descriptionHtml?: string; // Added
  tags: string[]
  collections: {
    edges: Array<{
      node: DetailedCollectionNode
    }>
  }
  images: {
    edges: Array<{
      node: ProductImage
    }>
  }
  media: {
    edges: Array<{
      node: ProductMedia
    }>
  }
  priceRange: PriceRange
  compareAtPriceRange: PriceRange
  metafields: Metafield[]
  variants: { // Variants are expected for DetailedProduct
    edges: Array<{
      node: ProductVariant
    }>
  }
}

// Get all products (unchanged)
export async function getProducts(featured: boolean = false): Promise<Product[]> {
  try {
    const { data } = await storefrontFetch<{
      products: {
        edges: Array<{
          node: Product
        }>
      }
    }>({
      query: PRODUCTS_QUERY,
    })

    if (!data?.products?.edges) {
      throw new Error('No products found in response')
    }

    const products = data.products.edges.map((edge) => ({
      ...edge.node,
      metafields: edge.node.metafields || []
    }))

    // Filter products by featured tag if requested
    if (featured) {
      return products.filter((product) => product.tags.includes('featured'))
    }

    return products
  } catch (error) {
    console.error('Error fetching products:', error)
    throw error
  }
}

// New function to get a single product with detailed collection info
export async function getProductWithCollections(productId: string): Promise<DetailedProduct | null> {
  try {
    console.log('🔍 Fetching detailed product:', productId)
    
    const { data, errors } = await storefrontFetch<{
      product: DetailedProduct
    }>({
      query: PRODUCT_WITH_COLLECTIONS_QUERY,
      variables: { id: productId }
    })

    if (errors) {
      console.error('❌ GraphQL Errors:', errors)
    }

    if (!data?.product) {
      console.log('❌ Product not found:', productId)
      return null
    }

    const product = {
      ...data.product,
      metafields: data.product.metafields || []
    }

    // Debug: Log collection information
    console.log('📊 Product fetched:', product.title)
    console.log('📁 Collections:', product.collections.edges.map(edge => ({
      title: edge.node.title,
      handle: edge.node.handle
    })))

    return product
  } catch (error) {
    console.error('❌ Error fetching detailed product:', error)
    return null
  }
}

// Helper function to get collection info for breadcrumbs
export function getCollectionInfoForBreadcrumb(product: DetailedProduct | Product) {
  const primaryCollection = product.collections.edges[0]?.node
  
  if (!primaryCollection) {
    return {
      title: 'Products',
      handle: 'products',
      href: '/categories/products'
    }
  }

  // Check if the collection has a handle (from DetailedProduct)
  const handle = 'handle' in primaryCollection ? primaryCollection.handle : null
  
  // Fallback to creating a slug from title if no handle
  const slug = handle || primaryCollection.title.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-+|-+$/g, '')

  return {
    title: primaryCollection.title,
    handle: slug,
    href: `/categories/${slug}`
  }
}
