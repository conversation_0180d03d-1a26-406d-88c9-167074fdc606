import { mutation } from "./_generated/server";

// Note: Default admin creation removed for security.
// Admins should be created through proper registration or admin invitation process.

// Mutation to seed sample data for testing
export const seedSampleData = mutation({
  args: {},
  handler: async (ctx) => {
    const now = Date.now();

    // Note: Admin creation removed. Use proper admin registration process.
    // For demo purposes, we'll use a placeholder admin ID
    const adminId = "demo_admin_placeholder";

    // Create sample users
    const users = [
      {
        userId: "user_001",
        email: "<EMAIL>",
        firstName: "John",
        lastName: "Doe",
        phone: "******-0123",
        businessName: "Doe Chemicals Ltd",
        gstNumber: "27AABCU9603R1ZX",
        isGstVerified: true,
        status: "pending" as const,
        role: "user" as const,
        legalNameOfBusiness: "Doe Chemicals Private Limited",
        tradeName: "Doe Chemicals",
        constitutionOfBusiness: "Private Limited Company",
        taxpayerType: "Regular",
        gstStatus: "Active",
        principalPlaceOfBusiness: "Mumbai, Maharashtra",
        createdAt: now - (2 * 24 * 60 * 60 * 1000), // 2 days ago
        updatedAt: now - (2 * 24 * 60 * 60 * 1000),
      },
      {
        userId: "user_002",
        email: "<EMAIL>",
        firstName: "Jane",
        lastName: "Smith",
        phone: "******-0124",
        businessName: "Smith Industries",
        gstNumber: "29AABCU9603R1ZY",
        isGstVerified: false,
        status: "pending" as const,
        role: "user" as const,
        legalNameOfBusiness: "Smith Industries Private Limited",
        tradeName: "Smith Industries",
        constitutionOfBusiness: "Private Limited Company",
        taxpayerType: "Regular",
        gstStatus: "Pending",
        principalPlaceOfBusiness: "Delhi, Delhi",
        createdAt: now - (1 * 24 * 60 * 60 * 1000), // 1 day ago
        updatedAt: now - (1 * 24 * 60 * 60 * 1000),
      },
      {
        userId: "user_003",
        email: "<EMAIL>",
        firstName: "Bob",
        lastName: "Wilson",
        phone: "******-0125",
        businessName: "Wilson Chemical Works",
        gstNumber: "07AABCU9603R1ZZ",
        isGstVerified: true,
        status: "approved" as const,
        role: "user" as const,
        legalNameOfBusiness: "Wilson Chemical Works Private Limited",
        tradeName: "Wilson Chemicals",
        constitutionOfBusiness: "Private Limited Company",
        taxpayerType: "Regular",
        gstStatus: "Active",
        principalPlaceOfBusiness: "Bangalore, Karnataka",
        approvedBy: adminId,
        approvedAt: now - (12 * 60 * 60 * 1000), // 12 hours ago
        createdAt: now - (3 * 24 * 60 * 60 * 1000), // 3 days ago
        updatedAt: now - (12 * 60 * 60 * 1000),
      }
    ];

    const userIds = [];
    for (const user of users) {
      const userId = await ctx.db.insert("users", user);
      userIds.push(userId);
    }

    // Create sample products
    const products = [
      {
        productId: "prod_001",
        title: "Benzene (99.9% Pure)",
        description: "High purity benzene for industrial applications",
        tags: ["chemical", "solvent", "aromatic"],
        collections: ["Solvents", "Aromatic Compounds"],
        images: [
          {
            url: "https://via.placeholder.com/300x300?text=Benzene",
            altText: "Benzene chemical structure"
          }
        ],
        priceRange: {
          minVariantPrice: { amount: "150.00", currencyCode: "INR" },
          maxVariantPrice: { amount: "150.00", currencyCode: "INR" }
        },
        purity: "99.9%",
        packaging: "1L, 5L, 25L",
        casNumber: "71-43-2",
        molecularFormula: "C6H6",
        molecularWeight: "78.11 g/mol",
        appearance: "Colorless liquid",
        solubility: "Insoluble in water",
        phValue: "N/A",
        chemicalName: "Benzene",
        features: ["High purity", "Industrial grade", "Multiple packaging options"],
        applications: ["Solvent", "Chemical synthesis", "Research"],
        status: "active" as const,
        featured: true,
        totalInventory: 500,
        createdAt: now - (7 * 24 * 60 * 60 * 1000), // 7 days ago
        updatedAt: now - (1 * 24 * 60 * 60 * 1000),
        createdBy: adminId,
      },
      {
        productId: "prod_002",
        title: "Toluene (Technical Grade)",
        description: "Technical grade toluene for industrial use",
        tags: ["chemical", "solvent", "aromatic"],
        collections: ["Solvents", "Aromatic Compounds"],
        images: [
          {
            url: "https://via.placeholder.com/300x300?text=Toluene",
            altText: "Toluene chemical structure"
          }
        ],
        priceRange: {
          minVariantPrice: { amount: "120.00", currencyCode: "INR" },
          maxVariantPrice: { amount: "120.00", currencyCode: "INR" }
        },
        purity: "99.5%",
        packaging: "1L, 5L, 25L, 200L",
        casNumber: "108-88-3",
        molecularFormula: "C7H8",
        molecularWeight: "92.14 g/mol",
        appearance: "Colorless liquid",
        solubility: "Insoluble in water",
        phValue: "N/A",
        chemicalName: "Methylbenzene",
        features: ["Technical grade", "Cost effective", "Bulk packaging available"],
        applications: ["Paint thinner", "Adhesives", "Chemical intermediate"],
        status: "active" as const,
        featured: false,
        totalInventory: 1000,
        createdAt: now - (5 * 24 * 60 * 60 * 1000), // 5 days ago
        updatedAt: now - (2 * 24 * 60 * 60 * 1000),
        createdBy: adminId,
      }
    ];

    const productIds = [];
    for (const product of products) {
      const productId = await ctx.db.insert("products", product);
      productIds.push(productId);
    }

    // Create sample notifications
    const notifications = [
      {
        type: "user_registration" as const,
        title: "New User Registration",
        message: `${users[0].firstName} ${users[0].lastName} has registered and is pending approval.`,
        recipientType: "all_admins" as const,
        isRead: false,
        priority: "medium" as const,
        relatedEntityType: "user" as const,
        relatedEntityId: userIds[0],
        createdAt: now - (2 * 24 * 60 * 60 * 1000),
      },
      {
        type: "user_registration" as const,
        title: "New User Registration",
        message: `${users[1].firstName} ${users[1].lastName} has registered and is pending approval.`,
        recipientType: "all_admins" as const,
        isRead: false,
        priority: "medium" as const,
        relatedEntityType: "user" as const,
        relatedEntityId: userIds[1],
        createdAt: now - (1 * 24 * 60 * 60 * 1000),
      },
      {
        type: "user_approval" as const,
        title: "User Approved",
        message: `${users[2].firstName} ${users[2].lastName}'s account has been approved.`,
        recipientType: "specific_user" as const,
        recipientId: userIds[2],
        isRead: false,
        priority: "high" as const,
        relatedEntityType: "user" as const,
        relatedEntityId: userIds[2],
        createdAt: now - (12 * 60 * 60 * 1000),
        createdBy: adminId,
      }
    ];

    for (const notification of notifications) {
      await ctx.db.insert("notifications", notification);
    }

    // Create sample settings
    const settings = [
      {
        key: "site_name",
        value: "Benzochem Industries Admin",
        description: "The name of the admin site",
        category: "general" as const,
        isPublic: true,
        updatedBy: adminId,
        updatedAt: now,
        createdAt: now,
      },
      {
        key: "max_file_upload_size",
        value: ********, // 10MB
        description: "Maximum file upload size in bytes",
        category: "general" as const,
        isPublic: false,
        updatedBy: adminId,
        updatedAt: now,
        createdAt: now,
      }
    ];

    for (const setting of settings) {
      await ctx.db.insert("settings", setting);
    }

    return {
      message: "Sample data seeded successfully",
      adminId,
      userIds,
      productIds,
      counts: {
        admins: 1,
        users: users.length,
        products: products.length,
        notifications: notifications.length,
        settings: settings.length,
      }
    };
  },
});
