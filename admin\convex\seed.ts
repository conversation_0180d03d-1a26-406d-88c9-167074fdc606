import { mutation } from "./_generated/server";

// Note: Default admin creation removed for security.
// Admins should be created through proper registration or admin invitation process.

// Mutation to seed sample data for testing
export const seedSampleData = mutation({
  args: {},
  handler: async (ctx) => {
    const now = Date.now();

    // Create a demo admin for testing re-authentication
    // This is for development/demo purposes only
    const existingAdmin = await ctx.db
      .query("admins")
      .filter((q) => q.eq(q.field("email"), "<EMAIL>"))
      .first();

    let adminId;
    if (existingAdmin) {
      adminId = existingAdmin._id;
    } else {
      adminId = await ctx.db.insert("admins", {
        email: "<EMAIL>",
        firstName: "Demo",
        lastName: "Admin",
        password: "demo123", // Demo password for testing re-auth
      });
    }

    return {
      message: "Demo admin created for testing",
      adminId,
      credentials: {
        email: "<EMAIL>",
        password: "demo123"
      },
      note: "Use these credentials to test the re-authentication system"
    };



  },
});
