import { mutation } from "./_generated/server";

// Note: Default admin creation removed for security.
// Admins should be created through proper registration or admin invitation process.

// Mutation to seed sample data for testing
export const seedSampleData = mutation({
  args: {},
  handler: async (ctx) => {
    const now = Date.now();

    // Note: Admin creation removed. Use proper admin registration process.
    // Skip seeding data that requires admin IDs since no admin exists
    console.log("Skipping seed data that requires admin references");

    return {
      message: "Seed data creation skipped - no admin account available",
      note: "Create an admin account first, then run seed data creation"
    };



  },
});
