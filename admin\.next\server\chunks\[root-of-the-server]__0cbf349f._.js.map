{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/convex/_generated/api.js"], "sourcesContent": ["/* eslint-disable */\n/**\n * Generated `api` utility.\n *\n * THIS CODE IS AUTOMATICALLY GENERATED.\n *\n * To regenerate, run `npx convex dev`.\n * @module\n */\n\nimport { anyApi } from \"convex/server\";\n\n/**\n * A utility for referencing Convex functions in your app's API.\n *\n * Usage:\n * ```js\n * const myFunctionReference = api.myModule.myFunction;\n * ```\n */\nexport const api = anyApi;\nexport const internal = anyApi;\n"], "names": [], "mappings": "AAAA,kBAAkB,GAClB;;;;;;;CAOC;;;;AAED;AAAA;;AAUO,MAAM,MAAM,wJAAA,CAAA,SAAM;AAClB,MAAM,WAAW,wJAAA,CAAA,SAAM", "debugId": null}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/lib/apiKeyAuth.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { ConvexHttpClient } from 'convex/browser';\nimport { api } from '../../../convex/_generated/api';\n\n// Initialize Convex client for server-side operations\nconst convex = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL!);\n\nexport interface ApiKeyValidationResult {\n  isValid: boolean;\n  apiKey?: {\n    id: string;\n    keyId: string;\n    environment: \"live\" | \"test\";\n    name: string;\n    permissions: string[];\n    rateLimit: {\n      requestsPerMinute: number;\n      requestsPerHour: number;\n      requestsPerDay: number;\n      burstLimit?: number;\n    };\n    rateLimitCounts?: {\n      minute: number;\n      hour: number;\n      day: number;\n      burst: number;\n    };\n    rateLimitResets?: {\n      minute: number;\n      hour: number;\n      day: number;\n    };\n  };\n  error?: string;\n  statusCode?: number;\n}\n\n/**\n * Extract API key from request headers\n */\nexport function extractApiKey(request: NextRequest): string | null {\n  // Check Authorization header (Bearer token)\n  const authHeader = request.headers.get('authorization');\n  if (authHeader?.startsWith('Bearer ')) {\n    return authHeader.substring(7);\n  }\n\n  // Check X-API-Key header\n  const apiKeyHeader = request.headers.get('x-api-key');\n  if (apiKeyHeader) {\n    return apiKeyHeader;\n  }\n\n  // Check query parameter (less secure, but sometimes needed)\n  const url = new URL(request.url);\n  const apiKeyParam = url.searchParams.get('api_key');\n  if (apiKeyParam) {\n    return apiKeyParam;\n  }\n\n  return null;\n}\n\n/**\n * Validate API key and return validation result\n */\nexport async function validateApiKey(apiKey: string): Promise<ApiKeyValidationResult> {\n  try {\n    if (!apiKey) {\n      return {\n        isValid: false,\n        error: 'API key is required',\n        statusCode: 401\n      };\n    }\n\n    // Validate with Convex\n    const validationResult = await convex.query(api.apiKeys.validateApiKey, {\n      key: apiKey\n    });\n\n    if (!validationResult) {\n      return {\n        isValid: false,\n        error: 'Invalid or expired API key',\n        statusCode: 401\n      };\n    }\n\n    return {\n      isValid: true,\n      apiKey: validationResult\n    };\n\n  } catch (error) {\n    console.error('API key validation error:', error);\n    return {\n      isValid: false,\n      error: 'Internal server error during API key validation',\n      statusCode: 500\n    };\n  }\n}\n\n/**\n * Check if API key has required permission\n */\nexport function hasPermission(apiKey: ApiKeyValidationResult['apiKey'], requiredPermission: string): boolean {\n  if (!apiKey) return false;\n  \n  // Check for wildcard permission (admin access)\n  if (apiKey.permissions.includes('*')) return true;\n  \n  // Check for specific permission\n  if (apiKey.permissions.includes(requiredPermission)) return true;\n  \n  // Check for permission category (e.g., 'products:*' allows 'products:read')\n  const [category] = requiredPermission.split(':');\n  if (apiKey.permissions.includes(`${category}:*`)) return true;\n  \n  return false;\n}\n\n/**\n * Rate limiting check\n */\nexport async function checkRateLimit(apiKey: ApiKeyValidationResult['apiKey']): Promise<{\n  allowed: boolean;\n  error?: string;\n  retryAfter?: number;\n}> {\n  if (!apiKey) {\n    return { allowed: false, error: 'No API key provided' };\n  }\n\n  const now = Date.now();\n  const currentMinute = Math.floor(now / 60000) * 60000; // Round to minute\n  const currentHour = Math.floor(now / 3600000) * 3600000; // Round to hour\n  const currentDay = Math.floor(now / 86400000) * 86400000; // Round to day\n\n  const counts = apiKey.rateLimitCounts || { minute: 0, hour: 0, day: 0, burst: 0 };\n  const resets = apiKey.rateLimitResets || { minute: currentMinute, hour: currentHour, day: currentDay };\n\n  // Reset counters if time windows have passed\n  if (resets.minute < currentMinute) {\n    counts.minute = 0;\n    counts.burst = 0;\n  }\n  if (resets.hour < currentHour) {\n    counts.hour = 0;\n  }\n  if (resets.day < currentDay) {\n    counts.day = 0;\n  }\n\n  // Check limits\n  const limits = apiKey.rateLimit;\n  \n  // Check daily limit first (most restrictive long-term)\n  if (counts.day >= limits.requestsPerDay) {\n    const retryAfter = Math.ceil((currentDay + 86400000 - now) / 1000);\n    return { \n      allowed: false, \n      error: 'Daily rate limit exceeded', \n      retryAfter \n    };\n  }\n\n  // Check hourly limit\n  if (counts.hour >= limits.requestsPerHour) {\n    const retryAfter = Math.ceil((currentHour + 3600000 - now) / 1000);\n    return { \n      allowed: false, \n      error: 'Hourly rate limit exceeded', \n      retryAfter \n    };\n  }\n\n  // Check burst limit (if configured)\n  if (limits.burstLimit && counts.burst >= limits.burstLimit) {\n    const retryAfter = Math.ceil((currentMinute + 60000 - now) / 1000);\n    return { \n      allowed: false, \n      error: 'Burst rate limit exceeded', \n      retryAfter \n    };\n  }\n\n  // Check per-minute limit\n  if (counts.minute >= limits.requestsPerMinute) {\n    const retryAfter = Math.ceil((currentMinute + 60000 - now) / 1000);\n    return { \n      allowed: false, \n      error: 'Per-minute rate limit exceeded', \n      retryAfter \n    };\n  }\n\n  return { allowed: true };\n}\n\n/**\n * Update rate limit counters\n */\nexport async function updateRateLimitCounters(apiKey: string): Promise<void> {\n  try {\n    await convex.mutation(api.apiKeys.updateApiKeyUsage, { key: apiKey });\n  } catch (error) {\n    console.error('Failed to update rate limit counters:', error);\n    // Don't throw error here as it shouldn't block the request\n  }\n}\n\n/**\n * Create error response for API key validation failures\n */\nexport function createApiKeyErrorResponse(\n  error: string, \n  statusCode: number = 401, \n  retryAfter?: number\n): NextResponse {\n  const headers: Record<string, string> = {\n    'Content-Type': 'application/json',\n    'X-RateLimit-Error': error\n  };\n\n  if (retryAfter) {\n    headers['Retry-After'] = retryAfter.toString();\n  }\n\n  return NextResponse.json(\n    {\n      success: false,\n      error: error,\n      code: statusCode === 401 ? 'UNAUTHORIZED' : \n            statusCode === 403 ? 'FORBIDDEN' : \n            statusCode === 429 ? 'RATE_LIMITED' : 'ERROR'\n    },\n    { \n      status: statusCode,\n      headers\n    }\n  );\n}\n\n/**\n * Log API key usage for security monitoring\n */\nexport async function logApiKeyUsage(\n  apiKey: ApiKeyValidationResult['apiKey'],\n  request: NextRequest,\n  success: boolean,\n  error?: string\n): Promise<void> {\n  try {\n    // Extract relevant request information\n    const requestInfo = {\n      method: request.method,\n      url: request.url,\n      userAgent: request.headers.get('user-agent'),\n      ip: request.headers.get('x-forwarded-for') ||\n          request.headers.get('x-real-ip') ||\n          'unknown',\n      timestamp: Date.now(),\n      success,\n      error,\n      keyId: apiKey?.keyId,\n      environment: apiKey?.environment\n    };\n\n    // Log to console for now (in production, you might want to use a proper logging service)\n    console.log('API Key Usage:', JSON.stringify(requestInfo, null, 2));\n\n    // Log security events for failed attempts\n    if (!success && error) {\n      await logSecurityEvent(request, apiKey, error);\n    }\n\n    // Check for suspicious patterns\n    await checkSuspiciousActivity(request, apiKey, success);\n\n  } catch (logError) {\n    console.error('Failed to log API key usage:', logError);\n    // Don't throw error here as it shouldn't block the request\n  }\n}\n\n/**\n * Log security events\n */\nexport async function logSecurityEvent(\n  request: NextRequest,\n  apiKey: ApiKeyValidationResult['apiKey'] | undefined,\n  error: string\n): Promise<void> {\n  try {\n    let eventType: string;\n    let severity: string;\n    let description: string;\n\n    // Determine event type and severity based on error\n    if (error.includes('API key is required') || error.includes('Invalid API key')) {\n      eventType = 'invalid_api_key';\n      severity = 'medium';\n      description = 'Invalid or missing API key attempt';\n    } else if (error.includes('Rate limit exceeded')) {\n      eventType = 'rate_limit_exceeded';\n      severity = 'low';\n      description = 'API rate limit exceeded';\n    } else if (error.includes('Insufficient permissions')) {\n      eventType = 'permission_violation';\n      severity = 'medium';\n      description = 'API key attempted to access unauthorized resource';\n    } else {\n      eventType = 'suspicious_usage_pattern';\n      severity = 'low';\n      description = `API request failed: ${error}`;\n    }\n\n    const details = {\n      error,\n      timestamp: Date.now(),\n      requestPath: new URL(request.url).pathname,\n      hasApiKey: !!apiKey,\n    };\n\n    await convex.mutation(api.security.logSecurityEvent, {\n      eventType: eventType as any,\n      severity: severity as any,\n      description,\n      details,\n      ipAddress: request.headers.get('x-forwarded-for') ||\n                 request.headers.get('x-real-ip') ||\n                 'unknown',\n      userAgent: request.headers.get('user-agent') || undefined,\n      requestUrl: request.url,\n      requestMethod: request.method,\n      apiKeyId: apiKey?.keyId,\n      apiKeyEnvironment: apiKey?.environment,\n    });\n\n  } catch (error) {\n    console.error('Failed to log security event:', error);\n  }\n}\n\n/**\n * Check for suspicious activity patterns\n */\nexport async function checkSuspiciousActivity(\n  request: NextRequest,\n  apiKey: ApiKeyValidationResult['apiKey'] | undefined,\n  success: boolean\n): Promise<void> {\n  try {\n    const ip = request.headers.get('x-forwarded-for') ||\n              request.headers.get('x-real-ip') ||\n              'unknown';\n\n    // Check for multiple failed attempts from same IP\n    if (!success) {\n      // In a real implementation, you would track failed attempts in a cache or database\n      // For now, we'll just log a potential security event\n      await convex.mutation(api.security.logSecurityEvent, {\n        eventType: 'multiple_failed_attempts',\n        severity: 'medium',\n        description: 'Failed API request detected',\n        details: {\n          ip,\n          userAgent: request.headers.get('user-agent'),\n          requestPath: new URL(request.url).pathname,\n          timestamp: Date.now(),\n        },\n        ipAddress: ip,\n        userAgent: request.headers.get('user-agent') || undefined,\n        requestUrl: request.url,\n        requestMethod: request.method,\n        apiKeyId: apiKey?.keyId,\n        apiKeyEnvironment: apiKey?.environment,\n      });\n    }\n\n    // Check for unusual usage patterns (simplified example)\n    if (apiKey && success) {\n      const hour = new Date().getHours();\n\n      // Flag requests outside normal business hours as potentially suspicious\n      if (hour < 6 || hour > 22) {\n        await convex.mutation(api.security.logSecurityEvent, {\n          eventType: 'unusual_ip_activity',\n          severity: 'low',\n          description: 'API request outside normal business hours',\n          details: {\n            hour,\n            ip,\n            keyId: apiKey.keyId,\n            timestamp: Date.now(),\n          },\n          ipAddress: ip,\n          userAgent: request.headers.get('user-agent') || undefined,\n          requestUrl: request.url,\n          requestMethod: request.method,\n          apiKeyId: apiKey.keyId,\n          apiKeyEnvironment: apiKey.environment,\n        });\n      }\n    }\n\n  } catch (error) {\n    console.error('Failed to check suspicious activity:', error);\n  }\n}\n\n/**\n * Middleware wrapper for API key authentication\n */\nexport function withApiKeyAuth(\n  handler: (request: NextRequest, apiKey: ApiKeyValidationResult['apiKey']) => Promise<NextResponse>,\n  options: {\n    requiredPermission?: string;\n    skipRateLimit?: boolean;\n  } = {}\n) {\n  return async (request: NextRequest): Promise<NextResponse> => {\n    try {\n      // Extract API key from request\n      const apiKeyString = extractApiKey(request);\n\n      if (!apiKeyString) {\n        await logApiKeyUsage(undefined, request, false, 'No API key provided');\n        return createApiKeyErrorResponse('API key is required');\n      }\n\n      // Validate API key\n      const validation = await validateApiKey(apiKeyString);\n\n      if (!validation.isValid) {\n        await logApiKeyUsage(undefined, request, false, validation.error);\n        return createApiKeyErrorResponse(\n          validation.error || 'Invalid API key',\n          validation.statusCode || 401\n        );\n      }\n\n      // Check permissions if required\n      if (options.requiredPermission && !hasPermission(validation.apiKey, options.requiredPermission)) {\n        await logApiKeyUsage(validation.apiKey, request, false, 'Insufficient permissions');\n        return createApiKeyErrorResponse('Insufficient permissions', 403);\n      }\n\n      // Check rate limits (unless skipped)\n      if (!options.skipRateLimit) {\n        const rateLimitCheck = await checkRateLimit(validation.apiKey);\n\n        if (!rateLimitCheck.allowed) {\n          await logApiKeyUsage(validation.apiKey, request, false, rateLimitCheck.error);\n          return createApiKeyErrorResponse(\n            rateLimitCheck.error || 'Rate limit exceeded',\n            429,\n            rateLimitCheck.retryAfter\n          );\n        }\n\n        // Update rate limit counters\n        await updateRateLimitCounters(apiKeyString);\n      }\n\n      // Log successful authentication\n      await logApiKeyUsage(validation.apiKey, request, true);\n\n      // Call the actual handler with validated API key\n      return await handler(request, validation.apiKey);\n\n    } catch (error) {\n      console.error('API key middleware error:', error);\n      await logApiKeyUsage(undefined, request, false, 'Internal server error');\n      return createApiKeyErrorResponse('Internal server error', 500);\n    }\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;AAAA;;;;;;;;;AAGA,sDAAsD;AACtD,MAAM,SAAS,IAAI,iKAAA,CAAA,mBAAgB;AAmC5B,SAAS,cAAc,OAAoB;IAChD,4CAA4C;IAC5C,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;IACvC,IAAI,YAAY,WAAW,YAAY;QACrC,OAAO,WAAW,SAAS,CAAC;IAC9B;IAEA,yBAAyB;IACzB,MAAM,eAAe,QAAQ,OAAO,CAAC,GAAG,CAAC;IACzC,IAAI,cAAc;QAChB,OAAO;IACT;IAEA,4DAA4D;IAC5D,MAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;IAC/B,MAAM,cAAc,IAAI,YAAY,CAAC,GAAG,CAAC;IACzC,IAAI,aAAa;QACf,OAAO;IACT;IAEA,OAAO;AACT;AAKO,eAAe,eAAe,MAAc;IACjD,IAAI;QACF,IAAI,CAAC,QAAQ;YACX,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,YAAY;YACd;QACF;QAEA,uBAAuB;QACvB,MAAM,mBAAmB,MAAM,OAAO,KAAK,CAAC,IAAI,OAAO,CAAC,cAAc,EAAE;YACtE,KAAK;QACP;QAEA,IAAI,CAAC,kBAAkB;YACrB,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,YAAY;YACd;QACF;QAEA,OAAO;YACL,SAAS;YACT,QAAQ;QACV;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;YACL,SAAS;YACT,OAAO;YACP,YAAY;QACd;IACF;AACF;AAKO,SAAS,cAAc,MAAwC,EAAE,kBAA0B;IAChG,IAAI,CAAC,QAAQ,OAAO;IAEpB,+CAA+C;IAC/C,IAAI,OAAO,WAAW,CAAC,QAAQ,CAAC,MAAM,OAAO;IAE7C,gCAAgC;IAChC,IAAI,OAAO,WAAW,CAAC,QAAQ,CAAC,qBAAqB,OAAO;IAE5D,4EAA4E;IAC5E,MAAM,CAAC,SAAS,GAAG,mBAAmB,KAAK,CAAC;IAC5C,IAAI,OAAO,WAAW,CAAC,QAAQ,CAAC,GAAG,SAAS,EAAE,CAAC,GAAG,OAAO;IAEzD,OAAO;AACT;AAKO,eAAe,eAAe,MAAwC;IAK3E,IAAI,CAAC,QAAQ;QACX,OAAO;YAAE,SAAS;YAAO,OAAO;QAAsB;IACxD;IAEA,MAAM,MAAM,KAAK,GAAG;IACpB,MAAM,gBAAgB,KAAK,KAAK,CAAC,MAAM,SAAS,OAAO,kBAAkB;IACzE,MAAM,cAAc,KAAK,KAAK,CAAC,MAAM,WAAW,SAAS,gBAAgB;IACzE,MAAM,aAAa,KAAK,KAAK,CAAC,MAAM,YAAY,UAAU,eAAe;IAEzE,MAAM,SAAS,OAAO,eAAe,IAAI;QAAE,QAAQ;QAAG,MAAM;QAAG,KAAK;QAAG,OAAO;IAAE;IAChF,MAAM,SAAS,OAAO,eAAe,IAAI;QAAE,QAAQ;QAAe,MAAM;QAAa,KAAK;IAAW;IAErG,6CAA6C;IAC7C,IAAI,OAAO,MAAM,GAAG,eAAe;QACjC,OAAO,MAAM,GAAG;QAChB,OAAO,KAAK,GAAG;IACjB;IACA,IAAI,OAAO,IAAI,GAAG,aAAa;QAC7B,OAAO,IAAI,GAAG;IAChB;IACA,IAAI,OAAO,GAAG,GAAG,YAAY;QAC3B,OAAO,GAAG,GAAG;IACf;IAEA,eAAe;IACf,MAAM,SAAS,OAAO,SAAS;IAE/B,uDAAuD;IACvD,IAAI,OAAO,GAAG,IAAI,OAAO,cAAc,EAAE;QACvC,MAAM,aAAa,KAAK,IAAI,CAAC,CAAC,aAAa,WAAW,GAAG,IAAI;QAC7D,OAAO;YACL,SAAS;YACT,OAAO;YACP;QACF;IACF;IAEA,qBAAqB;IACrB,IAAI,OAAO,IAAI,IAAI,OAAO,eAAe,EAAE;QACzC,MAAM,aAAa,KAAK,IAAI,CAAC,CAAC,cAAc,UAAU,GAAG,IAAI;QAC7D,OAAO;YACL,SAAS;YACT,OAAO;YACP;QACF;IACF;IAEA,oCAAoC;IACpC,IAAI,OAAO,UAAU,IAAI,OAAO,KAAK,IAAI,OAAO,UAAU,EAAE;QAC1D,MAAM,aAAa,KAAK,IAAI,CAAC,CAAC,gBAAgB,QAAQ,GAAG,IAAI;QAC7D,OAAO;YACL,SAAS;YACT,OAAO;YACP;QACF;IACF;IAEA,yBAAyB;IACzB,IAAI,OAAO,MAAM,IAAI,OAAO,iBAAiB,EAAE;QAC7C,MAAM,aAAa,KAAK,IAAI,CAAC,CAAC,gBAAgB,QAAQ,GAAG,IAAI;QAC7D,OAAO;YACL,SAAS;YACT,OAAO;YACP;QACF;IACF;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAKO,eAAe,wBAAwB,MAAc;IAC1D,IAAI;QACF,MAAM,OAAO,QAAQ,CAAC,IAAI,OAAO,CAAC,iBAAiB,EAAE;YAAE,KAAK;QAAO;IACrE,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yCAAyC;IACvD,2DAA2D;IAC7D;AACF;AAKO,SAAS,0BACd,KAAa,EACb,aAAqB,GAAG,EACxB,UAAmB;IAEnB,MAAM,UAAkC;QACtC,gBAAgB;QAChB,qBAAqB;IACvB;IAEA,IAAI,YAAY;QACd,OAAO,CAAC,cAAc,GAAG,WAAW,QAAQ;IAC9C;IAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;QACE,SAAS;QACT,OAAO;QACP,MAAM,eAAe,MAAM,iBACrB,eAAe,MAAM,cACrB,eAAe,MAAM,iBAAiB;IAC9C,GACA;QACE,QAAQ;QACR;IACF;AAEJ;AAKO,eAAe,eACpB,MAAwC,EACxC,OAAoB,EACpB,OAAgB,EAChB,KAAc;IAEd,IAAI;QACF,uCAAuC;QACvC,MAAM,cAAc;YAClB,QAAQ,QAAQ,MAAM;YACtB,KAAK,QAAQ,GAAG;YAChB,WAAW,QAAQ,OAAO,CAAC,GAAG,CAAC;YAC/B,IAAI,QAAQ,OAAO,CAAC,GAAG,CAAC,sBACpB,QAAQ,OAAO,CAAC,GAAG,CAAC,gBACpB;YACJ,WAAW,KAAK,GAAG;YACnB;YACA;YACA,OAAO,QAAQ;YACf,aAAa,QAAQ;QACvB;QAEA,yFAAyF;QACzF,QAAQ,GAAG,CAAC,kBAAkB,KAAK,SAAS,CAAC,aAAa,MAAM;QAEhE,0CAA0C;QAC1C,IAAI,CAAC,WAAW,OAAO;YACrB,MAAM,iBAAiB,SAAS,QAAQ;QAC1C;QAEA,gCAAgC;QAChC,MAAM,wBAAwB,SAAS,QAAQ;IAEjD,EAAE,OAAO,UAAU;QACjB,QAAQ,KAAK,CAAC,gCAAgC;IAC9C,2DAA2D;IAC7D;AACF;AAKO,eAAe,iBACpB,OAAoB,EACpB,MAAoD,EACpD,KAAa;IAEb,IAAI;QACF,IAAI;QACJ,IAAI;QACJ,IAAI;QAEJ,mDAAmD;QACnD,IAAI,MAAM,QAAQ,CAAC,0BAA0B,MAAM,QAAQ,CAAC,oBAAoB;YAC9E,YAAY;YACZ,WAAW;YACX,cAAc;QAChB,OAAO,IAAI,MAAM,QAAQ,CAAC,wBAAwB;YAChD,YAAY;YACZ,WAAW;YACX,cAAc;QAChB,OAAO,IAAI,MAAM,QAAQ,CAAC,6BAA6B;YACrD,YAAY;YACZ,WAAW;YACX,cAAc;QAChB,OAAO;YACL,YAAY;YACZ,WAAW;YACX,cAAc,CAAC,oBAAoB,EAAE,OAAO;QAC9C;QAEA,MAAM,UAAU;YACd;YACA,WAAW,KAAK,GAAG;YACnB,aAAa,IAAI,IAAI,QAAQ,GAAG,EAAE,QAAQ;YAC1C,WAAW,CAAC,CAAC;QACf;QAEA,MAAM,OAAO,QAAQ,CAAC,IAAI,QAAQ,CAAC,gBAAgB,EAAE;YACnD,WAAW;YACX,UAAU;YACV;YACA;YACA,WAAW,QAAQ,OAAO,CAAC,GAAG,CAAC,sBACpB,QAAQ,OAAO,CAAC,GAAG,CAAC,gBACpB;YACX,WAAW,QAAQ,OAAO,CAAC,GAAG,CAAC,iBAAiB;YAChD,YAAY,QAAQ,GAAG;YACvB,eAAe,QAAQ,MAAM;YAC7B,UAAU,QAAQ;YAClB,mBAAmB,QAAQ;QAC7B;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;IACjD;AACF;AAKO,eAAe,wBACpB,OAAoB,EACpB,MAAoD,EACpD,OAAgB;IAEhB,IAAI;QACF,MAAM,KAAK,QAAQ,OAAO,CAAC,GAAG,CAAC,sBACrB,QAAQ,OAAO,CAAC,GAAG,CAAC,gBACpB;QAEV,kDAAkD;QAClD,IAAI,CAAC,SAAS;YACZ,mFAAmF;YACnF,qDAAqD;YACrD,MAAM,OAAO,QAAQ,CAAC,IAAI,QAAQ,CAAC,gBAAgB,EAAE;gBACnD,WAAW;gBACX,UAAU;gBACV,aAAa;gBACb,SAAS;oBACP;oBACA,WAAW,QAAQ,OAAO,CAAC,GAAG,CAAC;oBAC/B,aAAa,IAAI,IAAI,QAAQ,GAAG,EAAE,QAAQ;oBAC1C,WAAW,KAAK,GAAG;gBACrB;gBACA,WAAW;gBACX,WAAW,QAAQ,OAAO,CAAC,GAAG,CAAC,iBAAiB;gBAChD,YAAY,QAAQ,GAAG;gBACvB,eAAe,QAAQ,MAAM;gBAC7B,UAAU,QAAQ;gBAClB,mBAAmB,QAAQ;YAC7B;QACF;QAEA,wDAAwD;QACxD,IAAI,UAAU,SAAS;YACrB,MAAM,OAAO,IAAI,OAAO,QAAQ;YAEhC,wEAAwE;YACxE,IAAI,OAAO,KAAK,OAAO,IAAI;gBACzB,MAAM,OAAO,QAAQ,CAAC,IAAI,QAAQ,CAAC,gBAAgB,EAAE;oBACnD,WAAW;oBACX,UAAU;oBACV,aAAa;oBACb,SAAS;wBACP;wBACA;wBACA,OAAO,OAAO,KAAK;wBACnB,WAAW,KAAK,GAAG;oBACrB;oBACA,WAAW;oBACX,WAAW,QAAQ,OAAO,CAAC,GAAG,CAAC,iBAAiB;oBAChD,YAAY,QAAQ,GAAG;oBACvB,eAAe,QAAQ,MAAM;oBAC7B,UAAU,OAAO,KAAK;oBACtB,mBAAmB,OAAO,WAAW;gBACvC;YACF;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wCAAwC;IACxD;AACF;AAKO,SAAS,eACd,OAAkG,EAClG,UAGI,CAAC,CAAC;IAEN,OAAO,OAAO;QACZ,IAAI;YACF,+BAA+B;YAC/B,MAAM,eAAe,cAAc;YAEnC,IAAI,CAAC,cAAc;gBACjB,MAAM,eAAe,WAAW,SAAS,OAAO;gBAChD,OAAO,0BAA0B;YACnC;YAEA,mBAAmB;YACnB,MAAM,aAAa,MAAM,eAAe;YAExC,IAAI,CAAC,WAAW,OAAO,EAAE;gBACvB,MAAM,eAAe,WAAW,SAAS,OAAO,WAAW,KAAK;gBAChE,OAAO,0BACL,WAAW,KAAK,IAAI,mBACpB,WAAW,UAAU,IAAI;YAE7B;YAEA,gCAAgC;YAChC,IAAI,QAAQ,kBAAkB,IAAI,CAAC,cAAc,WAAW,MAAM,EAAE,QAAQ,kBAAkB,GAAG;gBAC/F,MAAM,eAAe,WAAW,MAAM,EAAE,SAAS,OAAO;gBACxD,OAAO,0BAA0B,4BAA4B;YAC/D;YAEA,qCAAqC;YACrC,IAAI,CAAC,QAAQ,aAAa,EAAE;gBAC1B,MAAM,iBAAiB,MAAM,eAAe,WAAW,MAAM;gBAE7D,IAAI,CAAC,eAAe,OAAO,EAAE;oBAC3B,MAAM,eAAe,WAAW,MAAM,EAAE,SAAS,OAAO,eAAe,KAAK;oBAC5E,OAAO,0BACL,eAAe,KAAK,IAAI,uBACxB,KACA,eAAe,UAAU;gBAE7B;gBAEA,6BAA6B;gBAC7B,MAAM,wBAAwB;YAChC;YAEA,gCAAgC;YAChC,MAAM,eAAe,WAAW,MAAM,EAAE,SAAS;YAEjD,iDAAiD;YACjD,OAAO,MAAM,QAAQ,SAAS,WAAW,MAAM;QAEjD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM,eAAe,WAAW,SAAS,OAAO;YAChD,OAAO,0BAA0B,yBAAyB;QAC5D;IACF;AACF", "debugId": null}}, {"offset": {"line": 460, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/app/api/v1/products/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { ConvexHttpClient } from 'convex/browser';\nimport { api } from '../../../../../convex/_generated/api';\nimport { withApiKeyAuth } from '@/lib/apiKeyAuth';\n\n// Initialize Convex client for server-side operations\nconst convex = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL!);\n\n/**\n * GET /api/v1/products\n * Retrieve products with API key authentication\n */\nexport const GET = withApiKeyAuth(\n  async (request: NextRequest, apiKey) => {\n    try {\n      const url = new URL(request.url);\n      const limit = parseInt(url.searchParams.get('limit') || '50');\n      const offset = parseInt(url.searchParams.get('offset') || '0');\n      const search = url.searchParams.get('search') || undefined;\n      const status = url.searchParams.get('status') || undefined;\n      const featured = url.searchParams.get('featured') === 'true' ? true : undefined;\n\n      // Validate limit\n      if (limit > 100) {\n        return NextResponse.json(\n          { \n            success: false, \n            error: 'Limit cannot exceed 100 items per request',\n            code: 'INVALID_LIMIT'\n          },\n          { status: 400 }\n        );\n      }\n\n      // Get products from Convex\n      const products = await convex.query(api.products.getProducts, {\n        limit,\n        offset,\n        search,\n        status: status as any,\n        featured,\n      });\n\n      // Transform products for API response (remove internal fields)\n      const apiProducts = products.map(product => ({\n        id: product.productId,\n        title: product.title,\n        description: product.description,\n        tags: product.tags,\n        collections: product.collections,\n        images: product.images,\n        priceRange: product.priceRange,\n        // Chemical-specific fields\n        purity: product.purity,\n        packaging: product.packaging,\n        casNumber: product.casNumber,\n        hsnNumber: product.hsnNumber,\n        molecularFormula: product.molecularFormula,\n        molecularWeight: product.molecularWeight,\n        appearance: product.appearance,\n        solubility: product.solubility,\n        phValue: product.phValue,\n        chemicalName: product.chemicalName,\n        features: product.features,\n        applications: product.applications,\n        applicationDetails: product.applicationDetails,\n        status: product.status,\n        featured: product.featured,\n        totalInventory: product.totalInventory,\n        createdAt: product.createdAt,\n        updatedAt: product.updatedAt,\n      }));\n\n      return NextResponse.json({\n        success: true,\n        data: apiProducts,\n        pagination: {\n          limit,\n          offset,\n          total: apiProducts.length,\n          hasMore: apiProducts.length === limit\n        },\n        meta: {\n          apiKeyId: apiKey.keyId,\n          environment: apiKey.environment,\n          timestamp: Date.now()\n        }\n      });\n\n    } catch (error) {\n      console.error('Products API error:', error);\n      return NextResponse.json(\n        { \n          success: false, \n          error: 'Internal server error',\n          code: 'INTERNAL_ERROR'\n        },\n        { status: 500 }\n      );\n    }\n  },\n  { \n    requiredPermission: 'products:read'\n  }\n);\n\n/**\n * POST /api/v1/products\n * Create a new product (requires write permission)\n */\nexport const POST = withApiKeyAuth(\n  async (request: NextRequest, apiKey) => {\n    try {\n      const body = await request.json();\n      \n      // Validate required fields\n      const requiredFields = ['title', 'description', 'priceRange'];\n      for (const field of requiredFields) {\n        if (!body[field]) {\n          return NextResponse.json(\n            { \n              success: false, \n              error: `Missing required field: ${field}`,\n              code: 'MISSING_FIELD'\n            },\n            { status: 400 }\n          );\n        }\n      }\n\n      // Create product via Convex\n      const productId = await convex.mutation(api.products.createProduct, {\n        ...body,\n        createdBy: apiKey.id, // Use API key ID as creator\n      });\n\n      return NextResponse.json({\n        success: true,\n        data: {\n          id: productId,\n          message: 'Product created successfully'\n        },\n        meta: {\n          apiKeyId: apiKey.keyId,\n          environment: apiKey.environment,\n          timestamp: Date.now()\n        }\n      }, { status: 201 });\n\n    } catch (error) {\n      console.error('Create product API error:', error);\n      return NextResponse.json(\n        { \n          success: false, \n          error: 'Failed to create product',\n          code: 'CREATE_FAILED'\n        },\n        { status: 500 }\n      );\n    }\n  },\n  { \n    requiredPermission: 'products:write'\n  }\n);\n\n/**\n * OPTIONS handler for CORS\n */\nexport async function OPTIONS(request: NextRequest) {\n  return new NextResponse(null, {\n    status: 200,\n    headers: {\n      'Access-Control-Allow-Origin': '*',\n      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\n      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',\n      'Access-Control-Max-Age': '86400',\n    },\n  });\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AACA;AACA;;;;;AAEA,sDAAsD;AACtD,MAAM,SAAS,IAAI,iKAAA,CAAA,mBAAgB;AAM5B,MAAM,MAAM,CAAA,GAAA,0HAAA,CAAA,iBAAc,AAAD,EAC9B,OAAO,SAAsB;IAC3B,IAAI;QACF,MAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;QAC/B,MAAM,QAAQ,SAAS,IAAI,YAAY,CAAC,GAAG,CAAC,YAAY;QACxD,MAAM,SAAS,SAAS,IAAI,YAAY,CAAC,GAAG,CAAC,aAAa;QAC1D,MAAM,SAAS,IAAI,YAAY,CAAC,GAAG,CAAC,aAAa;QACjD,MAAM,SAAS,IAAI,YAAY,CAAC,GAAG,CAAC,aAAa;QACjD,MAAM,WAAW,IAAI,YAAY,CAAC,GAAG,CAAC,gBAAgB,SAAS,OAAO;QAEtE,iBAAiB;QACjB,IAAI,QAAQ,KAAK;YACf,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,SAAS;gBACT,OAAO;gBACP,MAAM;YACR,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,2BAA2B;QAC3B,MAAM,WAAW,MAAM,OAAO,KAAK,CAAC,6HAAA,CAAA,MAAG,CAAC,QAAQ,CAAC,WAAW,EAAE;YAC5D;YACA;YACA;YACA,QAAQ;YACR;QACF;QAEA,+DAA+D;QAC/D,MAAM,cAAc,SAAS,GAAG,CAAC,CAAA,UAAW,CAAC;gBAC3C,IAAI,QAAQ,SAAS;gBACrB,OAAO,QAAQ,KAAK;gBACpB,aAAa,QAAQ,WAAW;gBAChC,MAAM,QAAQ,IAAI;gBAClB,aAAa,QAAQ,WAAW;gBAChC,QAAQ,QAAQ,MAAM;gBACtB,YAAY,QAAQ,UAAU;gBAC9B,2BAA2B;gBAC3B,QAAQ,QAAQ,MAAM;gBACtB,WAAW,QAAQ,SAAS;gBAC5B,WAAW,QAAQ,SAAS;gBAC5B,WAAW,QAAQ,SAAS;gBAC5B,kBAAkB,QAAQ,gBAAgB;gBAC1C,iBAAiB,QAAQ,eAAe;gBACxC,YAAY,QAAQ,UAAU;gBAC9B,YAAY,QAAQ,UAAU;gBAC9B,SAAS,QAAQ,OAAO;gBACxB,cAAc,QAAQ,YAAY;gBAClC,UAAU,QAAQ,QAAQ;gBAC1B,cAAc,QAAQ,YAAY;gBAClC,oBAAoB,QAAQ,kBAAkB;gBAC9C,QAAQ,QAAQ,MAAM;gBACtB,UAAU,QAAQ,QAAQ;gBAC1B,gBAAgB,QAAQ,cAAc;gBACtC,WAAW,QAAQ,SAAS;gBAC5B,WAAW,QAAQ,SAAS;YAC9B,CAAC;QAED,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,YAAY;gBACV;gBACA;gBACA,OAAO,YAAY,MAAM;gBACzB,SAAS,YAAY,MAAM,KAAK;YAClC;YACA,MAAM;gBACJ,UAAU,OAAO,KAAK;gBACtB,aAAa,OAAO,WAAW;gBAC/B,WAAW,KAAK,GAAG;YACrB;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO;YACP,MAAM;QACR,GACA;YAAE,QAAQ;QAAI;IAElB;AACF,GACA;IACE,oBAAoB;AACtB;AAOK,MAAM,OAAO,CAAA,GAAA,0HAAA,CAAA,iBAAc,AAAD,EAC/B,OAAO,SAAsB;IAC3B,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAE/B,2BAA2B;QAC3B,MAAM,iBAAiB;YAAC;YAAS;YAAe;SAAa;QAC7D,KAAK,MAAM,SAAS,eAAgB;YAClC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;gBAChB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBACE,SAAS;oBACT,OAAO,CAAC,wBAAwB,EAAE,OAAO;oBACzC,MAAM;gBACR,GACA;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,4BAA4B;QAC5B,MAAM,YAAY,MAAM,OAAO,QAAQ,CAAC,6HAAA,CAAA,MAAG,CAAC,QAAQ,CAAC,aAAa,EAAE;YAClE,GAAG,IAAI;YACP,WAAW,OAAO,EAAE;QACtB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ,IAAI;gBACJ,SAAS;YACX;YACA,MAAM;gBACJ,UAAU,OAAO,KAAK;gBACtB,aAAa,OAAO,WAAW;gBAC/B,WAAW,KAAK,GAAG;YACrB;QACF,GAAG;YAAE,QAAQ;QAAI;IAEnB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO;YACP,MAAM;QACR,GACA;YAAE,QAAQ;QAAI;IAElB;AACF,GACA;IACE,oBAAoB;AACtB;AAMK,eAAe,QAAQ,OAAoB;IAChD,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,MAAM;QAC5B,QAAQ;QACR,SAAS;YACP,+BAA+B;YAC/B,gCAAgC;YAChC,gCAAgC;YAChC,0BAA0B;QAC5B;IACF;AACF", "debugId": null}}]}