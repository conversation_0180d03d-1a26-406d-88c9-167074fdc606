"use client"

import { useState } from "react"
import Image from "next/image"
import Link from "next/link"
import { motion } from "framer-motion"
import { ShoppingBag, Info } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardFooter } from "@/components/ui/card"
import type { Product } from "@/lib/types"

export default function ProductCard({ product }: { product: Product }) {
  const [isHovered, setIsHovered] = useState(false)

  // Helper function to get the first collection title
  const getCategory = () => {
    return product.collections?.edges?.[0]?.node?.title || 'Uncategorized'
  }

  // Helper function to get the main image URL
  const getImageUrl = () => {
    return product.images?.edges?.[0]?.node?.url ||
           product.media?.edges?.[0]?.node?.image?.url ||
           "/placeholder.svg"
  }

  // Helper function to get the price
  const getPrice = () => {
    const minPrice = parseFloat(product.priceRange?.minVariantPrice?.amount || '0')
    const maxPrice = parseFloat(product.priceRange?.maxVariantPrice?.amount || '0')
    
    if (minPrice === maxPrice) {
      return minPrice.toFixed(2)
    }
    return `${minPrice.toFixed(2)} - ${maxPrice.toFixed(2)}`
  }

  const ComparedPrice = () => {
    const compareMixPrice = parseFloat(product.compareAtPriceRange?.maxVariantPrice?.amount || '0')
    const compareMinPrice = parseFloat(product.compareAtPriceRange?.minVariantPrice?.amount || '0')
    if (compareMinPrice === compareMixPrice) {
      return compareMinPrice.toFixed(2)
    }
    return `${compareMinPrice.toFixed(2)} - ${compareMixPrice.toFixed(2)}`
  }

  // Helper function to get metafield value
  const getMetafieldValue = (key: string) => {
    if (!product.metafields || !Array.isArray(product.metafields)) {
      console.warn(`No metafields found for product ${product.id}`)
      return ''
    }
    const metafield = product.metafields.find(m => m?.namespace === 'custom' && m?.key === key)
    if (!metafield) {
      console.warn(`Metafield ${key} not found for product ${product.id}`)
    }
    return metafield?.value || 'N/A'
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5 }}
    >
      <Card
        className="overflow-hidden border-0 bg-white shadow-sm transition-all duration-300 hover:shadow-md"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <div className="relative aspect-square overflow-hidden">
          <Image
            src={getImageUrl()}
            alt={product.title}
            fill
            className="object-cover transition-transform duration-500 hover:scale-105"
          />

          {isHovered && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="absolute inset-0 flex items-center justify-center bg-black/40"
            >
              <Link href={`/products/${product.id.split('/').pop()}`}>
                <Button variant="secondary" size="sm" className="mr-2">
                  <Info className="h-4 w-4 mr-1" />
                  Details
                </Button>
              </Link>
            </motion.div>
          )}

          <div className="absolute top-2 right-2 bg-teal-600 text-white text-xs font-medium px-2 py-1 rounded">
            {getCategory()}
          </div>
        </div>

        <CardContent className="p-4">
          <Link href={`/products/${product.id.split('/').pop()}`}>
            <h3 className="font-medium text-lg mb-1 hover:text-teal-600 transition-colors">{product.title}</h3>
          </Link>
          <p className="text-sm text-neutral-600 mb-2">{product.description.slice(0,100)}...</p>
          <div className="flex flex-wrap gap-2 text-xs">
            <span className="bg-neutral-100 px-2 py-1 rounded">Purity: {getMetafieldValue('purity')}</span>
            <span className="bg-neutral-100 px-2 py-1 rounded">{getMetafieldValue('package_size')}</span>
          </div>
        </CardContent>

        <CardFooter className="p-4 pt-0 flex items-center justify-between">
          <span className="font-medium line-through">₹{ComparedPrice()}</span>
          <span className="font-medium">₹{getPrice()}</span>
          <Button
            variant="ghost"
            size="sm"
            className="text-teal-600 hover:text-teal-700 hover:bg-teal-50"
          >
            <ShoppingBag className="h-4 w-4 mr-1" />
            Add to Cart
          </Button>
        </CardFooter>
      </Card>
    </motion.div>
  )
}
