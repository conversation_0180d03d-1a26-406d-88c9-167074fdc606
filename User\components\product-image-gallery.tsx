"use client"

import { useState } from "react"
import Image from "next/image"
import { motion, AnimatePresence } from "framer-motion"
import { ChevronLeft, ChevronRight, ZoomIn } from "lucide-react"
import { Button } from "@/components/ui/button"
import { <PERSON><PERSON>, DialogContent, DialogTrigger } from "@/components/ui/dialog"
import type { Product } from "@/lib/types"
import { DialogTitle } from "@radix-ui/react-dialog"

interface ProductImageGalleryProps {
  product: Product
}

export default function ProductImageGallery({ product }: ProductImageGalleryProps) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0)

  // Get all available images from the product
  const images = [
    product.featuredMedia?.preview?.image?.url,
    ...(product.media?.edges ? product.media.edges.map(edge => edge.node.image.url) : [])
  ].filter(Boolean) as string[]

  // If no images are available, use a placeholder
  if (images.length === 0) {
    images.push('/placeholder.svg')
  }

  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % images.length)
  }

  const prevImage = () => {
    setCurrentImageIndex((prev) => (prev - 1 + images.length) % images.length)
  }

  const setImage = (index: number) => {
    setCurrentImageIndex(index)
  }

  return (
    <div>
      <div className="relative aspect-square bg-neutral-50 rounded-lg overflow-hidden mb-4">
        <Dialog>
          <DialogTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              className="absolute top-4 right-4 bg-white/80 backdrop-blur-sm z-10 rounded-full"
            >
              <ZoomIn className="h-5 w-5" />
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl">
            <DialogTitle className="sr-only">Product Image</DialogTitle>
            <div className="relative aspect-square">
              <Image
                src={images[currentImageIndex]}
                alt={product.title}
                fill
                className="object-contain"
              />
            </div>
          </DialogContent>
        </Dialog>

        <AnimatePresence mode="wait">
          <motion.div
            key={currentImageIndex}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="relative aspect-square"
          >
            <Image
              src={images[currentImageIndex]}
              alt={product.title}
              fill
              className="object-contain"
            />
          </motion.div>
        </AnimatePresence>

        {images.length > 1 && (
          <>
            <Button
              variant="ghost"
              size="icon"
              className="absolute left-2 top-1/2 -translate-y-1/2 bg-white/80 backdrop-blur-sm z-10 rounded-full"
              onClick={prevImage}
            >
              <ChevronLeft className="h-5 w-5" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-2 top-1/2 -translate-y-1/2 bg-white/80 backdrop-blur-sm z-10 rounded-full"
              onClick={nextImage}
            >
              <ChevronRight className="h-5 w-5" />
            </Button>
          </>
        )}
      </div>

      {images.length > 1 && (
        <div className="grid grid-cols-4 gap-4">
          {images.map((image, index) => (
            <button
              key={index}
              onClick={() => setImage(index)}
              className={`relative aspect-square rounded-lg overflow-hidden border-2 transition-colors ${index === currentImageIndex ? 'border-teal-600' : 'border-transparent hover:border-teal-400'}`}
            >
              <Image src={image} alt={`${product.title} - View ${index + 1}`} fill className="object-cover" />
            </button>
          ))}
        </div>
      )}
    </div>
  )
}
