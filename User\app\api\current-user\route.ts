import { NextResponse } from "next/server";
import { storefrontFetch } from "@/lib/shopify";

// GraphQL query to get customer data with specific metafields
const CUSTOMER_QUERY = `
  query getCustomer($customerAccessToken: String!) {
    customer(customerAccessToken: $customerAccessToken) {
      id
      firstName
      lastName
      email
      phone
      defaultAddress {
        id
        address1
        address2
        city
        province
        country
        zip
      }
      businessName: metafield(namespace: "custom", key: "business_name") {
        value
      }
      gstNumber: metafield(namespace: "custom", key: "gstin") {
        value
      }
      gstStatus: metafield(namespace: "custom", key: "gst_status") {
        value
      }
      legalNameOfBusiness: metafield(namespace: "custom", key: "legal_name_of_business") {
        value
      }
      tradeName: metafield(namespace: "custom", key: "trade_name") {
        value
      }
      constitutionOfBusiness: metafield(namespace: "custom", key: "constitution_of_business") {
        value
      }
      taxpayerType: metafield(namespace: "custom", key: "taxpayer_type") {
        value
      }
      gstStatus: metafield(namespace: "custom", key: "gst_status") {
        value
      }
      principalPlaceOfBusiness: metafield(namespace: "custom", key: "principal_place_of_business_address") {
        value
      }
      natureOfCoreBusinessActivity: metafield(namespace: "custom", key: "nature_of_core_business_activity") {
        value
      }
    }
  }
`;

export async function GET(request: Request) {
  try {
    // Get the customer access token from the request headers
    const customerAccessToken = request.headers.get('x-customer-access-token');

    if (!customerAccessToken) {
      return NextResponse.json(
        { error: "Unauthorized - No customer access token provided" },
        { status: 401 }
      );
    }

    console.log("🔍 [current-user API] Received token:", customerAccessToken ? `${customerAccessToken.substring(0, 10)}...` : 'NONE');

    // Fetch customer data from Shopify
    const { data, errors } = await storefrontFetch<{ customer: any }>({
      query: CUSTOMER_QUERY,
      variables: { customerAccessToken }
    });

    if (errors) {
      console.error("GraphQL errors:", errors);
      return NextResponse.json(
        { error: "Failed to fetch customer data" },
        { status: 500 }
      );
    }

    const customerData = data?.customer;

    if (!customerData) {
      console.log("❌ No customer data found - token may be invalid or expired");
      return NextResponse.json(
        { error: "Unauthorized - Invalid or expired customer access token" },
        { status: 401 }
      );
    }

    console.log("✅ [current-user API] Customer data retrieved successfully");
    console.log("🏠 [current-user API] Address data:", customerData.defaultAddress);

    // Extract user data from Shopify customer
    const gstStatus = customerData.gstStatus?.value || "";
    const isGstVerified = gstStatus === "Verified" || gstStatus === "Active"; // Determine verification from status

    const userData = {
      id: customerData.id,
      email: customerData.email || "",
      firstName: customerData.firstName || "",
      lastName: customerData.lastName || "",
      name: `${customerData.firstName || ""} ${customerData.lastName || ""}`.trim(),
      phone: customerData.phone || "",
      defaultAddress: customerData.defaultAddress,
      // Extract business data from metafields (using correct field mappings)
      businessName: customerData.businessName?.value || "", // This is the actual business_name from custom metafields
      gstNumber: customerData.gstNumber?.value || "",
      isGstVerified: isGstVerified,
      role: "customer",
      shopifyCustomerId: customerData.id,
      // Add timestamp for real-time tracking
      lastUpdated: new Date().toISOString(),
      // Add additional business information from metafields
      legalNameOfBusiness: customerData.legalNameOfBusiness?.value || "",
      tradeName: customerData.tradeName?.value || "",
      constitutionOfBusiness: customerData.constitutionOfBusiness?.value || "",
      taxpayerType: customerData.taxpayerType?.value || "",
      gstStatus: gstStatus,
      principalPlaceOfBusiness: customerData.principalPlaceOfBusiness?.value || "",
      natureOfCoreBusinessActivity: customerData.natureOfCoreBusinessActivity?.value || ""
    };

    return NextResponse.json(userData);
  } catch (error) {
    console.error("Error fetching current user:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
