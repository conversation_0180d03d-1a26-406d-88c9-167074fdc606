import { NextResponse } from 'next/server';
import { getShopifyConfig } from '@/lib/env-validation';

export async function POST(request: Request) {
  let shopifyConfig;

  try {
    shopifyConfig = getShopifyConfig(true); // Include server-side variables
    console.log('🔧 Shopify configuration loaded for phone verification');
  } catch (error) {
    console.error("Shopify environment variables are not properly configured:", error);
    return NextResponse.json({
      isAvailable: false,
      message: "Server configuration error."
    }, { status: 500 });
  }

  try {
    const { phone, countryCode } = await request.json();

    if (!phone || typeof phone !== 'string') {
      return NextResponse.json({ 
        isAvailable: false, 
        message: "Phone number is required." 
      }, { status: 400 });
    }

    // Basic phone validation (digits only, 10+ characters)
    const phoneDigits = phone.replace(/\D/g, '');
    if (phoneDigits.length < 10) {
      return NextResponse.json({ 
        isAvailable: false, 
        message: "Phone number must be at least 10 digits." 
      }, { status: 400 });
    }

    // Format phone number with country code
    const fullPhone = `${countryCode || '+91'}${phoneDigits}`;
    
    // Also search for phone without country code and with different formats
    const phoneVariations = [
      fullPhone,
      phoneDigits,
      phone,
      `+91${phoneDigits}`, // Common India format
      `91${phoneDigits}`,   // Without +
    ];

    console.log(`Checking phone availability in Shopify: ${fullPhone}`);

    // Check each phone variation
    for (const phoneVariation of phoneVariations) {
      const shopifyUrl = `https://${shopifyConfig.storeDomain}/admin/api/${shopifyConfig.apiVersion}/customers/search.json?query=phone:${encodeURIComponent(phoneVariation)}`;

      const response = await fetch(shopifyUrl, {
        method: 'GET',
        headers: {
          'X-Shopify-Access-Token': shopifyConfig.adminAccessToken!,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        console.error(`Shopify API error for phone ${phoneVariation}: ${response.status} ${response.statusText}`);
        continue; // Try next variation
      }

      const data = await response.json();
      console.log(`Shopify phone search response for ${phoneVariation}:`, JSON.stringify(data, null, 2));

      if (data.customers && data.customers.length > 0) {
        // Phone number is already taken
        return NextResponse.json({
          isAvailable: false,
          message: "This phone number is already registered. Please use a different phone number or sign in.",
          phone: fullPhone
        }, { status: 200 });
      }
    }

    // Phone number is available
    return NextResponse.json({
      isAvailable: true,
      message: "Phone number is available.",
      phone: fullPhone
    }, { status: 200 });

  } catch (error: any) {
    console.error("API Error verifying phone:", error);
    let message = "An unexpected error occurred while checking phone availability.";
    if (error.message) {
      message = error.message;
    }
    return NextResponse.json({ 
      isAvailable: false, 
      message 
    }, { status: 500 });
  }
}
