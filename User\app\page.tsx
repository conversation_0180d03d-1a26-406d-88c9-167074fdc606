import type { Metadata } from "next"
import { Suspense } from "react" // Added
import Hero from "@/components/hero"
import CategorySection from "@/components/category-section"
import FeaturedProducts from "@/components/featured-products"
import FeaturedProductsSkeleton from "@/components/featured-products-skeleton" // Added
import Newsletter from "@/components/newsletter"

export const metadata: Metadata = {
  title: "Benzochem Industries | Premium Chemical Products",
  description:
    "Specialized chemical trading company offering high-quality powder and liquid products for industrial applications.",
}

export default function Home() {
  return (
    <main className="flex min-h-screen flex-col">
      <Hero />
      <div className="container mx-auto px-4 py-16 md:py-24">
        <h2 className="text-3xl font-medium text-center mb-16">Our Product Categories</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <CategorySection
            title="Powder Products"
            description="High-purity powder chemicals for industrial applications"
            image="https://images.unsplash.com/photo-1532187863486-abf9dbad1b69"
            href="/categories/powder"
          />
          <CategorySection
            title="Liquid Products"
            description="Premium liquid solutions for specialized chemical processes"
            image="https://images.visualelectric.com/08e4505c-6e36-4d72-91f0-74a3d8b72982/large"
            href="/categories/liquid"
          />
        </div>
      </div>
      <div className="bg-neutral-50 py-16 md:py-24">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-medium text-center mb-16">Featured Products</h2>
          <Suspense fallback={<FeaturedProductsSkeleton />}>
            <FeaturedProducts />
          </Suspense>
        </div>
      </div>
      <Newsletter />
    </main>
  )
}
