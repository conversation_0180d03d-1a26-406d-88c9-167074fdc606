"use client"

import { motion } from "framer-motion"
import { MapPin, Phone, Mail, Clock, Building } from "lucide-react"

export default function ContactInfo() {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.2 }}
    >
      <h2 className="text-2xl font-medium mb-6">Contact Information</h2>

      <div className="space-y-8">
        <div className="flex">
          <div className="w-12 h-12 rounded-full bg-teal-100 flex items-center justify-center mr-4 flex-shrink-0">
            <Building className="h-5 w-5 text-teal-600" />
          </div>
          <div>
            <h3 className="text-lg font-medium mb-1">Headquarters</h3>
            <p className="text-neutral-600">
              Our main office and manufacturing facility is located in New York, with additional distribution centers
              across North America, Europe, and Asia.
            </p>
          </div>
        </div>

        <div className="flex">
          <div className="w-12 h-12 rounded-full bg-teal-100 flex items-center justify-center mr-4 flex-shrink-0">
            <MapPin className="h-5 w-5 text-teal-600" />
          </div>
          <div>
            <h3 className="text-lg font-medium mb-1">Address</h3>
            <p className="text-neutral-600">123 Chemical Lane, Industrial District</p>
            <p className="text-neutral-600">New York, NY 10001</p>
            <p className="text-neutral-600">United States</p>
          </div>
        </div>

        <div className="flex">
          <div className="w-12 h-12 rounded-full bg-teal-100 flex items-center justify-center mr-4 flex-shrink-0">
            <Phone className="h-5 w-5 text-teal-600" />
          </div>
          <div>
            <h3 className="text-lg font-medium mb-1">Phone</h3>
            <p className="text-neutral-600">Main: +1 (234) 567-890</p>
            <p className="text-neutral-600">Customer Service: +1 (234) 567-891</p>
            <p className="text-neutral-600">Technical Support: +1 (234) 567-892</p>
          </div>
        </div>

        <div className="flex">
          <div className="w-12 h-12 rounded-full bg-teal-100 flex items-center justify-center mr-4 flex-shrink-0">
            <Mail className="h-5 w-5 text-teal-600" />
          </div>
          <div>
            <h3 className="text-lg font-medium mb-1">Email</h3>
            <p className="text-neutral-600">General Inquiries: <EMAIL></p>
            <p className="text-neutral-600">Sales: <EMAIL></p>
            <p className="text-neutral-600">Support: <EMAIL></p>
          </div>
        </div>

        <div className="flex">
          <div className="w-12 h-12 rounded-full bg-teal-100 flex items-center justify-center mr-4 flex-shrink-0">
            <Clock className="h-5 w-5 text-teal-600" />
          </div>
          <div>
            <h3 className="text-lg font-medium mb-1">Business Hours</h3>
            <p className="text-neutral-600">Monday - Friday: 8:00 AM - 6:00 PM EST</p>
            <p className="text-neutral-600">Saturday: 9:00 AM - 1:00 PM EST</p>
            <p className="text-neutral-600">Sunday: Closed</p>
          </div>
        </div>
      </div>
    </motion.div>
  )
}
