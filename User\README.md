# Benzochem Industries - Shopify Integration

This project integrates a Next.js e-commerce site with Shopify using the Storefront API and Admin API.

## Setup Instructions

### 1. Environment Variables

Create a `.env.local` file in the root of your project with the following variables:

\`\`\`
SHOPIFY_STORE_DOMAIN=your-store.myshopify.com
SHOPIFY_STOREFRONT_ACCESS_TOKEN=your-storefront-access-token
SHOPIFY_ADMIN_API_ACCESS_TOKEN=your-admin-access-token
SHOPIFY_API_VERSION=2023-10
SHOPIFY_WEBHOOK_SECRET=your-webhook-secret
\`\`\`

### 2. Shopify Store Setup

1. Create a Shopify Partner account if you don't have one
2. Create a development store
3. Create a private app to get your API credentials
4. Set up the necessary access scopes for both Storefront and Admin APIs

### 3. Product Metafields

For the product data to match our application's structure, set up the following metafields in Shopify:

- Namespace: `product`
- Metafields:
  - `purity` (string)
  - `packaging` (string)
  - `cas_number` (string)
  - `molecular_formula` (string)
  - `molecular_weight` (string)
  - `appearance` (string)
  - `solubility` (string)
  - `ph_value` (string)
  - `chemical_name` (string)
  - `features` (string - pipe-separated values)
  - `applications` (string - pipe-separated values)
  - `application_details` (string - pipe-separated values)

### 4. Webhooks

Set up the following webhooks in your Shopify admin:

- Products: create, update, delete
- Orders: create, update
- Customers: create, update

Point these webhooks to your deployed application's webhook endpoint:
`https://your-domain.com/api/webhooks/shopify`

## Development

Run the development server:

\`\`\`bash
npm run dev
\`\`\`

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.
