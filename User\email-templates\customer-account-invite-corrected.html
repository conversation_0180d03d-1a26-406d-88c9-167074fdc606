<!-- 
CORRECTED Customer Account Invite Email Template for Shopify
Copy this HTML and paste it into Shopify Admin > Settings > Notifications > Customer account invite

Key Fix: Uses {{ account_activation_url }} instead of {{ customer_url }}
-->

<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #ffffff;">
  <!-- Header -->
  <div style="background-color: #14b8a6; color: white; padding: 20px; text-align: center;">
    <h2 style="margin: 0; font-size: 24px;">Welcome to {{ shop.name }}!</h2>
  </div>

  <!-- Main Content -->
  <div style="padding: 30px 20px;">
    <p style="font-size: 16px; color: #333; margin-bottom: 20px;">Hi {{ customer.first_name }},</p>

    <p style="font-size: 16px; color: #333; line-height: 1.6; margin-bottom: 25px;">
      Thank you for registering with Benzochem Industries! We're excited to have you as part of our business community.
    </p>

    <!-- Premium Message Box -->
    <div style="background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%); padding: 30px; border-radius: 12px; margin: 35px 0; border-left: 6px solid #14b8a6; box-shadow: 0 8px 25px rgba(0,0,0,0.08);">
      <div style="font-size: 15px; color: #475569; line-height: 1.7;">
        You're just one step away from accessing our premium chemical products and exclusive business pricing.
      </div>
    </div>

    <p style="font-size: 16px; color: #333; margin-bottom: 25px;">
      To complete your registration and activate your account, please click the button below:
    </p>

    <!-- Activation Button -->
    <div style="text-align: center; margin: 30px 0;">
      <a href="{{ account_activation_url }}" 
         style="background-color: #14b8a6; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; display: inline-block; font-size: 16px; font-weight: bold; box-shadow: 0 4px 12px rgba(20, 184, 166, 0.3);">
        Activate Your Account
      </a>
    </div>

    <p style="font-size: 14px; color: #666; margin-bottom: 10px;">
      Or copy and paste this link into your browser:
    </p>
    <p style="background-color: #f8f9fa; padding: 15px; border-radius: 6px; word-break: break-all; font-size: 14px; color: #333; border: 1px solid #e2e8f0;">
      {{ account_activation_url }}
    </p>

    <!-- What's Next Section -->
    <div style="background-color: #f0f9ff; padding: 25px; border-radius: 8px; margin: 30px 0; border: 1px solid #e0f2fe;">
      <h3 style="color: #14b8a6; margin-top: 0; margin-bottom: 15px; font-size: 18px;">What's Next?</h3>
      <ul style="color: #475569; line-height: 1.8; margin: 0; padding-left: 20px;">
        <li>Browse our extensive chemical product catalog</li>
        <li>Access exclusive business pricing</li>
        <li>Track your orders and shipments</li>
        <li>Manage your business profile</li>
      </ul>
    </div>

    <!-- Important Notice -->
    <div style="background-color: #fef3c7; padding: 20px; border-radius: 8px; margin: 25px 0; border-left: 4px solid #f59e0b;">
      <p style="margin: 0; color: #92400e; font-size: 14px;">
        <strong>Important:</strong> This activation link will expire in 24 hours for security reasons. If you need a new link, please contact our support team.
      </p>
    </div>

    <p style="font-size: 16px; color: #333; margin-bottom: 10px;">
      If you have any questions, please don't hesitate to contact our support team.
    </p>

    <p style="font-size: 16px; color: #333; margin-bottom: 0;">
      Best regards,<br>
      <strong>The Benzochem Industries Team</strong>
    </p>
  </div>

  <!-- Footer -->
  <div style="background-color: #f8f9fa; padding: 20px; text-align: center; font-size: 12px; color: #666; border-top: 1px solid #e2e8f0;">
    <p style="margin: 0 0 5px 0;">© {{ "now" | date: "%Y" }} {{ shop.name }}. All rights reserved.</p>
    <p style="margin: 0;">{{ shop.address.address1 }}, {{ shop.address.city }}, {{ shop.address.province }} {{ shop.address.zip }}</p>
  </div>
</div>

<!-- 
SHOPIFY VARIABLES REFERENCE:
- {{ shop.name }} - Your store name
- {{ customer.first_name }} - Customer's first name
- {{ customer.last_name }} - Customer's last name
- {{ customer.email }} - Customer's email
- {{ account_activation_url }} - The activation link (CORRECT VARIABLE)
- {{ shop.address.address1 }} - Store address
- {{ shop.address.city }} - Store city
- {{ shop.address.province }} - Store province/state
- {{ shop.address.zip }} - Store zip code
- {{ "now" | date: "%Y" }} - Current year

IMPORTANT: 
- Use {{ account_activation_url }} NOT {{ customer_url }}
- The activation URL is automatically generated by Shopify
- This template should be used in the "Customer account invite" notification
-->
