// Debug script to test login functionality
// Run this in the browser console to debug login issues

window.debugLogin = async function(email, password) {
  console.log("🔍 Starting login debug for:", email);
  
  try {
    // Step 1: Test authentication
    console.log("Step 1: Testing authentication...");
    const authResponse = await fetch('https://benzochem.myshopify.com/api/2025-04/graphql.json', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Storefront-Access-Token': '95029391ce9ba8bad8a604e7faf7eb87'
      },
      body: JSON.stringify({
        query: `
          mutation customerAccessTokenCreate($input: CustomerAccessTokenCreateInput!) {
            customerAccessTokenCreate(input: $input) {
              customerAccessToken {
                accessToken
                expiresAt
              }
              customerUserErrors {
                field
                message
                code
              }
            }
          }
        `,
        variables: {
          input: {
            email: email,
            password: password
          }
        }
      })
    });

    const authResult = await authResponse.json();
    console.log("Authentication result:", authResult);

    if (authResult.data?.customerAccessTokenCreate?.customerUserErrors?.length > 0) {
      console.error("❌ Authentication failed:", authResult.data.customerAccessTokenCreate.customerUserErrors);
      return;
    }

    const accessToken = authResult.data?.customerAccessTokenCreate?.customerAccessToken?.accessToken;
    if (!accessToken) {
      console.error("❌ No access token received");
      return;
    }

    console.log("✅ Authentication successful, access token:", accessToken.substring(0, 20) + "...");

    // Step 2: Test customer data fetch
    console.log("Step 2: Testing customer data fetch...");
    const customerResponse = await fetch('https://benzochem.myshopify.com/api/2025-04/graphql.json', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Storefront-Access-Token': '95029391ce9ba8bad8a604e7faf7eb87'
      },
      body: JSON.stringify({
        query: `
          query getCustomer($customerAccessToken: String!) {
            customer(customerAccessToken: $customerAccessToken) {
              id
              firstName
              lastName
              email
              phone
              acceptsMarketing
              createdAt
              metafields(first: 10) {
                edges {
                  node {
                    namespace
                    key
                    value
                  }
                }
              }
              defaultAddress {
                id
                address1
                address2
                city
                province
                country
                zip
                phone
              }
              orders(first: 10) {
                edges {
                  node {
                    id
                    orderNumber
                    processedAt
                    financialStatus
                    fulfillmentStatus
                    totalPriceV2 {
                      amount
                      currencyCode
                    }
                  }
                }
              }
            }
          }
        `,
        variables: {
          customerAccessToken: accessToken
        }
      })
    });

    const customerResult = await customerResponse.json();
    console.log("Customer data result:", customerResult);

    if (customerResult.data?.customer) {
      console.log("✅ Customer data fetched successfully:");
      console.log("- ID:", customerResult.data.customer.id);
      console.log("- Email:", customerResult.data.customer.email);
      console.log("- Name:", customerResult.data.customer.firstName, customerResult.data.customer.lastName);
      console.log("- Metafields:", customerResult.data.customer.metafields?.edges?.length || 0);
    } else {
      console.error("❌ No customer data received");
      console.log("Full response:", customerResult);
    }

    // Step 3: Test localStorage operations
    console.log("Step 3: Testing localStorage operations...");
    
    // Save token
    localStorage.setItem('shopify_customer_access_token', accessToken);
    localStorage.setItem('shopify_customer_access_token_expires', authResult.data.customerAccessTokenCreate.customerAccessToken.expiresAt);
    
    // Verify token was saved
    const savedToken = localStorage.getItem('shopify_customer_access_token');
    const savedExpiry = localStorage.getItem('shopify_customer_access_token_expires');
    
    console.log("Token saved:", savedToken ? "✅" : "❌");
    console.log("Expiry saved:", savedExpiry ? "✅" : "❌");
    
    if (savedToken) {
      console.log("Saved token matches:", savedToken === accessToken ? "✅" : "❌");
    }

    console.log("🎉 Login debug completed successfully!");
    
  } catch (error) {
    console.error("❌ Login debug failed:", error);
  }
};

console.log("🧪 Login debug helper loaded!");
console.log("Usage: window.debugLogin('<EMAIL>', 'password')");
