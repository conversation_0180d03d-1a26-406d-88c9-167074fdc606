"use client"

import { Card, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import OrderHistory from "@/components/order-history" // Assuming this path is correct

// Props can be added here if this component becomes dynamic in the future
// interface OrderHistoryTabProps {}

export default function OrderHistoryTab(/*props: OrderHistoryTabProps*/) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Order History</CardTitle>
        <CardDescription>
          View and track all your orders
        </CardDescription>
      </CardHeader>
      <CardContent>
        <OrderHistory />
      </CardContent>
    </Card>
  )
}