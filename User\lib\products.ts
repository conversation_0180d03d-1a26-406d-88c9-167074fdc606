import { getProducts } from './product-service'
import type { Product } from './types'

// Get products filtered by collection
export async function getProductsByCollection(collectionTitle: string): Promise<Product[]> {
  try {
    const allProducts = await getProducts()
    
    // Filter products by collection title
    return allProducts.filter(product => 
      product.collections?.edges?.some(edge => 
        edge.node.title.toLowerCase() === collectionTitle.toLowerCase()
      ) ?? false
    )
  } catch (error) {
    console.error(`Error fetching products for collection ${collectionTitle}:`, error)
    return []
  }
}
