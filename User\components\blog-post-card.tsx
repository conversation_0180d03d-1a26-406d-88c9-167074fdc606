"use client"

import { motion } from "framer-motion"
import Image from "next/image"
import Link from "next/link"
import { ArrowRight } from "lucide-react"
import type { BlogPost } from "@/lib/types"

interface BlogPostCardProps {
  post: BlogPost
}

export default function BlogPostCard({ post }: BlogPostCardProps) {
  return (
    <motion.article
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5 }}
      className="group bg-white rounded-lg overflow-hidden shadow-sm"
    >
      <Link href={`/blog/${post.slug}`} className="block">
        <div className="relative aspect-[16/9] overflow-hidden">
          <Image
            src={post.image || "/placeholder.svg"}
            alt={post.title}
            fill
            className="object-cover transition-transform duration-500 group-hover:scale-105"
          />
        </div>
      </Link>

      <div className="p-6">
        <div className="flex items-center mb-4">
          <span className="text-xs font-medium bg-teal-100 text-teal-800 px-2 py-1 rounded-full mr-3">
            {post.category}
          </span>
          <span className="text-xs text-neutral-500">{post.date}</span>
        </div>

        <Link href={`/blog/${post.slug}`} className="block group-hover:text-teal-600 transition-colors">
          <h3 className="text-xl font-medium mb-3">{post.title}</h3>
        </Link>

        <p className="text-neutral-600 text-sm mb-4 line-clamp-3">{post.excerpt}</p>

        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="relative w-8 h-8 rounded-full overflow-hidden mr-2">
              <Image
                src={post.author.avatar || "/placeholder.svg"}
                alt={post.author.name}
                fill
                className="object-cover"
              />
            </div>
            <span className="text-sm font-medium">{post.author.name}</span>
          </div>

          <Link
            href={`/blog/${post.slug}`}
            className="text-sm font-medium text-teal-600 hover:text-teal-700 inline-flex items-center"
          >
            Read More
            <ArrowRight className="ml-1 h-3 w-3" />
          </Link>
        </div>
      </div>
    </motion.article>
  )
}
