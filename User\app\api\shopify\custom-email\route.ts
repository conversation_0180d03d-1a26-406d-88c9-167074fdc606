import { NextRequest, NextResponse } from 'next/server'
import { getShopifyConfig } from '@/lib/env-validation'

// Custom email sending API for Shopify
export async function POST(request: NextRequest) {
  let shopifyConfig;
  
  try {
    shopifyConfig = getShopifyConfig(true); // Need admin access for email sending
    console.log('🔧 Shopify configuration loaded for custom email');
  } catch (error) {
    console.error("Shopify environment variables are not properly configured:", error);
    return NextResponse.json(
      { error: 'Server configuration error' },
      { status: 500 }
    )
  }

  try {
    const { customerId, emailType, customData } = await request.json()

    if (!customerId || !emailType) {
      return NextResponse.json(
        { error: 'Customer ID and email type are required' },
        { status: 400 }
      )
    }

    // Custom email templates
    const emailTemplates = {
      welcome: {
        subject: `Welcome to ${customData?.shopName || 'Benzochem Industries'}!`,
        body: generateWelcomeEmail(customData)
      },
      activation: {
        subject: 'Activate Your Benzochem Industries Account',
        body: generateActivationEmail(customData)
      },
      verification: {
        subject: 'Verify Your Email Address',
        body: generateVerificationEmail(customData)
      }
    }

    const template = emailTemplates[emailType as keyof typeof emailTemplates]
    
    if (!template) {
      return NextResponse.json(
        { error: 'Invalid email type' },
        { status: 400 }
      )
    }

    // Send email via Shopify Admin API (if available) or external service
    // Note: Shopify doesn't have a direct "send custom email" API
    // You would typically use a service like SendGrid, Mailgun, etc.
    
    console.log('Custom email would be sent:', {
      customerId,
      emailType,
      subject: template.subject,
      bodyPreview: template.body.substring(0, 100) + '...'
    })

    return NextResponse.json({
      success: true,
      message: 'Custom email template generated',
      template: template
    })

  } catch (error) {
    console.error('Custom email API error:', error)
    return NextResponse.json(
      { error: 'Failed to process custom email request' },
      { status: 500 }
    )
  }
}

function generateWelcomeEmail(data: any) {
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <div style="background-color: #14b8a6; color: white; padding: 20px; text-align: center;">
        <h1>Welcome to Benzochem Industries!</h1>
      </div>
      
      <div style="padding: 20px;">
        <p>Hi ${data?.firstName || 'Valued Customer'},</p>
        
        <p>Thank you for joining Benzochem Industries! We're excited to have you as part of our business community.</p>
        
        <div style="background-color: #f0f9ff; padding: 15px; border-radius: 8px; margin: 20px 0;">
          <h3 style="color: #14b8a6; margin-top: 0;">What's Next?</h3>
          <ul>
            <li>Browse our extensive chemical product catalog</li>
            <li>Access exclusive business pricing</li>
            <li>Track your orders and shipments</li>
            <li>Manage your business profile and GST details</li>
          </ul>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${data?.accountUrl || '#'}" 
             style="background-color: #14b8a6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
            Access Your Account
          </a>
        </div>
        
        <p>If you have any questions, please don't hesitate to contact our support team.</p>
        
        <p>Best regards,<br>
        The Benzochem Industries Team</p>
      </div>
      
      <div style="background-color: #f8f9fa; padding: 15px; text-align: center; font-size: 12px; color: #666;">
        <p>© ${new Date().getFullYear()} Benzochem Industries. All rights reserved.</p>
      </div>
    </div>
  `
}

function generateActivationEmail(data: any) {
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <div style="background-color: #14b8a6; color: white; padding: 20px; text-align: center;">
        <h1>Activate Your Account</h1>
      </div>
      
      <div style="padding: 20px;">
        <p>Hi ${data?.firstName || 'there'},</p>
        
        <p>You're almost ready to start using your Benzochem Industries account! Just click the button below to activate your account:</p>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${data?.activationUrl || '#'}" 
             style="background-color: #14b8a6; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; display: inline-block; font-size: 16px;">
            Activate Your Account
          </a>
        </div>
        
        <p>Or copy and paste this link into your browser:</p>
        <p style="background-color: #f8f9fa; padding: 10px; border-radius: 4px; word-break: break-all;">
          ${data?.activationUrl || '#'}
        </p>
        
        <div style="background-color: #fef3c7; padding: 15px; border-radius: 8px; margin: 20px 0;">
          <p style="margin: 0;"><strong>Important:</strong> This activation link will expire in 24 hours for security reasons.</p>
        </div>
        
        <p>Once activated, you'll be able to:</p>
        <ul>
          <li>Access our full product catalog</li>
          <li>View business pricing</li>
          <li>Place orders online</li>
          <li>Track shipments</li>
        </ul>
        
        <p>If you didn't create this account, please ignore this email.</p>
        
        <p>Best regards,<br>
        The Benzochem Industries Team</p>
      </div>
      
      <div style="background-color: #f8f9fa; padding: 15px; text-align: center; font-size: 12px; color: #666;">
        <p>© ${new Date().getFullYear()} Benzochem Industries. All rights reserved.</p>
      </div>
    </div>
  `
}

function generateVerificationEmail(data: any) {
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <div style="background-color: #14b8a6; color: white; padding: 20px; text-align: center;">
        <h1>Verify Your Email</h1>
      </div>
      
      <div style="padding: 20px;">
        <p>Hi ${data?.firstName || 'there'},</p>
        
        <p>Please verify your email address to complete your Benzochem Industries registration:</p>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${data?.verificationUrl || '#'}" 
             style="background-color: #14b8a6; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; display: inline-block; font-size: 16px;">
            Verify Email Address
          </a>
        </div>
        
        <p>This helps us ensure the security of your account and keep you updated on your orders.</p>
        
        <p>If you didn't request this verification, please ignore this email.</p>
        
        <p>Best regards,<br>
        The Benzochem Industries Team</p>
      </div>
      
      <div style="background-color: #f8f9fa; padding: 15px; text-align: center; font-size: 12px; color: #666;">
        <p>© ${new Date().getFullYear()} Benzochem Industries. All rights reserved.</p>
      </div>
    </div>
  `
}
