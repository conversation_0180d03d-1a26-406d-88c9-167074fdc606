import { NextRequest, NextResponse } from 'next/server'
import { updateCustomerBasicInfo, updateCustomerAddress } from '@/actions/shopifyActions'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { customerId, firstName, lastName, address } = body

    // Validate required fields
    if (!customerId) {
      return NextResponse.json(
        { success: false, error: 'Customer ID is required' },
        { status: 400 }
      )
    }

    // Validate that at least one field is being updated
    if (!firstName && !lastName && !address) {
      return NextResponse.json(
        { success: false, error: 'At least one field must be provided for update' },
        { status: 400 }
      )
    }

    console.log('🔄 Updating customer:', customerId, 'with data:', { firstName, lastName, address })

    let finalResult: any = { success: true, customer: null }

    // Update basic info (firstName, lastName) if provided
    if (firstName !== undefined || lastName !== undefined) {
      console.log('Updating basic info:', { firstName, lastName })
      const basicInfoData: any = {}

      if (firstName !== undefined) {
        basicInfoData.firstName = firstName
      }

      if (lastName !== undefined) {
        basicInfoData.lastName = lastName
      }

      const basicInfoResult = await updateCustomerBasicInfo(customerId, basicInfoData)

      if (!basicInfoResult.success) {
        console.error('❌ Failed to update basic info:', basicInfoResult.error)
        return NextResponse.json(
          { success: false, error: basicInfoResult.error },
          { status: 400 }
        )
      }

      finalResult.customer = basicInfoResult.customer
      console.log('✅ Basic info updated successfully')
    }

    // Update address if provided
    if (address) {
      console.log('Updating address:', address)

      // Validate required address fields
      if (!address.country || address.country.trim() === '') {
        console.error('❌ Country is required for address update')
        return NextResponse.json(
          { success: false, error: 'Country is required' },
          { status: 400 }
        )
      }

      if (!address.city || address.city.trim() === '') {
        console.error('❌ City is required for address update')
        return NextResponse.json(
          { success: false, error: 'City is required' },
          { status: 400 }
        )
      }

      const addressData = {
        address1: address.address1 || '',
        address2: address.address2 || '',
        city: address.city || '',
        province: address.province || '',
        country: address.country || '',
        zip: address.zip || ''
      }

      const addressResult = await updateCustomerAddress(customerId, addressData)

      if (!addressResult.success) {
        console.error('❌ Failed to update address:', addressResult.error)
        return NextResponse.json(
          { success: false, error: addressResult.error },
          { status: 400 }
        )
      }

      // Use the address result customer data if available, otherwise keep the basic info result
      if (addressResult.customer) {
        finalResult.customer = addressResult.customer
      }
      console.log('✅ Address updated successfully')
    }

    if (finalResult.success) {
      console.log('✅ Customer updated successfully')
      return NextResponse.json({
        success: true,
        message: 'Customer information updated successfully',
        customer: finalResult.customer
      })
    } else {
      console.error('❌ Failed to update customer:', finalResult.error)
      return NextResponse.json(
        { success: false, error: finalResult.error },
        { status: 400 }
      )
    }

  } catch (error) {
    console.error('Error in update-customer API:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
