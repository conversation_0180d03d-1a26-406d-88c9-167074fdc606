# Benzochem Industries Admin Panel - Environment Variables Template
# Copy this file to .env.local and fill in your actual values
# NEVER commit .env.local to version control!

# =============================================================================
# CONVEX DATABASE CONFIGURATION
# =============================================================================
# Get these from your Convex dashboard: https://dashboard.convex.dev/
# Guide: https://docs.convex.dev/quickstart

# Deployment used by `npx convex dev`
CONVEX_DEPLOYMENT=dev:your-deployment-name-here

# Public Convex URL for client connections
NEXT_PUBLIC_CONVEX_URL=https://your-deployment.convex.cloud

# =============================================================================
# SESSION SECURITY CONFIGURATION
# =============================================================================
# IMPORTANT: Change these secrets in production!
# Use strong, random strings of at least 32 characters

# JWT signing secret (minimum 32 characters)
JWT_SECRET=your-jwt-secret-at-least-32-characters-long-change-in-production

# Session encryption secret (minimum 32 characters)
SESSION_SECRET=your-session-secret-at-least-32-characters-long-change-in-production

# =============================================================================
# SECURITY NOTES
# =============================================================================
# 1. Replace ALL placeholder values with your actual secrets
# 2. NEVER commit .env.local to version control
# 3. Use different secrets for development and production
# 4. Generate cryptographically secure random strings for secrets
# 5. Rotate secrets regularly for security
# 6. Keep secrets backed up securely (password manager, encrypted storage)
