"use client"

import { motion } from "framer-motion"
import Image from "next/image"
import Link from "next/link"
import { ArrowRight } from "lucide-react"

interface CategorySectionProps {
  title: string
  description: string
  image: string
  href: string
}

export default function CategorySection({ title, description, image, href }: CategorySectionProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, margin: "-100px" }}
      transition={{ duration: 0.6 }}
      className="group relative overflow-hidden rounded-lg"
    >
      <div className="relative aspect-[4/3] overflow-hidden rounded-lg">
        <Image
          src={image || "/placeholder.svg"}
          alt={title}
          fill
          className="object-cover transition-transform duration-500 group-hover:scale-105"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent" />
      </div>

      <div className="absolute bottom-0 left-0 right-0 p-6 text-white">
        <h3 className="text-2xl font-medium mb-2">{title}</h3>
        <p className="text-white/80 mb-4">{description}</p>
        <Link
          href={href}
          className="inline-flex items-center text-sm font-medium text-white hover:text-teal-300 transition-colors"
        >
          Explore Products
          <motion.span initial={{ x: 0 }} whileHover={{ x: 5 }} className="ml-2">
            <ArrowRight className="h-4 w-4" />
          </motion.span>
        </Link>
      </div>
    </motion.div>
  )
}
